# 已下载视频功能实现说明

## 功能概述
在事件详情页面添加"已下载视频"按钮，点击后跳转到视频列表页面，调用指定的API接口获取已下载的视频数据。

## 实现的功能

### 1. ✅ 事件详情页面按钮
- 在事件详情页面的顶部控制区域添加"已下载视频"按钮
- 按钮位置：在"点击更新数据"按钮后面
- 按钮样式：与其他控制按钮保持一致

### 2. ✅ 跳转功能
- 点击按钮跳转到视频列表页面
- 路径：`/main/eventAnalysis/eventVideoList`
- 传递参数：`eventId` 和 `eventName`
- 在新窗口中打开

### 3. ✅ API接口集成
- 接口地址：`/api/monitor/event/downloadedVideos`
- 请求方式：GET
- 传递参数：根据API文档要求传递所有必要参数

### 4. ✅ 视频列表页面改造
- 修改原有的视频列表页面，使其支持已下载视频查询
- 替换原有的API调用为新的已下载视频接口
- 保持原有的筛选和搜索功能

## 修改的文件

### 1. src/views/main/eventAnalysis/eventInfo/index.vue

#### 模板部分修改
```vue
<!-- 在顶部控制按钮区域添加 -->
<div @click="goToVideoList" class="item">
  <svg-icon icon-class="播放"></svg-icon>已下载视频
</div>
```

#### JavaScript部分修改
```javascript
methods: {
  // 跳转到视频列表页
  goToVideoList() {
    const { href } = this.$router.resolve({
      path: '/main/eventAnalysis/eventVideoList',
      query: {
        eventId: this.$route.query.eventId,
        eventName: this.eventInfo.eventName
      }
    });
    window.open(href, '_blank');
  }
}
```

### 2. src/views/main/eventAnalysis/eventVideoList/index.vue

#### API调用修改
```javascript
// 获取数据总数
getDataCount() {
  // ...
  // 获取已下载视频总数
  this.$http.get("/api/monitor/event/downloadedVideos", { params }).then((res) => {
    this.total = res.body.data ? res.body.data.length : 0;
  });
}

// 获取数据列表
getDataList() {
  this.loading = true;
  let params = this.getParams();
  this.listData = [];
  // 获取已下载视频列表
  this.$http.get("/api/monitor/event/downloadedVideos", { params }).then((res) => {
    if (res.body.data && res.body.data.length > 0) {
      this.listData = res.body.data;
    }
    this.loading = false;
  });
}
```

#### 参数传递修改
```javascript
getParams() {
  return {
    ...params,
    eventId: this.$route.query.eventId, // 添加事件ID参数
    pageNo: this.pageNo,
    pageSize: this.pageSize,
    keyword: this.keyword,
    websiteNames: this.websiteNames[0] === "全部" ? null : this.websiteNames.toString(),
  };
}
```

## API接口说明

### 接口地址
```
GET /api/monitor/event/downloadedVideos
```

### 请求参数
根据API文档，支持以下参数：
- `eventId`: 事件ID（必需）
- `pageNo`: 页码
- `pageSize`: 每页数量
- `dayNum`: 时间范围（全部-NULL，今日0，近一日98，近三日2，近一周6）
- `startTime`: 自定义开始时间
- `endTime`: 自定义结束时间
- `websiteNames`: 来源筛选
- `keyword`: 关键词搜索

### 返回数据格式
```json
{
  "status": 0,
  "data": [
    {
      // 视频数据对象
    }
  ],
  "message": "success"
}
```

## 路由配置

视频列表页面的路由已经存在：
```javascript
{
  path: "/main/eventAnalysis/eventVideoList",
  name: "事件视频列表",
  component: () => import("@/views/main/eventAnalysis/eventVideoList"),
}
```

## 功能特点

1. **无缝集成**: 与现有的事件详情页面完美集成
2. **参数传递**: 正确传递事件ID和事件名称
3. **新窗口打开**: 不影响当前页面的使用
4. **保持功能**: 视频列表页面的所有筛选和搜索功能都保持不变
5. **API替换**: 平滑替换为新的已下载视频接口

## 使用流程

1. 用户在事件详情页面点击"已下载视频"按钮
2. 系统在新窗口中打开视频列表页面
3. 页面自动传递当前事件的ID和名称
4. 视频列表页面调用已下载视频API获取数据
5. 用户可以使用筛选、搜索等功能查看已下载的视频

## 测试建议

1. **按钮功能测试**:
   - 测试按钮点击是否正确跳转
   - 验证新窗口是否正确打开
   - 确认参数是否正确传递

2. **API接口测试**:
   - 测试API接口是否正确调用
   - 验证参数传递是否完整
   - 确认返回数据格式是否正确

3. **页面功能测试**:
   - 测试筛选功能是否正常
   - 测试搜索功能是否正常
   - 测试分页功能是否正常

4. **数据显示测试**:
   - 测试视频列表是否正确显示
   - 测试总数统计是否准确
   - 测试空数据情况的处理

## 注意事项

1. 确保后端API接口 `/api/monitor/event/downloadedVideos` 已实现
2. 确保接口返回的数据格式与前端期望的格式一致
3. 确保事件ID参数正确传递给API接口
4. 视频列表组件需要能够正确处理新的数据格式

功能已完整实现，可以投入使用。只需要确保后端提供对应的API接口即可。
