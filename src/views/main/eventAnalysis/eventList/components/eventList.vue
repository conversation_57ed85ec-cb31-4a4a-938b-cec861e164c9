<template>
  <div class="eventList">
    <div class="header flex sb">
      <div class="Controls flex">
        <div>
          <Checkbox
            :indeterminate="indeterminate"
            :value="checkAll"
            @click.prevent.native="handleCheckAll"
            >全选
          </Checkbox>
        </div>
        <router-link
          :to="`/main/eventAnalysis/eventDetails?type=1&moduleName=${encodeURIComponent(
            $route.meta.moduleName
          )}`"
          target="_blank"
        >
          <div class="btn">
            <Icon type="md-add-circle" style="font-size: 20px;" />
            新建事件
          </div>
        </router-link>

        <div class="btn">
          <Icon type="ios-pause" style="font-size: 20px;" />
          批量暂停
        </div>
        <div class="btn" @click="del()">
          <svg-icon icon-class="榜单-批量删除" />
          批量删除
        </div>
        <!--<div class="btn" @click="eventCompare">
          <svg-icon icon-class="事件对比" />
          事件对比
        </div>-->
      </div>
      <div class="search flex">
        <div class="label">状态：</div>
        <Select
          v-model="status"
          style="width: 100px;"
          @on-change="getEventTotal"
        >
          <Option
            v-for="item in statusList"
            :value="item.value"
            :key="item.value"
            >{{ item.label }}
          </Option>
        </Select>
        <div class="label">关键词：</div>
        <Input
          v-model="keyword"
          placeholder="搜索"
          style="width: 150px;"
          search
          @on-search="getEventTotal"
        />
      </div>

      <div @click="changeScreen" class="flex" style="align-items: center;">
        <span class="yuans">
          <span class="yuans-one" v-show="screenFlag"></span>
        </span>
        仅查看上屏事件
      </div>
    </div>

    <div class="listBox">
      <div class="header flex">
        <div class="item" style="width: 100px;">序号</div>
        <div class="item" style="width: 460px;">标题</div>
        <div class="item" style="width: 160px;">信息总量</div>
        <div class="item" style="width: 180px;">创建人员</div>
        <div class="item" style="width: 230px;">创建时间</div>
        <div class="item" style="width: 200px;">操作</div>
      </div>
      <div class="content">
        <Spin v-show="loading" fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <no-data v-show="total === 0 && !loading" />
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
          <div
            class="row flex"
            v-for="(item, index) in eventList"
            :key="index + 1"
          >
            <div class="item" style="width: 100px;">
              <Checkbox :label="item.eventId"
                >{{ (pageNo - 1) * pageSize + index + 1 }}
              </Checkbox>
            </div>
            <div
              class="item tipses cp"
              style="width: 445px;"
              @click="toInfo(item)"
            >
              <svg-icon
                v-if="item.isTop == 1"
                icon-class="置顶1"
                class="top-icon-list"
                title="已置顶"
              />
              <span class="tit">{{ item.eventName }}</span>
              <svg-icon
                v-if="item.isAlert == 1"
                icon-class="star"
                class="star-important-list"
                title="重点事件"
              />
              <span class="ispush" v-if="item.pushStatus == 1">已推送</span>
            </div>
            <div class="item" style="width: 150px;">
              {{ item.total }}
              <!--<div style="display: inline-block">
                <i class="icon hot_12 orange"></i>
              </div>
              <div style="display: inline-block">
                <i class="icon hot_3 up"></i>
              </div>
              <div style="display: inline-block">
                <i class="icon hot_1 up"></i>
              </div>-->
            </div>
            <div class="item" style="width: 170px;">
              {{ item.createAccount }}
            </div>
            <div class="item" style="width: 220px;">
              {{ moment(item.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </div>
            <div class="item" style="width: 220px; display: flex;">
              <svg-icon
                :title="item.isPush == 1 ? '已开始定时推送至“山东通”' : ''"
                :icon-class="item.isPush == 1 ? '定时消息(红)' : '定时消息(蓝)'"
              />
              <span title="导出报告" @click="showModal(item)">
                <svg-icon
                  icon-class="data-import"
                  style="color: blue;"
                  title="导出报告"
                />
              </span>
              <Poptip
                transfer
                confirm
                :title="
                  item.eventStatus == 1
                    ? '是否暂停该事件！'
                    : '是否重新启用该事件！'
                "
                @on-ok="batchPause(item, item.eventStatus == 1 ? 2 : 1)"
              >
                <span title="暂停事件" v-if="item.eventStatus == 1">
                  <svg-icon icon-class="列表-暂停" />
                </span>
                <span title="开启事件" v-else>
                  <svg-icon icon-class="列表-已暂停" />
                </span>
              </Poptip>
              <router-link
                :to="
                  '/main/eventAnalysis/eventDetails?type=1&eventId=' +
                  item.eventId +
                  '&moduleName=' +
                  encodeURIComponent($route.meta.moduleName)
                "
                target="_blank"
                title="编辑事件"
              >
                <svg-icon icon-class="列表-编辑" />
              </router-link>
              <Poptip
                transfer
                confirm
                title="
                  是否删除该事件！
                "
                @on-ok="del(item.eventId)"
              >
                <span title="删除事件">
                  <svg-icon icon-class="列表-删除" />
                </span>
              </Poptip>
              <!-- <svg-icon icon-class="事件分析-上屏" /> -->
              <Poptip
                transfer
                confirm
                :title="
                  item.isScreen == 1
                    ? '是否取消“' + item.eventName + '”事件推送?'
                    : '是否将“' +
                      item.eventName +
                      '”事件推送至舆情事件研判分析大屏?'
                "
                @on-ok="handleScreen(item)"
              >
                <span v-if="item.isScreen == 1" title="撤销推送">
                  <svg-icon icon-class="事件分析-下屏" />
                </span>
                <span v-else title="推送至大屏">
                  <svg-icon icon-class="事件分析-上屏" />
                </span>
              </Poptip>
              <Poptip
                transfer
                confirm
                :title="
                  item.isTop == 1
                    ? '点击取消置顶'
                    : '置顶'
                "
                @on-ok="handleTop(item)"
              >
                <span v-if="item.isTop == 1" title="点击取消置顶">
                  <svg-icon icon-class="置顶1" />
                </span>
                <span v-else title="置顶">
                  <svg-icon icon-class="置顶2" />
                </span>
              </Poptip>
            </div>
          </div>
        </CheckboxGroup>
        <Page
          v-show="total > 0"
          :total="total"
          :current="pageNo"
          :page-size="pageSize"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
          show-total
          show-elevator
          show-sizer
        />
      </div>
      <div style="color: #c0c0c0; padding-left: 30px; margin-top: 10px;">
        * 事件监测时间超过一个月后将自动暂停，如需持续监测请手动开启
      </div>
    </div>

    <Modal v-model="modalFlag" width="730" :closable="false">
      <p
        slot="header"
        style="height: 80px; line-height: 80px; text-align: center;"
      >
        <modalHeader :title="modalTitle" />
      </p>
      <!-- 事件分析报告下载 -->
      <div class="eventTime">
        <span class="label">报告统计时间：</span>
        <div>
          <DatePicker
            ref="datePicker"
            placeholder="事件起止时间"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            :options="options"
            :start-date="
              new Date(
                moment(eventItem.monitorStartTime).format('YYYY-MM-DD HH:mm:ss')
              )
            "
            style="width: 250px;"
            @on-open-change="onOpenChange"
            @on-change="dateChange"
          />
        </div>
      </div>
      <div slot="footer">
        <div class="foot">
          <span class="inBlock foots" @click="download()">{{ "确定" }}</span>
          <span
            class="inBlock foots"
            style="margin-left: 70px; background: #999999;"
            @click="
              modalFlag = false;
              modalTitle = '';
            "
            >{{ "关闭" }}</span
          >
        </div>
      </div>
    </Modal>

    <div class="tend-chart-box" ref="tendChart" style="display: none;"></div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
import modalHeader from "../../../../main/publicOpinionTips/components/modalHeader.vue";
import { exportWord, getBase64Sync } from "@/util/outword.js";
import situations from "@/assets/json/situations.json";
import echarts from "echarts";

export default {
  data() {
    // 这里存放数据
    return {
      indeterminate: false,
      checkAll: false,
      checkAllGroup: [],
      eventList: [],
      modalTitle: "",
      modalFlag: false,
      startTime: "",
      endTime: "",
      eventItem: {},
      dataDesc: [],
      siutionNum: [],
      maxNum: 0,
      maxSiution: "",
      total: 0,
      pageNo: 1,
      pageSize: 20,
      keyword: "",
      status: "0",
      screenFlag: false,
      loading: false,
      options: null,
      statusList: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "1",
          label: "运行中",
        },
        {
          value: "2",
          label: "暂停",
        },
      ],
      colorData: [
        "#1890FF",
        "#4ECB73",
        "#9CCFFF",
        "#435188",
        "#FBD337",
        "#E3E7E9",
        "#B3B8C4",
        "#7DD2CA",
        "#267F41",
        "#FF624E",
        "#FFA64E",
      ],
      resultData: {
        total: [],
        xAxis: [],
      },
      myChart: null,
      dayImageUrl: "",
      hourImageUrl: "",
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    modalHeader,
  },
  props: {
    eventId: {
      default: null,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    del(id) {
      let params = {};
      if (id) {
        params.eventIds = id;
      } else {
        if (this.checkAllGroup.length === 0) {
          this.$Message.warning("请选择信息后重试！");
          return false;
        }
        params.eventIds = this.checkAllGroup.toString();
      }

      this.$http.get("/monitor/event/batchDelete", { params }).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success(res.body.message);
          this.getEventList();
        }
      });
    },
    batchPause(item, eventStatus) {
      let params = {
        eventStatus: eventStatus,
      };
      if (item) {
        params.eventIds = item.eventId;
      } else {
        if (this.checkAllGroup.length === 0) {
          this.$Message.warning("请选择信息后重试！");
          return false;
        }
        params.eventIds = this.checkAllGroup.toString();
      }

      this.$http.get("/monitor/event/batchPause", { params }).then((res) => {
        if (res.body.status === 0) {
          this.$set(item, "eventStatus", eventStatus);
          this.$Message.success("操作事件成功");
        }
      });
    },
    changeScreen() {
      this.screenFlag = !this.screenFlag;
      this.pageNo = 1;
      this.getEventList();
      this.getEventTotal();
    },
    handleScreen(data) {
      let url = "/monitor/event/screentEvent";
      let params = {
        isScreen: data.isScreen == 1 ? -1 : 1,
        eventId: data.eventId,
      };
      this.$http.post(url, params, { emulateJSON: true }).then((res) => {
        if (res.body.status === 0) {
          this.getEventList();
        } else {
        }
      });
    },
    handleTop(data) {
      let url = "/monitor/event/setTop";
      let params = {
        eventId: data.eventId,
        type: data.isTop == 1 ? 0 : 1, // 1表示置顶，0表示取消置顶
      };
      this.$http.get(url, { params }).then((res) => {
        if (res.body.status === 0) {
          const message = data.isTop == 1 ? "事件已取消置顶" : "事件置顶成功";
          this.$Message.success(message);
          this.getEventList();
        } else {
          this.$Message.error(res.body.message || "操作失败");
        }
      });
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getEventList();
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getEventList();
    },
    editEvent(data) {
      const routeUrl = this.$router.resolve({
        path: "/main/eventAnalysis/eventCompare",
        params: {
          keyWord: "asd",
        },
      });
      window.open(routeUrl.href, "_blank");
    },
    //获取事件总数
    getEventTotal() {
      this.loading = true;
      this.total = 0;
      this.pageNo = 1;
      this.eventList = [];
      let params = {
        keyword: this.keyword,
        status: this.status,
        type: this.eventId,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
      };
      if (this.screenFlag) {
        params.isScreen = 1;
      }
      this.$http.get("/monitor/event/count", { params }).then((res) => {
        console.log(res);
        if (res.body.status === 0 && res.body.data > 0) {
          this.total = res.body.data;
        }
        if (this.total > 0) {
          this.getEventList();
        } else {
          this.loading = false;
        }
      });
    },
    // 获取事件列表
    getEventList() {
      this.loading = true;
      let params = {
        keyword: this.keyword,
        status: this.status,
        type: this.eventId,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
      };
      if (this.screenFlag) {
        params.isScreen = 1;
      }
      this.$http.get("/monitor/event/list", { params }).then((res) => {
        if (res.body.status === 0) {
          this.eventList = res.body.data;
        }
        this.loading = false;
      });
    },
    // 事件对比
    eventCompare() {
      const routeUrl = this.$router.resolve({
        path: "/main/eventAnalysis/eventCompare",
        params: {
          keyWord: "asd",
        },
      });
      window.open(routeUrl.href, "_blank");
    },
    // 查看详情页
    toInfo(d) {
      const routeUrl = this.$router.resolve({
        path: "/main/eventAnalysis/eventInfo",
        query: {
          eventId: d.eventId,
        },
      });
      window.open(routeUrl.href, "_blank");
    },
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.eventList.map((i) => i.eventId);
      } else {
        this.checkAllGroup = [];
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.eventList.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },

    // 导出事件报告
    showModal(item) {
      this.modalTitle = "事件分析报告下载";
      this.modalFlag = true;
      this.eventItem = item;
      console.log(item);
      console.log(this.$refs.tendChart);
      this.$refs.datePicker.handleClear();
    },

    getSituation(type) {
      let str = situations[type];
      return str;
    },

    // 导出事件报告
    download() {
      if (this.startTime != "" && this.endTime != "") {
        this.modalTitle = "";
        this.modalFlag = false;
        this.getDataDesc();
      } else {
        this.$Message.error({
          content: "请选择起止时间！",
          duration: 1,
        });
      }
    },
    dateChange(d) {
      this.startTime = d[0];
      this.endTime = d[1];
    },

    // 获取事件数据并导出word
    getDataDesc() {
      let params = {
        eventId: this.eventItem.eventId,
        sentiment: null,
        startTime: this.startTime,
        stopTime: this.endTime,
      };
      this.$http.get("/monitor/event/msgNumsDesc", { params }).then((res) => {
        this.dataDesc = res.body.data;
        this.siutionNum = [];

        let firstSituation = situations[this.eventItem.firstSituation];
        let firstAccount = this.eventItem.firstAccount;
        if (!firstAccount) {
          firstAccount = situations[this.eventItem.firstSituation] + "用户";
        }
        let firstTime = this.eventItem.firstTime;
        if (firstTime) {
          firstTime = moment(firstTime).format("yyyy-MM-DD HH:mm:ss");
        } else {
          firstTime = "-";
        }
        let firstTitle = this.eventItem.firstTitle;
        if (!firstTitle) {
          firstTitle = "-";
        }

        for (const key in this.dataDesc.siution) {
          let value = this.dataDesc.siution[key];
          const arrt = value.split("_");
          const siution = arrt[0];
          const num = arrt[1];
          let mo = {
            siution: siution,
            num: num,
          };
          this.siutionNum.push(mo);
        }
        //最多数据量平台获取
        let map = this.dataDesc.maxNum;
        for (const key in map) {
          this.maxNum = map[key];
          this.maxSiution = key;
        }

        // 获取传播趋势小时数据以及日数据并导出图片路径
        Promise.all([this.getSpreadTends("1"), this.getSpreadTends("2")])
          .then((res) => {
            // 文档导出参数
            let data = {
              firstSituation: firstSituation,
              firstAccount: firstAccount,
              firstTime: firstTime,
              firstTitle: firstTitle,
              eventName: this.eventItem.eventName,
              eventDescription: this.eventItem.eventDescription
                ? this.eventItem.eventDescription
                : "",
              startTime: this.startTime,
              endTime: this.endTime,
              total: this.dataDesc.total,
              maxSiution: this.getSituation(this.maxSiution),
              maxNum: this.maxNum,
              siutionNum: this.getSiutionList(),
              nePer: this.dataDesc.nePer,
              poPer: this.dataDesc.poPer + this.dataDesc.neutPer,
              hourImageUrl: this.hourImageUrl,
              dayImageUrl: this.dayImageUrl,
            };

            let imgSize = {
              hourImageUrl: [600, 300], //控制导出的word图片大小
              dayImageUrl: [600, 300],
            };

            exportWord(
              data,
              `${this.eventItem.eventName}-事件报告.docx`,
              imgSize
            );
          })
          .catch((error) => {
            console.error(error);
          });
      });
      this.getLog("事件分析/事件列表", '导出报告/'+this.eventItem.eventName);
    },
    setCharts(type) {
      let echertEl = document.querySelector(".tend-chart-box");
      echertEl.style.width = window.innerWidth + "px";
      echertEl.style.height = "388px";
      let myChart = echarts.init(this.$refs.tendChart);
      myChart.clear();
      let data = [
        {
          name: "总量",
          type: "line",
          smooth: true,
          symbol: "none",
          lineStyle: {
            width: 1,
          },
          yAxisIndex: 1,
          data: this.resultData[1000],
        },
      ];
      //  let legendRight = ["总量"];
      let option = {
        color: this.colorData,
        animation: false,
        grid: {
          left: "4%",
          right: "4%",
          top: "30%",
          bottom: 35,
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          confine: true,
          formatter: (params) => {
            let str = "";
            params.forEach((item) => {
              str =
                str +
                item.marker +
                item.seriesName +
                "：" +
                item.value +
                "<br />";
            });
            return this.formatTooltip(params[0].axisValue) + "<br />" + str;
          },
        },
        // legend: [
        //   {
        //     right: 20,
        //     top: 10,
        //     icon: "rect1",
        //     orient: "vertical", //垂直显示
        //     itemWidth: 12,
        //     itemHeight: 4,
        //     itemGap: 10,
        //     textStyle: {
        //       color: "rgba(0,0,0,.85)",
        //       fontSize: 13,
        //       lineHeight: 18,
        //     },
        //     data: legendRight,
        //   },
        // ],
        xAxis: {
          type: "category",
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#E3E8EC",
            },
          },
          axisTick: {
            show: true,
            alignWithLabel: true,
            lineStyle: {
              color: "#E3E8EC",
            },
          },
          axisLabel: {
            show: true,
            color: "rgba(0,0,0,.55)",
            lineHeight: 17,
            formatter: function (value) {
              if (moment(value).hour() === 0) {
                return echarts.format.formatTime("MM-dd", value);
              } else {
                return echarts.format.formatTime("MM/dd hh:mm", value);
              }
            },
          },
          splitLine: {
            show: false,
          },
          data: this.resultData.xAxis,
        },
        yAxis: [
          {
            type: "value",
            minInterval: 1,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              color: "rgba(0,0,0,.55)",
              lineHeight: 17,
              formatter: (value, index) => {
                if (value < 1000) {
                  return value;
                }
                if (value >= 1000 && value < 10000) {
                  return value / 1000 + "k";
                }
                if (value >= 10000) {
                  return value / 10000 + "w";
                }
                return value;
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#E3E8EC",
              },
            },
          },
          {
            type: "value",
            minInterval: 1,
            position: "left",
            axisLabel: {
              show: true,
              color: "rgba(0,0,0,.55)",
              lineHeight: 17,
              formatter: (value, index) => {
                if (value < 1000) {
                  return value;
                }
                if (value >= 1000 && value < 10000) {
                  return value / 1000 + "k";
                }
                if (value >= 10000) {
                  return value / 10000 + "w";
                }
                console.log(value, index);
                return value;
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#E3E8EC",
              },
            },
          },
        ],

        series: data,
      };
      myChart.setOption(option);
      setTimeout(() => {
        myChart.resize();
      }, 0);

      if (type === "1") {
        this.dayImageUrl = myChart.getDataURL({
          pixelRatio: 2,
          backgroundColor: "#fff",
        });
      } else if (type === "2") {
        this.hourImageUrl = myChart.getDataURL({
          pixelRatio: 2,
          backgroundColor: "#fff",
        });
      }
    },

    // 1: 按天统计 2: 按小时统计
    getSpreadTends(type) {
      let params = {
        eventId: this.eventItem.eventId,
        type: type,
        startTime: this.startTime,
        stopTime: this.endTime,
      };
      return this.$http
        .get("/monitor/event/spreadTends", { params })
        .then((res) => {
          let data = res.body.data;
          this.resultData = { ...data.dataY };
          const date1 = moment(data.dataX[0]);
          const date2 = moment(data.dataX[data.dataX.length - 1]);
          // 计算时间差（以分钟为单位）
          const diffInMinutes = date2.diff(date1, "minutes");
          // 判断是否小于 48 小时（2880 分钟）
          const format = type == 2 ? "yyyy-MM-DD HH:mm:ss" : "yyyy-MM-DD";
          this.resultData.xAxis = data.dataX.map((i) =>
            moment(i).format(format)
          );
          this.setCharts(type);
        });
    },

    getSiutionList() {
      let stats = [];
      for (const stat of this.siutionNum) {
        let item = {
          siution: this.getSituation(stat.siution),
          num: stat.num,
        };

        stats.push(item);
      }

      return stats;
    },

    onOpenChange() {
      this.options = {
        disabledDate: (date) => {
          // 设置不可选的开始日期
          let start = this.eventItem.monitorStartTime;
          // 设置不可选的结束日期
          let end =
            this.eventItem.monitorStopTime - Date.now() < 0
              ? this.eventItem.monitorStopTime
              : Date.now();
          // 返回布尔值，表示该日期是否不可选
          return date && (date.getTime() < start || date.getTime() > end);
        },
      };
      return true;
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    moment() {
      return moment;
    },
  },
  // 监控 data 中的数据变化
  watch: {
    eventId: {
      handler(val) {
        this.getEventTotal();
      },
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getEventTotal();
    // 接收通道
    const channel = new BroadcastChannel("event_channel");
    let that = this;
    channel.onmessage = function (event) {
      console.log("Received message:", event.data);
      // that.changeStatus(event.data);
      that.getEventTotal();
    };
  },
};
</script>
<style scoped lang="less">
.eventList {
  margin-right: 20px;
  width: 1370px;
  background: #ffffff;
  border: 1px solid;
  border-color: rgba(255, 255, 255, 0.29);
  border-radius: 8px;
  padding: 0 20px;

  & > .header {
    padding: 20px 0;
    border-bottom: 1px solid #e8e8e8;
  }

  .search {
    .label {
      display: inline-block;
      line-height: 30px;
      margin-left: 20px;
    }

    /deep/ .ivu-icon-ios-search {
      background-color: rgba(255, 255, 255, 0);
    }
  }

  .Controls {
    .btn {
      cursor: pointer;
      color: #ffffff;
      font-size: 16px;
      height: 30px;
      background: #5585ec;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 20px;
      margin: 0 5px;
    }
  }

  .listBox {
    .header {
      line-height: 70px;
      width: 1327.6px;
      height: 70px;
      padding: 0 30px;
      font-weight: 600;
      color: #333333;
      font-size: 16px;
    }
    .tipses{
      display: flex;
      align-items: center;
      overflow: hidden;
      padding-right: 10px;
      .tit{
        // flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .top-icon-list {
        flex-shrink: 0;
        margin-right: 8px;
        color: #F84F77;
        font-size: 16px;
      }

      .star-important-list {
        flex-shrink: 0;
        margin-left: 8px;
        color: #F79831;
        font-size: 16px;
      }

      .ispush{
        flex-shrink: 0;
        margin-left: 10px;
        background-color: #ff8000;
        color: #fff;
        padding: 0px 5px;
        border-radius: 4px;
      }
    
    }
  }

  .content {
    height: calc(~"100vh - 260px");
    overflow-y: auto;
    position: relative;

    .row {
      padding-left: 30px;
      height: 64px;
      align-items: center;

      &:nth-child(2n-1) {
        background-color: #f6f7fc;
      }

      &:nth-child(2n) {
        background-color: #fafafa;
      }

      .svg-icon {
        height: 20px;
        width: 20px;
        margin-right: 5px;
        cursor: pointer;

        &:nth-child(1) {
          height: 22px;
          width: 22px;
          margin-right: 8px;
        }
      }
    }
  }

  i.icon {
    display: inline-block;
    width: 16px;
    height: 20px;
    background-repeat: no-repeat;
    position: relative;
    top: 8px;

    &.hot_12 {
      top: 5px;
      height: 22px;
      margin: 0px 1px;

      &.orange {
        background-image: url(../../../../../assets/img/event/12h_orange.png);
      }
    }

    &.hot_3 {
      top: 5px;
      margin: 0px 1px;

      &.up {
        background: url(../../../../../assets/img/event/3h_up.png);
      }
    }

    &.hot_1 {
      top: 5px;
      margin: 0px 1px;

      &.up {
        background: url(../../../../../assets/img/event/1h_up.png);
      }
    }
  }
}

.yuans {
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #707070;
  display: inline-block;
  border-radius: 100%;
  cursor: pointer;
  margin-right: 4px;

  .yuans-one {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #5585ec;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 100%;
  }
}

.eventTime {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .label {
    font-size: 18px;
    margin-right: 10px;
  }
}

.foot {
  text-align: center;
  margin-bottom: 18px;
  margin-top: 20px;

  .foots {
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
  }
}

.inBlock {
  display: inline-block;
}
</style>
