<template>
  <div class="listItem flex sb">
    <img
      class="CornerMark"
      :src="
        require('/src/assets/img/' +
          (svgArr.includes(info.mwebsiteName) ? info.mwebsiteName : '视频') +
          '.png')
      "
      alt=""
    />
    <VideoPlayer :options="videoOptions" :info="info" :showTip="true" class="video" />
    <div class="title ellipsis-2 cp" @click="toDetails">
      <span
        v-html="
          highlightTool.highlightByHitWords(
            removeROrN(info.mtitle),
            [keyword],
            'highlight0'
          )
        "
      ></span>
    </div>
    <div class="remark flex sb">
      <div class="name ellipsis" :title="info.uname">{{ info.uname }}</div>
      <div class="time">{{ info.mpublishTime }}</div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import VideoPlayer from "@/components/videoPlayerNew";
import highlightTool from "trs-tool-highlight";

export default {
  data() {
    // 这里存放数据
    return {
      videoOptions: {},
      svgArr: ["抖音", "快手", "西瓜视频", "今日头条"],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { VideoPlayer },
  props: {
    info: {
      default: {},
    },
    keyword: {
      default: "",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    console.log(this.videoBaseUrl + this.info.videoFile);
    this.videoOptions = {
      autoplay: false,
      controls: true,
      sources: [
        {
          src: this.videoBaseUrl + this.info.videoFile,
          type: "video/mp4",
        },
      ],
    };
  },
  // 方法集合
  methods: {
    toDetails() {
      console.log(this.info);
      console.log(this.$route);
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: this.info.mkey,
          keyword: this.keyword,
          situation: this.info.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
          moduleOriginName: this.$route.meta && this.$route.meta.moduleName ? encodeURIComponent(this.$route.meta.moduleName) : '',
          moduleName: this.$route.meta && this.$route.meta.moduleName ? encodeURIComponent(this.$route.meta.moduleName.split('/')[0] + '/正文页') : ''
        },
      });
      window.open(href, "_blank");
    },
    removeROrN(str) {
      if (str) {
        return str
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    highlightTool() {
      return highlightTool;
    },
  },
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
.listItem {
  width: 320px;
  height: 400px;
  border: 1px solid #eee;
  border-radius: 5px;
  margin-bottom: 10px;
  flex-direction: column;
  position: relative;

  .CornerMark {
    position: absolute;
    z-index: 2;
    width: 25px;
    height: 25px;
    background-color: #fff;
    border-radius: 3px;
  }

  .video {
    width: 100%;
    height: 300px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    overflow: hidden;
  }

  .title {
    padding: 0 10px;
    font-size: 16px;
  }

  .remark {
    padding: 0 10px;
    color: #999;
    font-size: 14px;
    .name{
      width: 120px;
    }
  }
}
</style>
