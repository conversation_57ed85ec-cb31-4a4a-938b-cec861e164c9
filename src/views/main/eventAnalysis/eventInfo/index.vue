<template>
  <div>
    <!-- <FrameName /> -->
    <div class="eventInfo flex sb">
      <div class="analyze flex sb">
        <div
          style="
            
            

          "
          class="atitle"
        >
          <div class="nn">
            {{ eventInfo.eventName }}
          </div>
          <!-- 重点提醒星星按钮 -->
          <div
            class="star-important"
            @click="toggleImportant"
            :title="isImportant ? '取消重点提醒' : '设为重点提醒'"
          >
            <svg-icon
              :icon-class="isImportant ? 'star' : '星星'"
              class="star-icon"
            />
          </div>
          <div class="topControls flex">
            <!-- 上传雪亮视频按钮 -->
            <Upload
              :before-upload="handleVideoUpload"
              action=""
              accept="video/*"
              :show-upload-list="false"
            >
              <div class="item">
                <svg-icon icon-class="播放"></svg-icon>上传雪亮视频
              </div>
            </Upload>

            <div class="item">
              <Poptip
                transfer
                confirm
                title="
                 确认将此事件推送到山东通群吗？
                "
                @on-ok="handleTs"
              >
                <svg-icon icon-class="推送山东通"></svg-icon>推送到山东通
              </Poptip>
            </div>

            <div @click="batchDetection" class="item">
              <svg-icon icon-class="探测 -蓝"></svg-icon>启动信息探测
            </div>
            <div @click="updateEvent()" class="item">
              <svg-icon icon-class="更新"></svg-icon>点击更新数据
            </div>
          </div>
        </div>

        <OuterFrame title="事件描述" style="width: 100%;" :loading="loading">
          <EventDescription
            @saveEdit="saveEdit"
            :eventInfo="eventInfo"
            v-if="eventInfo.eventId"
          />
        </OuterFrame>

        
        <OuterFrame :loading="loading" title="首发情况" style="width: 100%;">
          <InitialSituation
            @saveEdit="saveEdit"
            :eventInfo="eventInfo"
            v-if="eventInfo.eventId"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="数据概览" style="width: 100%;">
          <DataOverview
            :eventInfo="eventInfo"
            @openInfoList="openInfoList"
            v-if="eventInfo.eventId"
            :key="key"
            :showBatchDetection.sync="showBatchDetection"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="传播趋势" style="width: 100%;">
          <PropagationTend
            v-if="eventInfo.eventId"
            :key="key"
            :eventInfo="eventInfo"
            @openInfoList="openInfoList"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="热榜分析" style="width: 100%;">
          <HotListAnalyze
            v-if="eventInfo.eventId"
            :key="key"
            :eventInfo="eventInfo"
            @openInfoList="openInfoList"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="最热信息" style="width: 49%;">
          <latestHotNews
            v-if="eventInfo.eventId"
            :eventInfo="eventInfo"
            :key="key"
          />
        </OuterFrame>
        <OuterFrame :loading="loading" title="最新信息" style="width: 50%;">
          <LatestNews
            v-if="eventInfo.eventId"
            :eventInfo="eventInfo"
            :key="key"
          />
        </OuterFrame>
        <OuterFrame
          :loading="loading"
          title="情感倾向分析"
          style="width: 49%;"
        >
          <AffectiveTendency v-if="eventInfo.eventId" />
        </OuterFrame>
        <OuterFrame :loading="loading" title="热点词云" style="width: 50%;">
          <HotWord v-if="eventInfo.eventId" @openInfoList="openInfoList" />
        </OuterFrame>

        <OuterFrame
          :loading="loading"
          title="活跃信源分析"
          style="width: 49%;"
        >
          <SourceAnalysis
            v-if="eventInfo.eventId"
            @openInfoList="openInfoList"
          />
        </OuterFrame>
        <OuterFrame
          :loading="loading"
          title="转载量TOP10"
          style="width: 50%;"
        >
          <TopTen v-if="eventInfo.eventId" :eventInfo="eventInfo" ref="echartTop" @openInfoList="openInfoList" />
        </OuterFrame>


        <OuterFrame title="观点分析" style="width: 100%;">
          <ViewpointAnalysis v-if="eventInfo.eventId" />
        </OuterFrame>
        <OuterFrame title="地域分布" style="width: 100%;">
          <RegionalDistribution v-if="eventInfo.eventId" />
        </OuterFrame>
        <!-- 雪亮视频播放区域 -->
        <OuterFrame
          title="雪亮视频"
          style="width: 100%;"
          :loading="loading"
          v-if="eventInfo.vedioFiles && eventInfo.vedioFiles.length > 0"
        >
          <div class="video-container">
            <div
              v-for="video in eventInfo.vedioFiles"
              :key="video.fileId"
              class="video-item"
            >
              <VideoPlayerNew
                :info="getVideoInfo(video)"
                :options="getVideoOptions(video)"
                :showTip="false"
                class="video-player"
              />
              <!-- <div class="video-info">
                <span class="video-name">{{ video.fileName }}</span>
              </div> -->
              <div class="alink" @click="delVideo(video)">删除视频</div>
            </div>
          </div>
        </OuterFrame>
      </div>
      <div class="promptSheet">
        <div class="tabs">
          <div
            :class="['item', 'cp', tabId === '1' ? 'active' : '']"
            @click="tabId = '1'"
          >
            事件脉络
          </div>
          <div
            :class="['item', 'cp', tabId === '2' ? 'active' : '']"
            @click="tabId = '2'"
          >
            关联提示单
          </div>
        </div>
        <EventVein v-show="tabId === '1'" :eventInfo="eventInfo" />
        <PromptSheet
          v-show="tabId === '2'"
          :data="eventInfo.promptMsg"
          :loading="loading"
        />
      </div>
      <InfoList
        :switchStatus="switchStatus"
        :params="params"
        @on-cancel="switchStatus = false"
        @updateEvent="updateEvent"
        :eventId="$route.query.eventId"
      />
    </div>

    <!-- 视频替换确认弹窗 -->
    <Modal
      v-model="showVideoReplaceModal"
      title="提示"
      :mask-closable="false"
      width="400"
    >
      <div style="text-align: center; padding: 20px 0;">
        已有雪亮视频，是否替换当前视频？
      </div>
      <div slot="footer">
        <Button type="primary" size="large" @click="confirmReplaceVideo(true)">确定</Button>
        <Button @click="confirmReplaceVideo(false)" size="large" style="margin-right: 8px;">不替换</Button>
      </div>
    </Modal>

    <!-- 多视频选择替换弹窗 -->
    <Modal
      v-model="showVideoSelectModal"
      title="提示"
      :mask-closable="false"
      width="500"
    >
      <div style="text-align: center; padding: 20px 0;">
        <p style="margin-bottom: 20px; font-size:14px" >已有雪亮视频如需替换，请选择需要替换的视频。</p>
        <div class="abc">
          <RadioGroup v-model="selectedVideoIndex">
            <div
              v-for="(video, index) in eventInfo.vedioFiles"
              :key="video.fileId"
              style="margin-bottom: 10px;"
            >
              <Radio :label="index" :title="video.fileName">{{ video.fileName }}</Radio>
            </div>
          </RadioGroup>
        </div>
      </div>
      <div slot="footer">
        <Button @click="confirmReplaceVideo(false)" size="large" style="margin-right: 8px;">不替换</Button>
        <Button @click="addNewVideo" size="large" style="margin-right: 8px;">新增视频</Button>
        <Button type="primary" size="large" @click="confirmSelectReplace">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import OuterFrame from "./components/outerFrame.vue"; //外框
import EventDescription from "./components/eventDescription.vue"; //事件描述
import InitialSituation from "./components/InitialSituation";
import DataOverview from "./components/dataOverview.vue"; //数据概览
import PropagationTend from "./components/propagationTend.vue"; //传播趋势
import HotListAnalyze from "./components/hotListAnalyze.vue"; //热榜分析
import HotWord from "./components/hotWord.vue"; //热点词云
import LatestNews from "./components/latestNews"; //最新信息
import SourceAnalysis from "./components/sourceAnalysis"; //活跃信源分析
import AffectiveTendency from "./components/affectiveTendency"; //情感倾向
import RegionalDistribution from "./components/regionalDistribution"; //地域分布
import ViewpointAnalysis from "./components/viewpointAnalysis"; //观点分析
import PromptSheet from "./components/promptSheet.vue"; //关联提示单
import EventVein from "./components/EventVein.vue"; //事件脉络
import InfoList from "./components/infoList.vue"; //右侧滑出监测列表
import moment from "moment/moment"; //左侧浮动信息列表
import TopTen from "./components/topTenNew.vue"; //活跃信源分析
import latestHotNews from "./components/latestHotNews.vue"; //最热信息
import VideoPlayerNew from "@/components/videoPlayerNew"; //视频播放器

export default {
  data() {
    // 这里存放数据
    return {
      eventInfo: {},
      loading: false,
      switchStatus: false,
      params: {},
      key: 1,
      tabId: "1",
      showBatchDetection: false,
      isImportant: false, //是否重点提醒
      // 视频相关
      showVideoReplaceModal: false, // 视频替换确认弹窗
      showVideoSelectModal: false, // 多视频选择替换弹窗
      selectedVideoIndex: -1, // 选中要替换的视频索引
      uploadingVideo: null, // 正在上传的视频文件
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    OuterFrame,
    EventDescription,
    InitialSituation,
    DataOverview,
    PropagationTend,
    HotListAnalyze,
    HotWord,
    LatestNews,
    SourceAnalysis,
    AffectiveTendency,
    RegionalDistribution,
    ViewpointAnalysis,
    PromptSheet,
    EventVein,
    InfoList,
    TopTen,
    latestHotNews,
    VideoPlayerNew,
  },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    openInfoList(obj) {
      let nowStartTime = obj.startTime1
        ? obj.startTime1
        : this.eventInfo.monitorStartTime;
      let nowStopTime = obj.stopTime1
        ? obj.stopTime1
        : this.eventInfo.monitorStopTime;
      this.params = {
        keyword: this.eventInfo.expKeywords,
        startTime: nowStartTime,
        endTime: moment(nowStopTime).isAfter(moment())
          ? moment().valueOf()
          : nowStopTime,
        ...obj,
      };
      this.switchStatus = true;
    },
    saveEdit(obj) {
      let params = {
        ...this.eventInfo,
        ...obj,
      };
      this.$http.post("/monitor/event/editEvent", params).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success("修改成功");
          this.getEventInfo();
        }
      });
    },
    getEventInfo() {
      return new Promise((resolve) => {
        this.loading = true;
        let params = {
          eventId: this.$route.query.eventId,
        };
        this.$http.get("/monitor/event/detail", { params }).then((res) => {
          if (res.body.status !== 0) {
            return false;
          }
          this.eventInfo = res.body.data;
          // 设置重点提醒状态
          this.isImportant = this.eventInfo.isAlert === 1;
          document.title = "事件-" + this.eventInfo.eventName;
          resolve(this.eventInfo.eventName);
          this.loading = false;
        });
      });
    },
    updateEvent() {
      this.loading = true;
      let params = {
        eventIds: this.$route.query.eventId,
      };
      this.$http
        .get("/monitor/event/updateEventTimer", { params })
        .then((res) => {
          if (res.body.status !== 0) {
            return false;
          }
          this.loading = false;
          this.key++;
          this.$Message.success("更新数据成功");
        });
    },
    // 批量探测
    batchDetection() {
      this.$http
        .post(
          "/monitor/event/startMsgDetection?eventId=" +
            this.$route.query.eventId,
          {}
        )
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success("探测已启动，稍后刷新页面查看结果");
          } else {
            this.$Message.error(res.body.message);
          }
        })
        .catch((err) => {
          this.$Message.error(err.body.message);
        });
    },
    handleTs() {
      this.$http
        .get(
          "/monitor/event/eventPush?eventId=" + this.$route.query.eventId,
          {}
        )
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success("已推送至山东通群");
          } else {
            this.$Message.error(res.body.message);
          }
        })
        .catch((err) => {
          this.$Message.error(err.body.message);
        });
    },
    // 切换重点提醒状态
    toggleImportant() {
      const eventId = this.$route.query.eventId;
      if (!eventId) {
        this.$Message.warning("事件ID不存在！");
        return false;
      }

      const type = this.isImportant ? 0 : 1; // 0-关闭重点 1-加重点

      let params = {
        eventId: eventId,
        type: type
      };

      this.$http.get("/monitor/event/setImportant", { params }).then((res) => {
        if (res.body.status === 0) {
          this.isImportant = !this.isImportant;
          this.$Message.success(this.isImportant ? "设置重点提醒成功！" : "取消重点提醒成功！");
        } else {
          this.$Message.error(res.body.message || "操作失败");
        }
      });
    },

    // 视频相关方法
    handleVideoUpload(file) {
      this.uploadingVideo = file;

      // 检查当前是否已有视频
      if (this.eventInfo.vedioFiles && this.eventInfo.vedioFiles.length > 0) {
        if (this.eventInfo.vedioFiles.length === 1) {
          // 只有一个视频，显示替换确认弹窗
          this.showVideoReplaceModal = true;
        } else {
          // 多个视频，显示选择替换弹窗
          this.selectedVideoIndex =-1;
          this.showVideoSelectModal = true;
        }
      } else {
        // 没有视频，直接上传
        this.uploadVideo(file);
      }

      return false; // 阻止默认上传行为
    },

    uploadVideo(file, isReplace = false, replaceIndex = -1) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', '21');

      this.$http.post('/minIO/common/uploadFiles', formData).then((res) => {
        if (res.body.status === 0) {
          // 视频上传成功，直接保存到事件，不显示中间提示
          this.saveVideoToEvent(res.body.data.fileId, isReplace, replaceIndex);
        } else {
          this.$Message.error(res.body.message || '视频上传失败');
        }
      }).catch(() => {
        this.$Message.error('视频上传失败');
      });
    },

    saveVideoToEvent(newVideoId, isReplace = false, replaceIndex = -1) {
      let videoIds = [];

      if (isReplace && replaceIndex >= 0) {
        // 替换模式：用新视频替换指定位置的视频
        videoIds = this.eventInfo.vedioFiles ?
          this.eventInfo.vedioFiles.map(video => video.fileId) : [];
        videoIds[replaceIndex] = newVideoId;
      } else if (isReplace && this.eventInfo.vedioFiles && this.eventInfo.vedioFiles.length === 1) {
        // 单个视频替换模式
        videoIds = [newVideoId];
      } else {
        // 新增模式：保留现有视频，添加新视频
        videoIds = this.eventInfo.vedioFiles ?
          this.eventInfo.vedioFiles.map(video => video.fileId) : [];
        videoIds.push(newVideoId);
      }

      const params = {
        eventId: this.$route.query.eventId,
        videoId: videoIds.join(',') // 将视频ID数组转换为逗号分隔的字符串
      };

      this.$http.get('/monitor/event/addVideo', { params }).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success('视频保存成功');
          // 重新获取事件信息以更新视频列表
          this.getEventInfo();
        } else {
          this.$Message.error(res.body.message || '视频保存失败');
        }
      });
    },

    getVideoUrl(fileUrl) {
      // 根据文件名构建视频URL
      return gl.minioUrl + fileUrl;
    },

    getVideoInfo(video) {
      return {
        videoUrl: this.getVideoUrl(video.fileUrl),
        mstatus: 1 // 1表示视频可以正常播放
      };
    },

    getVideoOptions(video) {
      return {
        autoplay: false,
        controls: true,
        
        sources: [{
          src: this.getVideoUrl(video.fileUrl),
          type: 'video/mp4'
        }]
      };
    },

    confirmReplaceVideo(isReplace) {
      this.showVideoReplaceModal = false;
      this.showVideoSelectModal =false;
      if (this.uploadingVideo) {
        this.uploadVideo(this.uploadingVideo, isReplace); // 传递替换标识
      }
    },

    cancelReplaceVideo() {
      this.showVideoReplaceModal = false;
      this.uploadingVideo = null;
    },

    confirmSelectReplace() {
      
      if(this.selectedVideoIndex === -1){
        //提示请选择
        this.$Message.warning('请选择需要替换的视频');
        return;
      }
      this.showVideoSelectModal = false;
      if (this.selectedVideoIndex >= 0 && this.uploadingVideo) {
        this.uploadVideo(this.uploadingVideo, true, this.selectedVideoIndex); // 传递替换标识和索引
      }
    },

    cancelSelectReplace() {
      this.showVideoSelectModal = false;
      this.selectedVideoIndex = -1;
      this.uploadingVideo = null;
    },

    addNewVideo() {
      this.showVideoSelectModal = false;
      if (this.uploadingVideo) {
        this.uploadVideo(this.uploadingVideo, false); // 传递新增标识
      }
    },
    // 删除视频方法
    delVideo(videoToDelete) {
      // 确认删除对话框
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除视频"${videoToDelete.fileName}"吗？删除后无法恢复。`,
        okText: '确定删除',
        cancelText: '取消',
        onOk: () => {
          this.performDeleteVideo(videoToDelete);
        }
      });
    },
    // 执行删除视频操作
    performDeleteVideo(videoToDelete) {
      // 获取剩余的视频ID列表（排除要删除的视频）
      const remainingVideoIds = this.eventInfo.vedioFiles
        .filter(video => video.fileId !== videoToDelete.fileId)
        .map(video => video.fileId);

      const params = {
        eventId: this.$route.query.eventId,
        videoId: remainingVideoIds.join(',') // 传递剩余视频的ID列表，用逗号分隔
      };

      // 调用接口删除视频
      this.$http.get('/monitor/event/addVideo', { params }).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success('视频删除成功');
          // 重新获取事件信息以更新视频列表
          this.getEventInfo();

          // 记录操作日志
          this.getLog("事件分析/事件列表", `删除雪亮视频/${videoToDelete.fileName}`);
        } else {
          this.$Message.error(res.body.message || '视频删除失败');
        }
      }).catch((error) => {
        console.error('删除视频失败:', error);
        this.$Message.error('视频删除失败，请稍后重试');
      });
    }
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getEventInfo().then((name) => {
      this.getLog("事件分析/事件列表", "查看事件分析/" + name);
    });
  },
};
</script>
<style scoped lang="less">
/deep/ .ivu-radio-wrapper{
  font-size:14px;
}
/deep/.ivu-modal{
  top:200px
}
/deep/.abc{
  display: block;
  padding:0 30px;

  .ivu-radio-group{

    align-content: center;
    justify-content: center;
    gap: 10px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    &>div{

      min-width: 100px;
      max-width:100%;

    }
    .ivu-radio-wrapper {
      max-width: 100%;
      overflow:hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

}
.atitle{
  display: flex;
  width: 100%;
  text-align: center;
  padding: 80px 80px 50px;
  position: relative;
  .nn{
    font-size: 30px; font-weight: 600; width: 100%;
    line-height:1.2em;
  }
}
.eventInfo {
  margin-top: 20px;
  width: 1727px;
  border-radius: 4px;

  & > div {
    height: calc(~"100vh - 30px");
    overflow-y: auto;
    background-color: #fff;
  }

  .analyze {
    flex-wrap: wrap;
    width: 1100px;
    padding: 10px;
    position: relative;

    /deep/ .controls {
      position: absolute;
      right: 10px;
      top: 10px;
      align-items: center;

      .svg-icon {
        margin: 5px;
        cursor: pointer;
      }
      & > span {
        color: #5585ec;
        font-size: 14px;
        margin: 0 5px;
        cursor: pointer;
      }
    }

    .eventName {
      font-size: 30px;
      line-height: 100px;
      font-weight: 600;
      text-align: center;
      width: 100%;
    }
  }

  // 星星重点提醒按钮样式
  .star-important {
    position: absolute;
    top: 80px;
    right: 0px; // 避免与topControls重叠
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    z-index: 10;
    box-sizing: border-box;
    height:calc(24px + 16px);
    line-height: 24px;
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
      transform: scale(1.1);
    }

    .star-icon {
      font-size: 24px;
      transition: all 0.3s ease;
    }
  }

  .promptSheet {
    width: 620px;
    position: relative;
    overflow: hidden;
    .tabs {
      overflow: hidden;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      position: relative;
      height: 60px;
      .item {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        text-align: center;
        line-height: 60px;
        width: 53%;
        font-size: 16px;
        &:nth-child(1) {
          clip-path: polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%);
          left: 0;
        }
        &:nth-child(2) {
          clip-path: polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%);
          right: 0;
        }
      }
      .active {
        background-color: #5585ec;
        color: #fff;
      }
    }
  }
}
.topControls {
  position: absolute;
  right: 10px;
  top: 20px;
  .item {
    padding: 0 5px;
    display: flex;
    align-items: center;
    // width: 110px;
    justify-content: center;
    height: 50px;
    background: #e6f7ff;
    border: 1px solid #5585ec;
    border-radius: 4px;
    font-size: 14px;
    line-height: 24px;
    cursor: pointer;
    text-align: center;
    margin: 0 2px;
    height: 24px;
  }
}

// 视频播放器样式
.video-container {  
  width: 100%;
  

  .video-item {
    margin:0 0 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    /deep/.video-player {
 
      width: 100%;
      max-width: 800px;
      height: 450px;
      border-radius: 4px;
      overflow: hidden;
 
      video{
        background: white;
      }
      // VideoPlayerNew 组件样式覆盖
       .videoPlayer {
        height: 100%;
        border-radius: 4px;
        video{
        background:white
      }
      }
      

      /deep/ .video-js {
        border-radius: 4px;
      }
    }

    .video-info {
      margin-top: 8px;
      padding: 8px 0;

      .video-name {
        font-size: 14px;
        color: #666;
      }
    }

    .alink{
 
    font-size:16px;
    text-decoration: underline;
    color:#5680E2;
    cursor: pointer;
    margin:0 20px;
  }
  }
}
</style>
