<template>
  <div class="shortVideo">
    <div class="searchBox flex">
      <Input
        v-model="searchValue"
        placeholder="请输入您要查找的内容"
        style="width: 500px;"
        @on-enter="search"
      >
      </Input>
      <div class="btn cp" @click="search">搜索</div>
    </div>
    <div class="filter_box">
      <div class="filter flex sb">
        <!-- <div class="item flex">
          <div class="label flex">
            <svg-icon icon-class="榜单-领域" />
            范围：
          </div>
          <div class="options flex">
            <div
              v-for="item in rangeOption"
              :key="item.key"
              :class="['item', 'cp', item.key == nowRangeKey ? 'active' : '']"
              @click="selectRange(item)"
            >
              {{ item.name }}
            </div>
          </div>
        </div> -->
        <div class="item flex">
          <div class="label flex">
            <svg-icon icon-class="榜单-时间" />
            时间：
          </div>
          <div class="options flex">
            <div
              v-for="item in timeOption"
              :key="item.key"
              :class="['item', 'cp', dayNum === item.key ? 'active' : '']"
              @click="dayNum = item.key"
            >
              {{ item.name }}
            </div>
            <DatePicker
              v-if="dayNum === '-99'"
              v-model="timeFrame"
              format="yyyy-MM-dd HH:mm"
              placeholder="请选择时间范围"
              style="width: 300px;"
              type="datetimerange"
              split-panels
              @on-change="dateChange"
            ></DatePicker>
          </div>
        </div>
      </div>
      <div class="filter flex sb">
        <div class="item flex">
          <div class="label flex">
            <svg-icon icon-class="榜单-来源" />
            来源：
          </div>
          <div class="options flex">
            <div
              v-for="item in sourceOption"
              :key="item.key"
              :class="[
                'item',
                'cp',
                websiteNames.includes(item.name) ? 'active' : '',
              ]"
              @click="selectSource(item.name)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="listBox flex sb">
      <Spin v-if="loading" fix>
        <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
        <div>Loading</div>
      </Spin>
      <NoData v-if="!loading && total === 0 && listData.length === 0" />
      <template v-if="total > 0">
        <ListItem
          v-for="item in listData"
          :key="item.mkey"
          :info="item"
          :keyword="keyword"
        />
        <div
          :key="item"
          v-for="item in 5 - (listData.length % 5)"
          style="width: 320px;"
        ></div>
        <Page
          v-if="total > 0 && !loading"
          :total="total"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
          :current="pageNo"
          :page-size="pageSize"
          show-elevator
          show-sizer
          show-total
        />
      </template>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import { sourceOption, timeOption, rangeOption } from "./filter.js";
import ListItem from "./components/listItem.vue";
import moment from "moment";

export default {
  data() {
    // 这里存放数据
    return {
      nowRangeKey: "1",
      tipsStatus: false,
      keyword: "",
      searchValue: "",
      timeOption,
      sourceOption,
      rangeOption,
      pageNo: 1,
      pageSize: 10,
      dayNum: "3",
      timeFrame: null,
      websiteNames: ["全部"],
      loading: false,
      total: 0,
      listData: [],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { ListItem },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getDataCount();
  },
  // 方法集合
  methods: {
    moment,
    selectRange(item) {
      this.nowRangeKey = item.key;
      this.search();
    },
    selectSource(text) {
      if (text === "全部") {
        this.websiteNames = ["全部"];
      } else {
        if (this.websiteNames.includes("全部")) {
          this.websiteNames.splice(this.websiteNames.indexOf("全部"), 1);
        }
        if (!this.websiteNames.includes(text)) {
          this.websiteNames.push(text);
        } else {
          this.websiteNames.splice(this.websiteNames.indexOf(text), 1);
        }
        if (this.websiteNames.length === 0) {
          this.websiteNames.push("全部");
        }
      }
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getDataList();
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getDataList();
    },
    search() {
      this.keyword = this.searchValue;
      this.getDataCount();
    },
    getParams() {
      let params = {};
      if (this.dayNum === "-99" && !this.timeFrame[0]) {
        return false;
      }
      if (this.dayNum === "-99" && this.timeFrame[0]) {
        params.startTime = moment(this.timeFrame[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        params.endTime = moment(this.timeFrame[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      } else {
        params.dayNum = this.dayNum === "all" ? null : this.dayNum;
      }
      // params.mStatus = this.nowRangeKey;
      console.log(params);
      return {
        ...params,
        eventId: this.$route.query.eventId, // 添加事件ID参数
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword,
        websiteNames:
          this.websiteNames[0] === "全部" ? null : this.websiteNames.toString(), //为其他，今日头条还不知道是几
      };
    },
    getDataCount() {
      this.listData = [];
      this.pageNo = 1;
      this.total = 0;
      let params = this.getParams();
      if (!params) {
        this.$Message.warning("请选择时间范围后重试");
        return false;
      }
      this.getDataList();
      // 获取已下载视频总数
      this.$http.get("/api/monitor/event/downloadedVideos", { params }).then((res) => {
        this.total = res.body.data ? res.body.data.length : 0;
      });
    },
    getDataList() {
      this.loading = true;
      let params = this.getParams();
      this.listData = [];
      // 获取已下载视频列表
      this.$http.get("/api/monitor/event/downloadedVideos", { params }).then((res) => {
        if (res.body.data && res.body.data.length > 0) {
          this.listData = res.body.data;
        }
        this.loading = false;
      });
    },
    dateChange(date) {
      this.search();
      console.log(date);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    dayNum: {
      handler(val) {
        if (val !== "-99") {
          this.search();
          this.timeFrame = null;
        }
      },
    },
    websiteNames: {
      handler(val) {
        this.search();
      },
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getLog("涉济监测/短视频监测");
  },
};
</script>
<style lang="less" scoped>
.shortVideo {
  padding: 20px 20px 20px 0;

  & > div {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;
  }

  .searchBox {
    .svg-icon {
      width: 26px;
      height: 26px;
      margin-top: 10px;
      margin-right: 10px;
    }

    /deep/ .ivu-input {
      height: 50px;
      font-size: 16px;
    }

    .btn {
      width: 100px;
      height: 50px;
      margin-left: 10px;
      line-height: 50px;
      background-color: #5585ec;
      color: #fff;
      text-align: center;
      border-radius: 5px;
    }
  }
  .filter_box {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
  }

  .filter {
   
     padding:5px 0;
    
    & > .item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      .svg-icon {
        width: 25px;
        height: 25px;
        margin-right: 10px;
      }

      .label {
        align-items: center;
      }

      .options {
        height: 30px;
        align-items: center;

        .item {
          margin-right: 10px;
        }

        .active {
          color: #5585ec;
        }
      }
    }
  }

  .listBox {
    flex-wrap: wrap;
    height: calc(~"100vh - 185px");
    overflow-y: auto;
    position: relative;

    .noData {
      margin: 0 auto;
    }

    .ivu-page {
      margin: 10px auto;
      width: 100%;
    }
  }
}
/deep/.ivu-page-options-sizer {
  position: relative;
}
</style>
