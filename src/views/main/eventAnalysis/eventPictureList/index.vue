<template>
  <div class="shortVideo">
    <div class="searchBox flex">
      <Input
        v-model="searchValue"
        placeholder="请输入您要查找的内容"
        style="width: 500px;"
        @on-enter="search"
      >
      </Input>
      <div class="btn cp" @click="search">搜索</div>
    </div>
    <div class="filter_box">
      <div class="filter flex sb">
        <div class="item flex">
          <div class="label flex">
            <svg-icon icon-class="榜单-时间" />
            时间：
          </div>
          <div class="options flex">
            <div
              v-for="item in timeOption"
              :key="item.key"
              :class="['item', 'cp', dayNum === item.key ? 'active' : '']"
              @click="dayNum = item.key"
            >
              {{ item.name }}
            </div>
            <DatePicker
              v-if="dayNum === '-99'"
              v-model="timeFrame"
              format="yyyy-MM-dd HH:mm"
              placeholder="请选择时间范围"
              style="width: 300px;"
              type="datetimerange"
              split-panels
              @on-change="dateChange"
            ></DatePicker>
          </div>
        </div>
        <div class="item flex">
          <div class="label flex">
            <svg-icon icon-class="榜单-来源" />
            来源：
          </div>
          <div class="options flex">
            <div
              v-for="item in sourceOption"
              :key="item.key"
              :class="[
                'item',
                'cp',
                websiteNames.includes(item.key) ? 'active' : '',
              ]"
              @click="selectSource(item.key)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="listBox flex sb">
      <Spin v-if="loading" fix>
        <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
        <div>Loading</div>
      </Spin>
      <NoData v-if="!loading && total === 0 && listData.length === 0" />
      <template v-if="total > 0">
        <ListItem
          v-for="(item, index) in listData"
          :key="item.mkey + item.fileName + index"
          :info="item"
        />
        <div
          :key="item"
          v-for="item in 5 - (listData.length % 5)"
          style="width: 320px;"
        ></div>
        <Page
          v-if="total > 0 && !loading"
          :total="total"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
          :current="pageNo"
          :page-size="pageSize"
          show-elevator
          show-sizer
          show-total
        />
      </template>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import { sourceOption, timeOption, rangeOption } from "./filter.js";
import ListItem from "./components/listItem1.vue";
import moment from "moment";
import imgArr from "./mock.js";

export default {
  data() {
    // 这里存放数据
    return {
      nowRangeKey: "1",
      tipsStatus: false,
      keyword: "",
      searchValue: "",
      timeOption,
      sourceOption,
      rangeOption,
      pageNo: 1,
      pageSize: 10,
      dayNum: "7",
      timeFrame: null,
      websiteNames: ["all"],
      loading: false,
      total: 0,
      listData: [],
      imgArr,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { ListItem },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getDataCount();
  },

  // 方法集合
  methods: {
    moment,
    // selectRange(item) {
    //   this.nowRangeKey = item.key;
    //   this.search();
    // },
    selectSource(text) {
      if (text === "all") {
        this.websiteNames = ["all"];
      } else {
        if (this.websiteNames.includes("all")) {
          this.websiteNames.splice(this.websiteNames.indexOf("all"), 1);
        }
        if (!this.websiteNames.includes(text)) {
          this.websiteNames.push(text);
        } else {
          this.websiteNames.splice(this.websiteNames.indexOf(text), 1);
        }
        if (this.websiteNames.length === 0) {
          this.websiteNames.push("all");
        }
      }
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getDataList();
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getDataList();
    },
    search() {
      this.keyword = this.searchValue;
      this.getDataCount();
    },
    getParams() {
      let params = {};
      if (this.dayNum === "-99" && !this.timeFrame[0]) {
        return false;
      }
      if (this.dayNum === "-99" && this.timeFrame[0]) {
        params.startTime = moment(this.timeFrame[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        params.endTime = moment(this.timeFrame[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      } else {
        params.dayNum = this.dayNum === "all" ? null : this.dayNum;
      }
      return {
        ...params,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword,
        situations:
          this.websiteNames[0] === "all" ? null : this.websiteNames.toString(), //为其他，今日头条还不知道是几
      };
    },
    getDataCount() {
      this.listData = [];
      this.pageNo = 1;
      this.total = 0;
      let params = this.getParams();
      if (!params) {
        this.$Message.warning("请选择时间范围后重试");
        return false;
      }
      this.$http.get("/search/picCount", {params}).then((res) => {
        this.total = res.body.data || 0;
      });
      this.getDataList();
    },
    getDataList() {
      this.loading = true;
      let params = this.getParams();
      this.listData = [];
      this.$http.get("/search/picList", {params}).then((res) => {
        if (res.body.data && res.body.data.length > 0) {
          this.listData = res.body.data;
        }
        this.loading = false;
      });
    },
    dateChange(date) {
      this.search();
      console.log(date);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    dayNum: {
      handler(val) {
        if (val !== "-99") {
          this.search();
          this.timeFrame = null;
        }
      },
    },
    websiteNames: {
      handler(val) {
        this.search();
      },
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getLog("涉济监测/短视频监测");
  },
};
</script>
<style lang="less" scoped>
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #fff;
  border-radius: 10px;
  border-left: 4px solid #5585ec;
}

.shortVideo {
  padding: 20px 20px 20px 0;

  & > div {
    background-color: #fff;
    padding: 10px;
    border-radius: 10px;
  }

  .searchBox {
    .svg-icon {
      width: 26px;
      height: 26px;
      margin-top: 10px;
      margin-right: 10px;
    }

    /deep/ .ivu-input {
      height: 50px;
      font-size: 16px;
    }

    .btn {
      width: 100px;
      height: 50px;
      margin-left: 10px;
      line-height: 50px;
      background-color: #5585ec;
      color: #fff;
      text-align: center;
      border-radius: 5px;
    }
  }
  .filter_box {
    margin: 10px 0;
  }

  .filter {
    &:not(:last-child) {
      margin-bottom: 10px;
    }
    & > .item {
      flex: 1;

      .svg-icon {
        width: 25px;
        height: 25px;
        margin-right: 10px;
      }

      .label {
        align-items: center;
      }

      .options {
        height: 30px;
        align-items: center;

        .item {
          margin-right: 10px;
        }

        .active {
          color: #5585ec;
        }
      }
    }
  }

  .listBox {
    flex-wrap: wrap;
    height: calc(~"100vh - 180px");
    overflow-y: auto;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    &::after {
      // content: "";
    }
    &:after {
      // content: "";
      // flex-grow: 999999999;
    }

    .noData {
      margin: 0 auto;
    }

    .ivu-page {
      margin: 10px auto;
      width: 100%;
    }
  }
}
/deep/.ivu-page-options-sizer {
  position: relative;
}
</style>
