<template>
  <div
    class="imgFrame"
    ref="imgFrame"
    v-show="info.pictureFiles"
    :title="info.mtitle"
  >
    <!-- :src="imageBaseUrl + info.pictureFiles" -->
    <ImgPreview
      v-if="ImgPreviewStatus"
      :imgList="[imageBaseUrl + info.pictureFiles]"
      @close="ImgPreviewStatus = false"
    />
    <Icon
      type="ios-expand"
      v-if="imgStart"
      color="#fff"
      @click.native="Enlarge"
    />
    <img
      :src="imageBaseUrl + info.pictureFiles"
      v-if="imgStart"
      alt=""
      :title="info.mtitle"
      @error="getImgStart()"
      @click="toDetails(info)"
    />

    <div v-else @click="toDetails(info)">
      <svg-icon
        style="height: 200px; width: 180px;"
        icon-class="ImageLoadingFailed"
      />
    </div>
  </div>
</template>

<script>
import ImgPreview from "@/components/imgPreview";

export default {
  props: ["info"],
  name: "",
  //import 引入组件
  components: { ImgPreview },
  watch: {},
  data() {
    return {
      num: 0,
      imgStart: true,
      ImgPreviewStatus: false,
      // imageBaseUrls: "https://***********:39436/imgApi/",
    };
  },
  methods: {
    Enlarge() {
      this.ImgPreviewStatus = true;
    },
    getImgStart() {
      this.imgStart = false;
    },
    toDetails() {
      console.log(this.info);
      console.log(this.$route);
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: this.info.mkey,
          keyword: this.keyword,
          situation: this.info.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
          moduleOriginName:
            this.$route.meta && this.$route.meta.moduleName
              ? encodeURIComponent(this.$route.meta.moduleName)
              : "",
          moduleName:
            this.$route.meta && this.$route.meta.moduleName
              ? encodeURIComponent(
                  this.$route.meta.moduleName.split("/")[0] + "/正文页"
                )
              : "",
        },
      });
      window.open(href, "_blank");
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
img {
  min-width: 100%;
  height: 200px;
  object-fit: cover;
}
.imgFrame {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  overflow: hidden;
  break-inside: avoid;
  box-sizing: border-box;
  padding: 8px 4px 0 4px;
  position: relative;
  flex-grow: 1;
  /deep/.ivu-icon {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}
.open {
  position: absolute;
  bottom: 15px;
  padding: 0 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  .imgCheck {
    margin-right: 40px;
  }
  .operatBtn {
    flex: 1;
    max-width: 198px;
  }
}
</style>
