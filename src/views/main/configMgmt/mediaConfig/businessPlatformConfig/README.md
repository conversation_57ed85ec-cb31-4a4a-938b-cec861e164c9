# 商业平台库模块

## 概述
商业平台库模块是全息档案系统中的一个新模块，位于媒体库模块下方。该模块包含两个主要功能：主体详情和商业平台清单。

## 功能特性

### 1. 主体详情
- **复用设计**: 复用媒体库详情页的样式和跳转逻辑
- **API接口**: 使用 `/resourceslibrary/mainBodyList` 接口
- **详情页路由**: `/main/Archive/businessPlatformLibrary/topicDetails/detail`
- **功能**: 
  - 显示单位名称、许可证编号以及八个渠道对应的数量和修改时间
  - 点击各渠道对应数量能查看详情，同现有媒体库详情页
  - 筛选条件包括查找，查找可按单位名称查找和许可证编号查找

### 2. 商业平台清单
- **API接口**: 使用 `/resourceslibrary/otherList` 接口
- **功能**:
  - 显示详细的商业平台信息
  - 包含舆论场字段，对应系统中八个平台，可以选择配置
  - 筛选条件：许可证编号查找和渠道选择（去掉了属地筛选）

## 技术实现

### 路由配置
```javascript
// 主模块路由
{
  path: "/main/Archive/businessPlatformLibrary",
  name: "商业平台库",
  component: () => import("@/views/main/Archive/businessPlatformLibrary"),
  redirect: "/main/Archive/businessPlatformLibrary/topicDetails",
  children: [
    {
      path: "/main/Archive/businessPlatformLibrary/topicDetails",
      name: "主体详情",
      component: () => import("@/views/main/Archive/businessPlatformLibrary/topicDetails"),
    },
    {
      path: "/main/Archive/businessPlatformLibrary/platformList",
      name: "商业平台清单",
      component: () => import("@/views/main/Archive/businessPlatformLibrary/platformList"),
    },
  ],
}

// 详情页路由
{
  path: "/main/Archive/businessPlatformLibrary/topicDetails/detail",
  name: "商业平台库主体详情查看",
  component: () => import("@/views/main/Archive/businessPlatformLibrary/topicDetails/detail"),
}
```

### 组件实现
- **TopicList组件**: 独立创建，专门为商业平台库设计，使用 `/resourceslibrary/mainBodyList` API
- **DetailView组件**: 独立创建，专门为商业平台库设计，使用 `/resourceslibrary/otherList` API
- **TopicFilters组件**: 独立创建，复制媒体库筛选组件并适配商业平台库需求
- **PlatformList组件**: 全新创建，专门用于商业平台清单展示
- **PlatformFilters组件**: 全新创建，专门用于商业平台清单筛选

### API接口

#### 主体详情列表接口
- **URL**: `/resourceslibrary/mainBodyList`
- **参数**: 
  - `searchType`: 查询类型 (1: 按单位名称, 2: 按许可证编号)
  - `keyWord`: 查询词
  - `pageNo`: 页码
  - `pageSize`: 每页条数

#### 商业平台清单接口
- **URL**: `/resourceslibrary/otherList`
- **参数**:
  - `licenseNumber`: 许可证编号
  - `channel`: 渠道
  - `pageNo`: 页码
  - `pageSize`: 每页条数

### 舆论场配置
```javascript
situationOptions: {
  30: '媒体网站',
  31: '客户端',
  10: '新浪微博',
  199: '短视频',
  20: '微信公众号',
  80: '小红书',
  60: '论坛贴吧',
  600: '今日头条'
}
```

## 文件结构
```
src/views/main/Archive/businessPlatformLibrary/
├── index.vue                          # 主入口页面
├── README.md                          # 功能说明文档
├── topicDetails/                      # 主体详情模块
│   ├── index.vue                      # 主体详情列表页
│   ├── detail.vue                     # 主体详情详情页
│   └── components/
│       ├── TopicFilters.vue           # 主体详情筛选组件
│       ├── TopicList.vue              # 主体详情列表组件
│       └── DetailView.vue             # 主体详情查看组件
└── platformList/                      # 商业平台清单模块
    ├── index.vue                      # 商业平台清单页
    └── components/
        ├── PlatformFilters.vue        # 筛选组件
        └── PlatformList.vue           # 列表组件
```

## 使用说明

1. **访问路径**: 全息档案 -> 商业平台库
2. **主体详情**: 查看和搜索各单位的基本信息和渠道统计
3. **商业平台清单**: 查看详细的平台信息，可配置舆论场
4. **筛选功能**: 支持按许可证编号和渠道进行筛选
5. **详情查看**: 点击主体详情中的数量可查看具体的平台信息

## 注意事项

1. 该模块为独立创建，不会影响现有媒体库功能
2. 所有组件都是独立的，便于维护和修改
3. API接口需要后端支持，确保接口返回数据格式正确：
   - `/resourceslibrary/mainBodyList` - 主体详情列表接口
   - `/resourceslibrary/otherList` - 商业平台清单和详情接口
4. 舆论场字段的保存功能需要后端API支持
5. 筛选条件中去掉了属地字段，与媒体库有所不同
6. 所有组件都支持响应式设计，适配不同屏幕尺寸
