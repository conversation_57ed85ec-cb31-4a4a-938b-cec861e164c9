<template>
  <div class="detail-view">
    <div class="detail-content">
      <div class="search-sidebar">
        <div class="search-box">
          <Input
            v-model="searchKeyword"
            placeholder="请输入单位名称"
            search
            @on-search="searchSubject"
          />
        </div>
        <div class="subject-list" ref="subjectListRef" @scroll="handleScroll">
          <div
            v-for="(item, index) in filteredSubjects"
            :key="index"
            :class="['subject-item', { active: currentDetail.licenseNumber === item.xkzbh }]"
            @click="selectSubject(item)"
          >
          <div class="subject-item-content">
            <Checkbox
                :value="item.isMonitor === 1"
                :indeterminate="item.isMonitor === 2"
                @on-change="(checked) => toggleSubjectMonitor(item, checked)"
                @click.stop
                style="margin-right: 8px;"
              ></Checkbox>
              <span>{{ item.dwmc || '未知单位' }}</span>
          </div>
          </div>
          <div v-if="subjectLoading" class="loading-more">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <span>加载中...</span>
          </div>
          <div v-if="!hasMoreSubjects && filteredSubjects.length > 0" class="no-more">
            没有更多数据了
          </div>
        </div>
      </div>

      <div class="detail-main">
        <div class="channel-tabs">
          <div class="back-button" @click="goBack">
          <Icon type="md-arrow-back" />
          返回全部列表
        </div>
        <div class="channel-tabs-cont">
          <div
            v-for="(count, channel) in channelCounts"
            :key="channel"
            :class="['channel-tab', { active: currentChannel === channel }]"
            @click="changeChannel(channel)"
          >
            {{ channel }} ({{ count }})
          </div>
        </div>

        </div>

        <div class="channel-content">
          <Spin v-if="loading" fix>
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>加载中...</div>
          </Spin>

          <!-- 使用表格布局实现单元格合并效果 -->
          <div v-if="!loading && channelList.length > 0" class="custom-table-container">
            <table class="custom-table">
              <thead>
                <tr>
                  <!-- 动态生成表头，根据 channelColumnsMap 定义 -->
                  <th v-for="(column, index) in channelColumnsMap[currentChannel]" :key="index" :class="getColumnClass(column.key || column.slot)">
                    {{ column.title }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- 处理数据分组，按单位名称和许可证编号分组 -->
                <template v-for="(group, groupIndex) in groupedChannelList">
                  <tr v-for="(item, itemIndex) in group.items" :key="groupIndex + '-' + itemIndex">
                    <!-- 动态生成表格内容，根据 channelColumnsMap 定义 -->
                    <template v-for="(column, colIndex) in channelColumnsMap[currentChannel]">
                      <!-- 序号列，每行都显示 -->
                      <td v-if="column.slot === '序号'" :key="'col-' + colIndex + '-' + groupIndex + '-' + itemIndex" class="number-cell">
                        {{ (currentPage - 1) * pageSize + getGroupStartIndex(groupIndex) + itemIndex + 1 }}
                      </td>

                      <!-- 单位名称（合并单元格） -->
                      <td v-else-if="column.key === 'unitName' && itemIndex === 0" :key="'col-' + colIndex + '-' + groupIndex + '-unit'" :rowspan="group.items.length" class="unit-name-cell" style="vertical-align: middle;">
                        <div class="ccol">
                          <Checkbox
                            :value="group.isMonitor == 1"
                            :indeterminate="group.isMonitor === 2"
                            @on-change="(checked) => toggleUnitMonitor(group, checked)"
                            style="margin-right: 10px;"
                          ></Checkbox>
                          <span>{{ group.unitName }}</span>
                        </div>
                      </td>

                      <!-- 许可证编号（合并单元格） -->
                      <td v-else-if="column.key === 'licenseNumber' && itemIndex === 0" :key="'col-' + colIndex + '-' + groupIndex + '-license'" :rowspan="group.items.length" class="license-number-cell" style="vertical-align: middle;">
                        {{ group.licenseNumber }}
                      </td>

                      <!-- 服务名称/媒体名称 -->
                      <td v-else-if="column.key === 'serviceName'" :key="'col-' + colIndex + '-' + groupIndex + '-' + itemIndex + '-service'" class="service-name-cell">
                        <div class="ccol">
                          <Checkbox
                            :value="item.isMonitor === 1"
                            :indeterminate="item.isMonitor === 2"
                            @on-change="(checked) => toggleServiceMonitor(item, checked)"
                            style="margin-right: 10px;"
                          ></Checkbox>
                           <span>{{ factorSName(item) }}</span>
                        </div>
                      </td>

                      <!-- 服务地址/平台 -->
                      <td v-else-if="column.key === 'serviceAddress'" :key="'col-' + colIndex + '-' + groupIndex + '-' + itemIndex + '-address'" :class="getColumnClass(column.key)">
                        {{ item.serviceAddress }}
                      </td>

                      <!-- 舆论场 -->
                      <td v-else-if="column.key === 'situation'" :key="'col-' + colIndex + '-' + groupIndex + '-' + itemIndex + '-opinion'" class="public-opinion-cell">
                        <!-- <Select
                          v-model="item.situation"
                          style="width: 100%;"
                          placeholder="请选择舆论场"
                          @on-change="(value) => handleSituationChange(item, value)"
                        >
                          <Option :value="String(index)" v-for="(situationItem, index) in situations" :key="index">{{ situationItem }}</Option>
                        </Select> -->
                        <span class="situation-label">{{ getSituationLabel(item.situation) }}</span>
                      </td>

                    </template>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>

          <div class="no-data" v-if="!loading && channelList.length === 0">
            暂无数据
          </div>

        </div>
        <div class="pagination-container" v-if="!loading && channelList.length > 0">
            <Page
              :total="total"
              :current="currentPage"
              :page-size="pageSize"
              show-total
              show-elevator
              show-sizer
              @on-change="changePage"
              @on-page-size-change="pageSizeChange"
            />
          </div>
      </div>
    </div>
  </div>
</template>

<script>
import { channelTypes } from "../options";
import { getYulunchangName } from '@/utils/yulunchang'

export default {
  data() {
    return {
      situations:{
                        30: "媒体网站",
                        31: "客户端",
                        10: "新浪微博",
                        199: "短视频",
                        20: "微信公众号",
                        80: "小红书",
                        60: "论坛贴吧",
                        600: "今日头条",
                    },
      loading: false,
      searchKeyword: "",
      subjects: [], // 所有主体列表
      filteredSubjects: [], // 过滤后的主体列表
      currentDetail: {}, // 当前选中的主体
      currentChannel: "", // 当前选中的渠道
      channelList: [], // 当前渠道的媒体列表
      channelCounts: {}, // 各渠道的媒体数量
      // 分页相关
      pageSize: 10, // 每页显示条数
      currentPage: 1, // 当前页码
      total: 0, // 总记录数
      // 左侧列表相关
      subjectPageSize: 50, // 左侧列表每页显示条数
      subjectCurrentPage: 1, // 左侧列表当前页码
      subjectTotal: 0, // 左侧列表总记录数
      subjectLoading: false, // 左侧列表加载状态
      hasMoreSubjects: true, // 是否有更多主体数据
      showRepeatedValues: false, // 是否显示重复的单位名称和许可证编号
      channelColumnsMap: {
        "互联网站": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '服务名称', key: 'serviceName', minWidth: 150 },
          { title: '服务地址', key: 'serviceAddress', minWidth: 110 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "论坛": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '服务名称', key: 'serviceName', minWidth: 150 },
          { title: '服务地址', key: 'serviceAddress', minWidth: 110 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "博客": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '服务名称', key: 'serviceName', minWidth: 150 },
          { title: '服务地址', key: 'serviceAddress', minWidth: 110 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "微博客": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '服务名称', key: 'serviceName', minWidth: 150 },
          { title: '服务地址', key: 'serviceAddress', minWidth: 110 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "公众账号": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '服务名称', key: 'serviceName', minWidth: 150 },
          { title: '平台', key: 'serviceAddress', width: 60 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "应用程序": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '媒体名称', key: 'serviceName', minWidth: 60 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "网络直播": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '媒体名称', key: 'serviceName', minWidth: 60 },
          { title: '舆论场', key: 'situation', width: 90 }
        ],
        "其他": [
          { title: '序号', slot: '序号', width: 70, align: 'center' },
          { title: '单位名称', key: 'unitName', minWidth: 180 },
          { title: '许可证编号', key: 'licenseNumber', width: 120 },
          { title: '媒体名称', key: 'serviceName', minWidth: 60 },
          { title: '舆论场', key: 'situation', width: 90 }
        ]
      }
    };
  },
  props: {
    detail: {
      type: Object,
      default: () => ({})
    },
    allSubjects: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    
    // 按单位名称和许可证编号分组的数据
    groupedChannelList() {
      const groups = [];
      const groupMap = {};

      // 遍历原始数据，按单位名称和许可证编号分组
      this.channelList.forEach(item => {
        const key = `${item.unitName}-${item.licenseNumber}`;

        if (!groupMap[key]) {
          // 创建新分组
          const group = {
            unitName: item.unitName,
            licenseNumber: item.licenseNumber,
            items: [item],
            isMonitor: item.isMonitor // 初始化分组的监控状态
          };

          groups.push(group);
          groupMap[key] = group;
        } else {
          // 添加到已有分组
          groupMap[key].items.push(item);
        }
      });

      // 计算每个分组的监控状态
      groups.forEach(group => {
        const items = group.items || [];

        if (items.length === 0) {
          group.isMonitor = 0;
          return;
        }

        // 检查是否所有服务都被选中
        const allSelected = items.every(item => item.isMonitor === 1);
        // 检查是否所有服务都未被选中
        const noneSelected = items.every(item => item.isMonitor === 0);

        if (allSelected) {
          // 所有服务都被选中，单位状态为选中
          group.isMonitor = 1;
        } else if (noneSelected) {
          // 所有服务都未被选中，单位状态为未选中
          group.isMonitor = 0;
        } else {
          // 部分服务被选中，单位状态为横杠
          group.isMonitor = 2;
        }
      });

      return groups;
    }
  },
  watch: {
    detail: {
      handler(newVal) {
        if (newVal && newVal.licenseNumber) {
          this.currentDetail = newVal;
          this.currentChannel = newVal.channel;
          this.getChannelData();
        }
      },
      immediate: true
    },
    allSubjects: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          // 如果传入了所有主体列表，则直接使用
          this.subjects = newVal;
          this.filteredSubjects = [...newVal];
        } else {
          // 否则通过API获取主体列表
          this.getSubjectList();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取舆论场标签 - 有数据显示数据，没有数据显示"-"
    getSituationLabel(value) {
      if (!value && value !== 0) {
        return '-';
      }
      return getYulunchangName(value);
    },

    // 更新单位监控状态
    updateUnitMonitorStatus() {
      // 遍历所有分组，检查每个分组中的服务监控状态
      this.groupedChannelList.forEach(group => {
        const items = group.items || [];

        // 如果没有服务项，不更新单位状态
        if (items.length === 0) return;

        // 检查是否所有服务都被选中
        const allSelected = items.every(item => item.isMonitor === 1);
        // 检查是否所有服务都未被选中
        const noneSelected = items.every(item => item.isMonitor === 0);

        // 更新单位监控状态
        if (allSelected) {
          // 所有服务都被选中，单位状态为选中
          group.isMonitor = 1;
          this.currentDetail.isMonitor = 1;
        } else if (noneSelected) {
          // 所有服务都未被选中，单位状态为未选中
          group.isMonitor = 0;
          this.currentDetail.isMonitor = 0;
        } else {
          // 部分服务被选中，单位状态为横杠
          group.isMonitor = 2;
          this.currentDetail.isMonitor = 2;
        }
      });
    },
    // 切换服务监控状态
    toggleServiceMonitor(item, checked) {
      // 切换状态：选中 -> 未选中，未选中 -> 选中
      const isMonitor = checked ? 1 : 0;
      console.log("aaa",item);
      this.$http.post(`/resourceslibrary/editMonitorById?id=${item.id}&isMonitor=${isMonitor}`).then(res => {
        if (res.body.status === 0) {
          this.$Message.success(res.body.message || '修改成功');
          // 更新本地数据
          item.isMonitor = isMonitor;

          // 检查是否需要更新单位级别的监控状态
          this.updateUnitMonitorStatus();
        } else {
          this.$Message.error(res.body.message || '修改失败');
          // 恢复原值，重新获取数据
          this.getChannelList(this.currentPage);
        }
      }).catch(err => {
        console.error('修改监控状态失败', err);
        this.$Message.error('修改失败');
        // 恢复原值，重新获取数据
        this.getChannelList(this.currentPage);
      });
    },
    // 切换单位监控状态
    toggleUnitMonitor(group, checked) {
      // 切换状态：选中 -> 未选中，未选中 -> 选中，横杠 -> 选中
      const isMonitor = checked ? 1 : 0;

      this.$http.post(`/resourceslibrary/editMonitor?licenseNumber=${group.licenseNumber}&isMonitor=${isMonitor}`).then(res => {
        if (res.body.status === 0) {
          this.$Message.success('修改成功');
          // 更新当前详情的监控状态
          this.currentDetail.isMonitor = isMonitor;
          group.isMonitor = isMonitor;

          // 重新获取数据，确保服务名称子集的选中状态与单位名称的状态一致
          this.getChannelList(this.currentPage);
        } else {
          this.$Message.error(res.body.message || '修改失败');
          // 恢复原值，重新获取数据
          this.getChannelList(this.currentPage);
        }
      }).catch(err => {
        console.error('修改监控状态失败', err);
        this.$Message.error('修改失败');
        // 恢复原值，重新获取数据
        this.getChannelList(this.currentPage);
      });
    },
    // 切换左侧单位列表中的监控状态
    toggleSubjectMonitor(subject, checked) {
      // 切换状态：选中 -> 未选中，未选中 -> 选中，横杠 -> 选中
      const isMonitor = checked ? 1 : 0;

      this.$http.post(`/resourceslibrary/editMonitor?licenseNumber=${subject.xkzbh}&isMonitor=${isMonitor}`).then(res => {
        if (res.body.status === 0) {
          this.$Message.success('修改成功');

          // 更新本地数据
          subject.isMonitor = isMonitor;

          // 如果当前选中的单位就是被修改的单位，也更新当前详情的监控状态
          if (this.currentDetail.licenseNumber === subject.xkzbh) {
            this.currentDetail.isMonitor = isMonitor;
            // 重新获取渠道列表数据，确保服务名称子集的选中状态与单位名称的状态一致
            this.getChannelList(this.currentPage);
          }
        } else {
          this.$Message.error(res.body.message || '修改失败');
          // 恢复原值
          this.getSubjectList();
        }
      }).catch(err => {
        console.error('修改监控状态失败', err);
        this.$Message.error('修改失败');
        // 恢复原值
        this.getSubjectList();
      });
    },
    factorSName(row){
      var ba=row.isRegister == 1 ? '' : '(未备案)'
      return row.serviceName + ba;
    },
    // 返回列表
    goBack() {
      this.$emit('goBack');
    },

    // 获取单位列表
    getSubjectList(isLoadMore = false) {
      if (this.subjectLoading) return;

      this.subjectLoading = true;

      // 如果不是加载更多，则重置数据
      if (!isLoadMore) {
        this.subjectCurrentPage = 1;
        this.filteredSubjects = [];
        this.hasMoreSubjects = true;
      }

      const params = {
        pageSize: this.subjectPageSize,
        pageNo: this.subjectCurrentPage,
        searchType: '1',
        keyWord: this.searchKeyword || ''
      };

      this.$http.get('/resourceslibrary/mainBodyList', { params }).then(res => {
        if (res.body.status === 0) {
          const newList = res.body.data.list || [];
          this.subjectTotal = res.body.data.count || 0;

          // 如果是加载更多，则追加数据
          if (isLoadMore) {
            this.filteredSubjects = [...this.filteredSubjects, ...newList];
          } else {
            this.filteredSubjects = newList;
          }

          // 判断是否还有更多数据
          this.hasMoreSubjects = this.filteredSubjects.length < this.subjectTotal;

          // 如果是首次加载且有数据，则选中第一个单位
          if (!isLoadMore && newList.length > 0 && !this.currentDetail.licenseNumber) {
            this.selectSubject(newList[0]);
          }
        } else {
          this.$Message.error(res.body.message || '获取单位列表失败');
        }
        this.subjectLoading = false;
      }).catch(err => {
        console.error('获取单位列表失败', err);
        this.$Message.error('获取单位列表失败');
        this.subjectLoading = false;
      });
    },

    // 处理滚动加载更多
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 滚动到底部时加载更多
      if (scrollHeight - scrollTop - clientHeight < 50 && !this.subjectLoading && this.hasMoreSubjects) {
        this.subjectCurrentPage++;
        this.getSubjectList(true);
      }
    },

    // 搜索主体
    searchSubject() {
      // 重置分页
      this.subjectCurrentPage = 1;
      this.hasMoreSubjects = true;

      // 如果有关键词，则通过API搜索
      this.getSubjectList();
    },

    // 选择主体
    selectSubject(subject) {
      if (!subject) return;

      // 保存当前的渠道选择
      const originalChannel = this.currentChannel;

      // 根据图片中的数据结构，更新当前选中的主体
      this.currentDetail = {
        ...subject,
        // 确保使用正确的许可证编号字段 (xkzbh)
        licenseNumber: subject.xkzbh || subject.licenseNumber,
        // 确保使用正确的单位名称字段 (dwmc)
        unitName: subject.dwmc || subject.unitName,
        // 保留原有的 channel 属性
        channel: originalChannel
      };

      // 获取渠道数据
      this.getChannelData();

      // 更新 URL 参数
      this.updateUrlParams();
    },

    // 更新 URL 参数
    updateUrlParams() {
      // 只有在浏览器环境下才执行
      if (typeof window === 'undefined') return;

      // 获取当前的 URL 参数
      const query = {
        ...this.$route.query,
        licenseNumber: this.currentDetail.licenseNumber || this.currentDetail.xkzbh
        // 移除 unitName 参数，因为它在 API 请求中没有被使用
      };

      // 始终使用当前选中的渠道更新 URL 参数
      if (this.currentChannel) {
        query.channel = this.currentChannel;
      }

      // 更新 URL 参数，但不触发路由变化
      this.$router.replace({
        path: this.$route.path,
        query
      }).catch(err => {
        // 忽略重复导航的错误
        if (err.name !== 'NavigationDuplicated') {
          console.error('URL 更新失败', err);
        }
      });
    },

    // 切换渠道
    changeChannel(channel) {
      this.currentChannel = channel;
      this.getChannelList();

      // 更新 URL 参数
      this.updateUrlParams();
    },

    // 获取渠道数据
    getChannelData() {
      this.loading = true;

      // 构建渠道计数对象
      this.channelCounts = {};
      Object.keys(channelTypes).forEach(channel => {
        const countKey = this.getChannelCountKey(channel);
        this.channelCounts[channel] = this.currentDetail[countKey] || 0;
      });

      // 保存当前的渠道选择
      const originalChannel = this.currentChannel;

      // 如果已经有选中的渠道，并且URL中没有指定渠道，则保持当前渠道
      if (originalChannel && !this.$route.query.channel) {
        this.currentChannel = originalChannel;
      }
      // 如果URL中指定了渠道，优先使用该渠道
      else if (this.$route.query.channel && this.channelCounts[this.$route.query.channel] !== undefined) {
        this.currentChannel = this.$route.query.channel;
      }
      // 如果当前主体指定了渠道，使用主体的渠道
      else if (this.currentDetail.channel && this.channelCounts[this.currentDetail.channel] !== undefined) {
        this.currentChannel = this.currentDetail.channel;
      }
      // 否则设置默认选中的渠道（有数据的第一个渠道）
      else {
        const firstChannelWithData = Object.keys(this.channelCounts).find(channel => this.channelCounts[channel] > 0);
        this.currentChannel = firstChannelWithData || Object.keys(channelTypes)[0];
      }

      this.getChannelList();
    },

    // 获取渠道列表数据
    getChannelList(page = 1) {
      this.loading = true;
      this.channelList = [];
      this.currentPage = page;

      const params = {
        licenseNumber: this.currentDetail.licenseNumber || this.currentDetail.xkzbh,
        channel: this.currentChannel,
        pageSize: this.pageSize,
        pageNo: this.currentPage // 使用pageNo作为页码参数
      };

      // 根据不同的渠道类型选择不同的API接口
      let apiEndpoint = '/resourceslibrary/otherList';

      this.$http.get(apiEndpoint, { params }).then(res => {
        if (res.body.status === 0) {
          const list = res.body.data.list || [];
          // 为每个项目添加默认的舆论场值，确保数据类型为字符串
          this.channelList = list.map(item => ({
            ...item,
            situation: item.situation ? String(item.situation) : null
          }));
          this.total = res.body.data.count || 0;
        } else {
          this.$Message.error(res.body.message || '获取数据失败');
        }
        this.loading = false;
      }).catch(err => {
        console.error('获取渠道列表数据失败', err);
        this.$Message.error('获取数据失败');
        this.loading = false;
      });
    },

    // 切换页码
    changePage(page) {
      this.getChannelList(page);
    },

    // 切换每页显示条数
    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1;
      this.getChannelList(1);
    },

    // 获取列的 CSS 类名
    getColumnClass(key) {
      const classMap = {
        '序号': 'number-cell',
        'unitName': 'unit-name-cell',
        'licenseNumber': 'license-number-cell',
        'serviceName': 'service-name-cell',
        'serviceAddress': this.currentChannel === '公众账号' ? 'platform-cell' : 'service-address-cell',
        'publicOpinion': 'public-opinion-cell'
      };

      return classMap[key] || '';
    },

    // 获取分组的起始索引
    getGroupStartIndex(groupIndex) {
      let startIndex = 0;
      for (let i = 0; i < groupIndex; i++) {
        startIndex += this.groupedChannelList[i].items.length;
      }
      return startIndex;
    },

    // 获取渠道对应的计数字段
    getChannelCountKey(channel) {
      const keyMap = {
        "互联网站": "hlwz",
        "论坛": "lt",
        "博客": "bk",
        "微博客": "wbk",
        "公众账号": "gzzh",
        "应用程序": "yycx",
        "网络直播": "wlzb",
        "其他": "qt"
      };

      return keyMap[channel] || "";
    },

    // 获取渠道对应的列定义
    getColumnsForChannel(channel) {
      return this.channelColumnsMap[channel] || [];
    },

    // 获取分组的起始索引
    getGroupStartIndex(groupIndex) {
      let startIndex = 0;
      // 计算之前所有分组的项目数量总和
      for (let i = 0; i < groupIndex; i++) {
        startIndex += this.groupedChannelList[i].items.length;
      }
      return startIndex;
    },

    // 处理舆论场变化
    async handleSituationChange(item, value) {
      console.log('舆论场变化:', item.id, value);

      try {
        const params = {
          id: item.id,
          situation: value
        };

        // 使用GET请求调用舆论场更新接口
        const response = await this.$http.get('/resourceslibrary/updateSituation', { params });

        if (response.body.status === 0) {
          this.$Message.success(response.body.message || '舆论场信息修改成功');
          // 更新本地数据
          item.situation = value;
        } else {
          this.$Message.error(response.body.message || '修改失败');
          // 如果失败，重新获取数据以恢复原值
          this.getChannelList();
        }
      } catch (error) {
        console.error('修改舆论场失败:', error);
        this.$Message.error('修改失败');
        // 如果失败，重新获取数据以恢复原值
        this.getChannelList();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.ccol{
  display: flex;
  align-items: center;
  text-align: left;

  /deep/ .ivu-checkbox-wrapper {
    flex-shrink: 0;
    margin-right: 8px;
  }

  span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.detail-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  /* 表格样式 */
  .custom-table-container {
    width: 100%;
    background-color: #d7d7d7; /* 边框颜色 */
    overflow: hidden;
    padding: 0px;

    /* 分页样式 */
    /deep/ .ivu-page {
      text-align: right;
      padding: 10px;
    }
    .custom-table {
      width: 100%;
      table-layout: fixed; /* 固定表格布局 */
      border-collapse: separate;
      border-spacing: 1px;

      th, td {
        background-color: #fff;
        padding: 12px 8px;
        text-align: center;
        height: 45px; /* 固定高度 */
        box-sizing: border-box;
        height: 60px;
        font-size:16px;

      }

      th {
        font-weight: 600;
        background-color: #E6F7FF; /* 表头背景色 */
        color: #333;

      }

      /* 单元格样式 */
      .number-cell {
        width: 84px;
        text-align: center;
        box-sizing: border-box;
      }

      .unit-name-cell {
        width: 332px;
        vertical-align: middle; /* 垂直居中 */
        box-sizing: border-box;
      }

      .license-number-cell {
        width: 160px;
        box-sizing: border-box;
        vertical-align: middle; /* 垂直居中 */
      }

      .service-name-cell {

        width: auto; /* 自动分配剩余宽度 */
      }

      .service-address-cell {
        width: auto; /* 自动分配剩余宽度 */
      }

      .platform-cell {
        width: auto; /* 自动分配剩余宽度 */
      }

      .public-opinion-cell {
        width: 120px;
        padding: 8px;

        /deep/ .ivu-select {
          width: 100%;

          .ivu-select-selection {
            border: 1px solid #dcdee2;
            border-radius: 4px;
            height: 32px;

            .ivu-select-selected-value {
              font-size: 14px;
              line-height: 30px;
            }
          }
        }
      }
    }
  }



  .detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;



    .detail-title {
      margin-left: 24px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .detail-content {
    flex: 1;
    display: flex;

    .search-sidebar {
      width: 270px;
      min-width: 270px;
      border-right: 1px solid #e8e8e8;
      background: white;
      border-radius: 8px;
      padding: 10px;

      max-height: ~'calc(100vh  - 40px)';
      display: flex;
      flex-direction: column;

      .search-box {
        padding-bottom: 10px;

        /deep/ .ivu-input-wrapper {
          width: 100%;
        }
      }

      .subject-list {
        margin-top: 10px;
        flex: 1;
        height: ~'calc(100vh - 200px)';
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #ddd;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: #f5f5f5;
        }

        .subject-item {
          padding: 10px 8px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          font-size: 14px;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &:hover {
            background-color: #f9f9f9;
          }

          &.active {
            background-color: #f0f7ff;
            color: #5585ec;
            font-weight: 600;
          }
          .subject-item-content {
            display: flex;
            align-items: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            /deep/ .ivu-checkbox-wrapper {
              flex-shrink: 0;
            }

            span {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }

        .loading-more, .no-more {
          padding: 10px 0;
          text-align: center;
          color: #999;
          font-size: 12px;

          .demo-spin-icon-load {
            margin-right: 5px;
          }
        }
      }
    }

    .detail-main {
      flex: 1;
      margin-left: 16px;
      display: flex;
      flex-direction: column;
      background:white;
      border-radius: 8px;
      padding:10px 15px;

      .channel-tabs {
        display: flex;
        margin-bottom: 16px;
        overflow-x: auto;
        gap: 100px;
        .back-button {
          display: flex;
          align-items: center;
          color: #5585ec;
          cursor: pointer;
          font-size: 14px;

          .ivu-icon {
            margin-right: 4px;
          }

          &:hover {
            opacity: 0.8;
          }
        }
        .channel-tabs-cont{
          flex:1;
          display: flex;
        }

        .channel-tab {

          padding: 8px 16px;
          cursor: pointer;
          font-size: 14px;
          color: #666;
          white-space: nowrap;

          &:hover {
            color: #5585ec;
          }

          &.active {
            color: #5585ec;
            font-weight: 600;

          }
        }
      }

      .channel-content {
        flex: 1;
        position: relative;

        .no-data {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
          color: #999;
          font-size: 14px;
        }

        .pagination-container {
          margin-top: 16px;
          text-align: right;
        }
      }
    }
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
