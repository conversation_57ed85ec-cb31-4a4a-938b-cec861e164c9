<template>
  <div class="topic-detail-page">
    <DetailView
      :detail="currentDetail"
      :allSubjects="allSubjects"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import DetailView from "./components/DetailView.vue";

export default {
  data() {
    return {
      currentDetail: {}, // 当前查看的详情
      allSubjects: [] // 所有主体列表，用于详情页左侧搜索
    };
  },
  components: {
    DetailView
  },
  created() {
    // 从路由参数获取详情信息
    if (this.$route.query.licenseNumber) {
      this.currentDetail = {
        licenseNumber: this.$route.query.licenseNumber,
        unitName: '', // 初始化为空字符串，稍后会通过 API 获取
        channel: this.$route.query.channel || '互联网站'
      };

      // 始终获取单位详细数据，包括单位名称
      this.getUnitDetailData(this.$route.query.licenseNumber);
    }

    // 获取所有主体列表
    this.getAllSubjects();
  },
  methods: {
    // 返回列表
    goBack() {
      this.$router.push({
        path: '/main/configMgmt/mediaConfig/businessPlatformConfig'
      });
    },

    // 获取所有主体列表（用于详情页左侧搜索）
    getAllSubjects() {
      this.$http.get('/resourceslibrary/mainBodyList', { params: { pageSize: 50 } }).then(res => {
        if (res.body.status === 0) {
          this.allSubjects = res.body.data.list || [];
        }
      }).catch(err => {
        console.error('获取主体列表失败', err);
      });
    },

    // 获取单位详细数据
    getUnitDetailData(licenseNumber) {
      const params = {
        applicationSubject: '',
        mediaClassification: '',
        searchType: '2', // 按许可证编号查找
        keyWord: licenseNumber
      };

      this.$http.get('/resourceslibrary/mainBodyList', { params }).then(res => {
        if (res.body.status === 0 && res.body.data.list && res.body.data.list.length > 0) {
          // 找到匹配的单位数据
          const unitData = res.body.data.list[0];

          // 更新当前详情数据，包含各渠道的数量
          this.currentDetail = {
            ...this.currentDetail,
            hlwz: unitData.hlwz || 0,
            lt: unitData.lt || 0,
            bk: unitData.bk || 0,
            wbk: unitData.wbk || 0,
            gzzh: unitData.gzzh || 0,
            yycx: unitData.yycx || 0,
            wlzb: unitData.wlzb || 0,
            qt: unitData.qt || 0,
            unitName: unitData.dwmc || this.currentDetail.unitName,
            mtfl: unitData.mtfl || ''
          };
        }
      }).catch(err => {
        console.error('获取单位详细数据失败', err);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.topic-detail-page {
  height:~"calc(100vh - 40px)";
  margin:20px 20px 20px 0;
}
</style>
