<template>
  <div class="topic-details">
    <!-- <titleChange @handleTitle="changeTitle" :titleList="titleList" /> -->
    <!-- 主体详情列表 -->
    <div class="list-view flex">
      <TopicList
        :queryParams="queryParams"
        ref="topicList"
      />
      <TopicFilters @query="handleQuery" />
    </div>
  </div>
</template>

<script>
import titleChange from "../../../publicOpinionTips/components/title.vue";
import TopicFilters from "../components/mediaConfigFilters.vue";
import TopicList from "../components/mediaConfigList.vue";

export default {
  name: "MediaConfig",
  data() {
    return {
      titleList: [
          { id: 1, name: "媒体平台配置" },
      ],
      queryParams: {}, // 查询参数
      isUpdatingUrl: false // 标记是否正在更新URL，避免重复调用
    };
  },
  components: {
    titleChange,
    TopicFilters,
    TopicList
  },
  created() {
    // 从URL参数初始化查询参数
    const query = this.$route.query;
    if (Object.keys(query).length > 0) {
      this.queryParams = { ...query };
    }
    this.getLog('配置管理/事件分析配置/媒体平台配置','浏览');
  },
  methods: {
    changeTitle(val) {
      console.log(val);
    },
    // 处理查询
    handleQuery(params) {
      // 设置标志位，表示正在处理查询
      this.isUpdatingUrl = true;

      // 更新查询参数
      this.queryParams = { ...params };

      // 更新URL参数，但不触发路由变化
      this.updateUrlParams();

      // 重置标志位
      setTimeout(() => {
        this.isUpdatingUrl = false;
      }, 100);
    },

    // 更新URL参数
    updateUrlParams() {
      // 构建新的URL参数
      const urlParams = new URLSearchParams();

      // 添加查询参数
      for (const key in this.queryParams) {
        if (this.queryParams[key]) {
          urlParams.set(key, this.queryParams[key]);
        }
      }

      // 使用History API更新URL，不触发路由变化
      const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
      window.history.replaceState(null, '', newUrl);
    }
  }
};
</script>

<style lang="less" scoped>
.topic-details {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  .list-view {
    height: 100%;
    flex: 1;
  }
}
</style>
