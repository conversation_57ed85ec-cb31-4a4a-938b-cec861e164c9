<template>
  <div class="remark flex">
    <div class="item" v-if="mtypeTow != 2">{{ data.mpublishTime }}</div>
    <!-- 平台 -->
    <div class="item" v-if="data.situations">
      <svg-icon icon-class="媒体类型" />{{ situations[data.situation] }}
    </div>
    <!-- 媒体 -->
    <div
      class="item"
      v-if="!handleSource && situations[data.situation] !== data.mwebsiteName"
    >
      {{ data.mwebsiteName }}
    </div>
    <!-- 用户 -->
    <div
      class="item ellipsis cp"
      style="max-width: 130px;display: flex;align-items: center;"
      v-if="(data.uname || (baseData && baseData.mrootUname)) && mtypeTow != 2"
      @click="accountDetails"
    >
      <div
        class="ellipsis"
        :title="
          baseData && baseData.mtype === 3
            ? data.uname || baseData.mrootUname
            : data.uname
        "
        style="flex: 1;overflow: hidden;"
      >
        <Icon type="md-person" />{{
          baseData && baseData.mtype === 3
            ? data.uname || baseData.mrootUname
            : data.uname
        }}
        <div style="display: inline-block;">
            <span class="ui_tab_mei" v-if="data.accountTypeMark=='媒'"></span>
            <span class="ui_tab_shang" v-if="data.accountTypeMark=='商'"></span>
            <span class="ui_tab_zi" v-if="data.accountTypeMark=='自'"></span>
          </div>
      </div>
      <svg-icon icon-class="电话" style="flex-shrink: 0;margin-left: 2px;" v-if="data.hasYinsi == 1" />
    </div>
    <!-- 发布于 -->
    <div
      class="item ellipsis cp"
      style="max-width: 130px;"
      v-if="data.mlocIssue && data.mareaIssue"
    >
      <div class="ellipsis" :title="data.mlocIssue + ' ' + data.mareaIssue">
        <svg-icon icon-class="location" />
        {{ data.mlocIssue + " " + data.mareaIssue }}
      </div>
    </div>
    <div
      v-if="(!data.mlocIssue || !data.mareaIssue) && data.mlocIp"
      class="item"
    >
      {{ data.mlocIp }}
    </div>
    <div v-if="data.murl || (baseData && baseData.mrootUrl)" class="flex url" style="width: 600px;">
      <div
        class="ellipsis"
        :title="
          baseData && baseData.mtype === 3
            ? data.murl || baseData.mrootUrl
            : data.murl
        "
        style="margin-right: 10px; max-width: 50%;"
      >
        {{
          baseData && baseData.mtype === 3
            ? data.murl || baseData.mrootUrl
            : data.murl
        }}
      </div>
      <div
        class="controlsBtn cp"
        @click.stop="
          copyUrl(
            $event,
            baseData && baseData.mtype === 3
              ? data.murl || baseData.mrootUrl
              : data.murl
          )
        "
      >
        复制
      </div>
      <div
        class="qrcode-container"
        @mouseenter="showQRCode(
          baseData && baseData.mtype === 3
            ? data.murl || baseData.mrootUrl
            : data.murl
        )"
        @mouseleave="hideQRCode"
      >
        <svg-icon icon-class="二维码" class="qrcode-icon cp" />
        <div v-if="showQrCode && qrCodeDataUrl" class="qrcode-popup">
          <img :src="qrCodeDataUrl" alt="二维码" />
        </div>
      </div>
    </div>
    <div class="item" style="margin-left: auto;" v-if="data.msgChannelId">
      数据来源：{{ data.msgChannelId | source }}
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import situations from "@/assets/json/situations.json";
import QRCode from "qrcode";
export default {
  data() {
    // 这里存放数据
    return {
      situations,
      qrCodeDataUrl: '', // 二维码图片数据
      showQrCode: true // 是否显示二维码
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    data: {},
    baseData: {
      default: null,
    },
    mtypeTow: {
      default: null,
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    accountDetails() {
      if (!this.data.ukey) {
        const { href } = this.$router.resolve({
          path: "/main/archiveDetail",
          query: {
            mkey: this.$route.query.msgKey,
            situation: this.data.situation,
            uname: encodeURIComponent(this.data.uname),
            mwebsiteName: encodeURIComponent(this.data.mwebsiteName),
          },
        });
        window.open(href, "_blank");
        return false;
      }
      const { href } = this.$router.resolve({
        path: "/main/archiveDetail",
        query: {
          ukey: this.data.ukey,
          // uname: this.data.uname,
          situation: this.$route.query.situation,
           uname: encodeURIComponent(this.data.uname),
          mwebsiteName: encodeURIComponent(this.data.mwebsiteName),
        },
      });
      window.open(href, "_blank");
    },
    // 生成二维码
    async generateQRCode(text) {
      try {
        const qrCodeDataUrl = await QRCode.toDataURL(text, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        this.qrCodeDataUrl = qrCodeDataUrl;
      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    },
    // 鼠标悬浮显示二维码
    async showQRCode(text) {
      await this.generateQRCode(text);
      this.showQrCode = true;
    },
    // 鼠标离开隐藏二维码
    hideQRCode() {
      this.showQrCode = false;
    }
  },
  // 计算属性 类似于 data 概念
  computed: {
    handleSource() {
      let arr = [10, 20, 80, 170];
      return arr.includes(this.data.situation);
    },
  },
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {
    source(d) {
      const mapperList = [
        {
          name: "拓尔思",
          value: "trs",
        },
        {
          name: "智慧星光",
          value: "zhxg",
        },
        {
          name: "大众网",
          value: "dzw",
        },
        {
          name: "舜网",
          value: "sw",
        },
        {
          name: "蜜度",
          value: "mid",
        },
        {
          name: "网宣",
          value: "wx",
        },
        {
          name: "美扮",
          value: "mb",
        },
      ];
      const mapper = mapperList.find((item) => d.indexOf(item.value) > -1);
      if (mapper) {
        return mapper.name;
      } else {
        return "-";
      }
    },
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.remark {
  align-items: center;
  line-height: 20px;
  color: #666666;
  font-size: 14px;
  .url {
    color: #5585ec;
    align-items: center;
    // max-width: 600px;

    .controlsBtn {
      height: 18px;
      border: 1px solid #5585ec;
      border-radius: 2px;
      line-height: 18px;
      color: #5585ec;
      font-size: 12px;
      margin-right: 10px;
      text-align: center;
      padding: 0 2px;
    }

    .qrcode-container {
      position: relative;
      display: inline-block;
      margin-left: 5px;

      .qrcode-icon {
        width: 18px;
        height: 18px;
        color: #5585ec;
        cursor: pointer;
        transition: color 0.3s ease;
        display: block;
        &:hover {
          color: #3a6bd8;
        }
        border:1px solid #5585ec;
        border-radius: 2px;
      }

      .qrcode-popup {
        position: absolute;
        top: 0px;
        left: 40px;
        // transform: translateX(-50%);
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 1000;

        // &::after {
        //   content: '';
        //   position: absolute;
        //   top: 100%;
        //   left: 50%;
        //   transform: translateX(-50%);
        //   border: 6px solid transparent;
        //   border-top-color: white;
        //   background:red;
        // }

        img {
          display: block;
          width: 180px;
          height: 180px;
        }
      }
    }
  }

  .item {
    margin-right: 15px;
  }
}
</style>
