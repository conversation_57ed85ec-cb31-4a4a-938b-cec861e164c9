<template>
    <div class="informationProce">
        <div class="proce_content_box">
            <div class="header">
                <div class="operation">
                    <Checkbox
                        :indeterminate="indeterminate"
                        :value="checkAll"
                        @click.prevent.native="handleCheckAll"
                        >全选
                    </Checkbox>
                    <div class="btn" @click="derive">
                        <svg-icon icon-class="信息库-导出" />
                        按选中导出
                    </div>
                    <div class="btn" @click="deriveFilter">
                        <svg-icon icon-class="信息库-导出" />
                        按筛选条件导出
                    </div>
                </div>
                <div class="proce_operation_right">
                    <sorts
                        :dataList="orderByTypeList"
                        @changeSort="changeSort"
                    ></sorts>
                    <Input
                        v-model="params.keyword"
                        placeholder="请输入关键词"
                        search
                        style="width: 300px"
                        @on-search="handleSearch"
                    >
                        <Select
                            v-model="params.searchPosition"
                            slot="prepend"
                            style="width: 80px"
                        >
                            <Option value="1">全文</Option>
                            <Option value="2">标题</Option>
                            <Option value="3">正文</Option>
                        </Select></Input
                    >
                    <div>共<span class="blue">{{ total }}</span>条</div>
                </div>
            </div>
            <div class="list_box">
                <Spin v-show="loading" fix>
                <Icon
                    type="ios-loading"
                    size="18"
                    class="demo-spin-icon-load"
                ></Icon>
                <div>Loading</div>
            </Spin>
                <div class="list_box_left">
                    <div class="list">
                        
                        <no-data v-show="total === 0 && !loading" />
                        <CheckboxGroup
                            v-model="checkAllGroup"
                            @on-change="checkAllGroupChange"
                        >
                            <MonitoringInfoList
                                v-for="(item, index) in tableData"
                                :key="item.mkey"
                                :data="item"
                                :index="index"
                                :pageNo="pageNo"
                                :pageSize="pageSize"
                            >
                            </MonitoringInfoList>
                        </CheckboxGroup>
                    </div>
                    <Page
                        v-show="total > 0 && !loading"
                        :total="total"
                        show-elevator
                        show-total
                        show-sizer
                        style="position: relative"
                        :current="pageNo"
                        :page-size="pageSize"
                        @on-change="pageNoChange"
                        @on-page-size-change="pageSizeChange"
                    />
                </div>
                <div class="list_box_right">
                    <div class="chart_box" ref="chart"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import MonitoringInfoList from "./infoList.vue";
import ListControls from "@/components/listControls/index.vue";
import sorts from "./sorts.vue";
import moment from "moment";
import echarts from "echarts";
export default {
    name: "informationProce",
    components: {
        MonitoringInfoList,
        ListControls,
        sorts,
        msgAccount: {}
    },
    props: {
        infoData: {
            type: Object,
            default: () => ({}),
        },
        dayNum: {
            type: String,
            default: () => "-1",
        },
        dateList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            loading: false,
            total: 0,
            pageNo: 1,
            pageSize: 50,
            tableData: [],
            indeterminate: false,
            checkAll: false,
            checkAllGroup: [],
            chart: null,
            orderByTypeList: [
                {
                    name: "发布时间",
                    key: "1",
                },
                {
                    name: "发布时间",
                    key: "2",
                },
            ],
            params: {
                keyword: "",
                searchPosition: "1",
                orderByType: "1",
            },
            searchType: '',
        };
    },

    methods: {
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;

            if (this.checkAll) {
                this.checkAllGroup = this.tableData.map((i) => i.mkey);
            } else {
                this.checkAllGroup = [];
            }
        },
        checkAllGroupChange(value) {
            if (value.length === this.tableData.length) {
                this.indeterminate = false;
                this.checkAll = true;
            } else if (value.length > 0) {
                this.indeterminate = true;
                this.checkAll = false;
            } else {
                this.indeterminate = false;
                this.checkAll = false;
            }
        },
        pageNoChange(pageNo) {
            this.pageNo = pageNo;
            this.getTableData();
        },
        pageSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.getTableData();
        },
        handleSearch() {
        console.log("111111111")
            this.pageNo = 1;
            this.getTableData();
            this.getChartData();
        },
        changeSort(item) {
            this.params.orderByType = item.key;
            this.getTableData();
        },
        getTableData() {
            this.checkAll = false;
            this.checkAllGroup = [];
            this.indeterminate = false;
            this.loading = true;
            this.tableData = [];
            let params = {
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                id: this.infoData.id,
                ...this.params,
                searchType: this.searchType,
                ukey: this.$route.query.ukey,
            };
            params.dayNum = this.dayNum;
            if (this.dayNum == "-99") {
                params.startTime = moment(this.dateList[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                params.endTime = moment(this.dateList[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            this.$http
                .get("/media/informationList", { params })
                .then((res) => {
                    if (res.body.status === 0) {
                        this.tableData = res.body.data.list || [];
                        this.total = res.body.data.total || 0;
                        
                    } else {
                        // this.$Message.error("服务器错误！");
                    }
                    this.loading = false;
                });
        },
        getChartData() {
            let params = {
              
                id: this.infoData.id,
                ...this.params,
                ukey: this.$route.query.ukey,
            };
            params.dayNum = this.dayNum;
            if (this.dayNum == "-99") {
                params.startTime = moment(this.dateList[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                params.endTime = moment(this.dateList[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            this.$http.get("/media/informationCount", {
                params,
            }).then((res) => {
                if (res.body.status === 0) {
                    this.msgAccount = res.body.data.msgAccount;
                    this.setChart3(this.msgAccount);
                }
            });
        },
        //导出
        derive() {
            if (this.checkAllGroup.length === 0) {
                return this.$Message.warning("请选择信息后重试！");
            }
            let params = {
                mkeys: this.checkAllGroup.toString(),
                id: this.infoData.id,
                ...this.params,
                searchType: this.searchType,
            };
            params.dayNum = this.dayNum;
            if (this.dayNum == "-99") {
                params.startTime = moment(this.dateList[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                params.endTime = moment(this.dateList[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            this.$http
                .get("/media/exportInfoList", {
                    params,
                    emulateJSON: true,
                    responseType: "blob",
                })
                .then((res) => {
                    const disposition = res.headers.get("Content-Disposition");
                    let filename = "downloaded_file";
                    if (
                        disposition &&
                        disposition.indexOf("attachment") !== -1
                    ) {
                        const filenameRegex =
                            /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                        const matches = filenameRegex.exec(disposition);
                        if (matches != null && matches[1]) {
                            filename = matches[1].replace(/['"]/g, "");
                            filename = decodeURIComponent(escape(filename)); // 解码文件名
                        }
                    }
                    const blob = new Blob([res.body]);
                    const a = document.createElement("a");
                    a.href = URL.createObjectURL(blob);
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();

                    setTimeout(() => {
                        document.body.removeChild(a);
                        URL.revokeObjectURL(a.href);
                    }, 0);
                });
          this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】导出文件标题');
        },
        deriveFilter() {
            let params = {
                ...this.params,
                id: this.infoData.id,
                searchType: this.searchType,
            };
            params.dayNum = this.dayNum;
            if (this.dayNum == "-99") {
                params.startTime = moment(this.dateList[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                params.endTime = moment(this.dateList[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            this.$http
                .get("/media/exportInfoList", {
                    params,
                    emulateJSON: true,
                    responseType: "blob",
                })
                .then((res) => {
                    const disposition = res.headers.get("Content-Disposition");
                    let filename = "downloaded_file";
                    if (
                        disposition &&
                        disposition.indexOf("attachment") !== -1
                    ) {
                        const filenameRegex =
                            /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                        const matches = filenameRegex.exec(disposition);
                        if (matches != null && matches[1]) {
                            filename = matches[1].replace(/['"]/g, "");
                            filename = decodeURIComponent(escape(filename)); // 解码文件名
                        }
                    }
                    const blob = new Blob([res.body]);
                    const a = document.createElement("a");
                    a.href = URL.createObjectURL(blob);
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();

                    setTimeout(() => {
                        document.body.removeChild(a);
                        URL.revokeObjectURL(a.href);
                    }, 0);
                });
          this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】导出文件标题');
        },
        setChart3(data) {
            let option = {
                animation: false,
                graphic: [
                    {
                        type: 'text',
                        left: '18%',
                        top: '45%',
                        style: {
                            text: '已删除信息',
                            // textAlign: 'center',
                            fill: '#333',
                            fontSize: 16,
                            cursor: 'pointer',
                            fontWeight: 'bold',
                   
                        },
                        onclick: () => {
                            handleTitleClick(1);
                        }
                    },
                    {
                        type: 'text',
                        left: '70%',
                        top: '45%',
                        style: {
                            text: '打标推送',
                            fill: '#333',
                            fontSize: 16,
                            cursor: 'pointer',
                            fontWeight: 'bold',
                        },
                        onclick: () => {
                            handleTitleClick(2);
                        }
                    },
                    {
                        type: 'text',
                        left: '18%',
                        top: '95%',
                        style: {
                            text: '提示单引用',
                            fill: '#333',
                            fontSize: 16,
                            cursor: 'pointer',
                            fontWeight: 'bold',
                        },
                        onclick: () => {
                            handleTitleClick(3);
                        }
                    },
                    {
                        type: 'text',
                        left: '70%',
                        top: '95%',
                        style: {
                            text: '要报引用',
                            fill: '#333',
                            fontSize: 16,
                            cursor: 'pointer',
                            fontWeight: 'bold',
                        },
                        onclick: () => {
                            handleTitleClick(4);
                        }
                    }
                ],
                series: [
                    // 删除信息（红色）
                    {
                        title: "已删除信息",
                        type: "pie",
                        radius: ["35%", "30%"],
                        center: ["25%", "25%"],
                        label: {
                            formatter: (val) => {
                                if (val.name !== "总数") {
                                    return `{val|${val.value}}{unit|条}`;
                                }
                                return "";
                            },
                            rich: {
                                val: {
                                    fontSize: 30,
                                    fontweight: "bold",
                                    color: "#333",
                                    verticalAlign: "bottom",
                                },
                                unit: {
                                    fontSize: 14,
                                    color: "#333",
                                    verticalAlign: "center",
                                },
                            },
                            position: "center",
                        },
                        hoverAnimation: false,
                        data: [
                            {
                                value: data.delTotal,
                                name: "已删除信息",
                                itemStyle: { color: "#ff0000" },
                            },
                            {
                                value: data.score,
                                name: "总数",
                                itemStyle: { color: "#D9E3ED" },
                            },
                        ],
                        avoidLabelOverlap: true,
                    },
                    // 打标信息（蓝色）
                    {
                        type: "pie",
                        radius: ["35%", "30%"],
                        center: ["75%", "25%"],
                        label: {
                            formatter: (val) => {
                                if (val.name !== "总数") {
                                    return `{val|${val.value}}{unit|条}`;
                                }
                                return "";
                            },
                            rich: {
                                val: {
                                    fontSize: 30,
                                    fontweight: "bold",
                                    color: "#333",
                                    verticalAlign: "bottom",
                                },
                                unit: {
                                    fontSize: 14,
                                    color: "#333",
                                    verticalAlign: "center",
                                },
                            },
                            position: "center",
                        },
                        data: [
                            {
                                value: data.allTotal,
                                name: "打标信息",
                                itemStyle: { color: "#2c7df3" },
                            },
                            {
                                value: data.score,
                                name: "总数",
                                itemStyle: { color: "#D9E3ED" },
                            },
                        ],
                        avoidLabelOverlap: true,
                    },
                    // 提示单引用（绿色）
                    {
                        type: "pie",
                        radius: ["35%", "30%"],
                        center: ["25%", "75%"],
                        label: {
                            formatter: (val) => {
                                if (val.name !== "总数") {
                                    return `{val|${val.value}}{unit|条}`;
                                }
                                return "";
                            },
                            rich: {
                                val: {
                                    fontSize: 30,
                                    fontweight: "bold",
                                    color: "#333",
                                    verticalAlign: "bottom",
                                },
                                unit: {
                                    fontSize: 14,
                                    color: "#333",
                                    verticalAlign: "center",
                                },
                            },
                            position: "center",
                        },
                        data: [
                            {
                                value: data.promptTotal,
                                name: "提示单引用",
                                itemStyle: { color: "#05ba97" },
                            },
                            {
                                value: data.score,
                                name: "总数",
                                itemStyle: { color: "#D9E3ED" },
                            },
                        ],
                        avoidLabelOverlap: true,
                    },
                    // 要报引用（橙色）
                    {
                        type: "pie",
                        radius: ["35%", "30%"],
                        center: ["75%", "75%"],
                        label: {
                            formatter: (val) => {
                                if (val.name !== "总数") {
                                    return `{val|${val.value}}{unit|条}`;
                                }
                                return "";
                            },
                            rich: {
                                val: {
                                    fontSize: 30,
                                    fontweight: "bold",
                                    color: "#333",
                                    verticalAlign: "bottom",
                                },
                                unit: {
                                    fontSize: 14,
                                    color: "#333",
                                    verticalAlign: "center",
                                },
                            },
                            position: "center",
                        },
                        data: [
                            {
                                value: data.periodsTotal,
                                name: "要报引用",
                                itemStyle: { color: "#f78e2b" },
                            },
                            {
                                value: data.score,
                                name: "总数",
                                itemStyle: { color: "#D9E3ED" },
                            },
                        ],
                        avoidLabelOverlap: true,
                    },
                ],
            };
            
            this.chart = echarts.init(this.$refs.chart);
            this.chart.setOption(option);
            const _this = this;
            function handleTitleClick(index) {
                if(_this.searchType === index) {
                    option.graphic.forEach((item, sindex) => {
                            item.style.fill = '#333';
                    })
                    _this.searchType = '';
                }else{
                    option.graphic.forEach((item, sindex) => {
                        if(sindex === index - 1) {
                            item.style.fill = '#2c7df3';
                        } else {
                            item.style.fill = '#333';
                        }
                    })
                    _this.searchType = index;
                }
                // 重新生成图表
                _this.chart.clear();
                _this.chart.setOption(option);
            }
        },
      
    },
    mounted() {
        // this.getTableData();
      this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】信息处置页浏览');
    },
    watch: {
        infoData: {
            handler(newVal) {
                // this.getTableData();
            },
            deep: true,
        },
        dayNum: {
            handler(newVal) {
                if (newVal != "-99") {
                    this.pageNo = 1;
                    this.getTableData();
                    this.getChartData();
                }
            },
            immediate: true,
        },
        dateList: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    this.pageNo = 1;
                    this.getTableData();
                    this.getChartData();
                }
            },
        },
        searchType: {
            handler(newVal) {
                this.pageNo = 1;
                this.getTableData();
            },
        },
    },
};
</script>

<style lang="less" scoped>
.informationProce {
    height: ~"calc(100vh - 380px)";
    // overflow: auto;
    // display: flex;
    // justify-content: space-between;
    // gap: 20px;
    .proce_content_box {
        height: 100%;
        background-color: #fff;
        padding: 20px 40px 20px 20px;
        border-radius: 8px;
        .header {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .btn {
                background: #5585ec;
                border-radius: 4px;

                padding: 0 18px;
                color: #fff;
                margin-right: 20px;
                cursor: pointer;
            }

            .operation {
                font-size: 16px;
                display: flex;
                align-items: center;

                .btn {
                    line-height: 30px;
                    height: 30px;
                }

                /deep/ .ivu-checkbox-wrapper {
                    font-size: 16px;
                    margin-right: 50px;
                    line-height: 40px;
                }

                /deep/ .ivu-checkbox-inner {
                    width: 16px;
                    height: 16px;
                }
            }
            .proce_operation_right {
                display: flex;
                gap: 20px;
                .blue{
                    color: #5585ec;
                }
            }
            .select {
                background: #fff;
                border-radius: 8px;
                font-family: PingFang SC;
                font-size: 14px;
                padding: 10px 20px;
                margin-bottom: 10px;
            }

            .playBack {
                line-height: 40px;
                height: 40px;
            }
        }

        .list_box {
            height: calc(~"100% - 30px");
            overflow: hidden;
            display: flex;
            gap: 20px;
            justify-content: space-between;
            position: relative;
        }
        .list_box_left {
            flex: 2.5;
            height: 100%;
            overflow: hidden;
        }
        .list_box_right {
            flex: 1;
            height: 100%;
            overflow: hidden;
            padding-bottom:30px;
            .chart_box{
                width: 100%;
                height: ~"calc(100% - 30px)";
            }
        }
        .list {
            height: calc(~"100% - 90px");
            position: relative;
            overflow-y: auto;
        }
    }
}
</style>
