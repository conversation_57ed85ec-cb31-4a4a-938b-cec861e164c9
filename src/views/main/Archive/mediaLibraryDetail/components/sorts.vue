<template>
  <div class="sort-content">
    <Dropdown trigger="click">
       <div class="select" @click="handShow">
      <span>{{ value }}</span>
      <svg-icon
        v-show="sortIndex == 1 || sortIndex == 3"
        icon-class="哨点-升序"
        style="width: 6.87px; height: 11.95px; margin-left: 8px"
      />
      <svg-icon
        v-show="sortIndex == 0 || sortIndex == 2"
        icon-class="哨点-降序"
        style="width: 6.87px; height: 11.95px; margin-left: 8px"
      />
       <span class="xian"></span>
       <span class="icons" :style="showFlag ? 'transform:rotate(180deg);top:2px;':''">
        <svg-icon
        icon-class="哨点-向下"
        style="width: 7.24px; height: 4.24px;"
      />
       </span>
       </div>
        <DropdownMenu slot="list">
          <div class="selet-list">
            <span
              v-for="(item, index) in dataList"
              :key="index"
              @click="handleValue(item, index)"
              class="lists"
              :style="
                value == item.name && index == sortIndex
                  ? 'color:#5585ec;background:#f3f3f3;'
                  : ''
              "
              >{{ item.name }}
              <svg-icon
                v-show="index == 1 || index == 3"
                icon-class="哨点-升序"
                style="width: 6.87px; height: 11.95px"
              />
              <svg-icon
                v-show="index == 0 || index == 2"
                icon-class="哨点-降序"
                style="width: 6.87px; height: 11.95px"
              />
            </span>
        </div>
        </DropdownMenu>
    </Dropdown>
  </div>
</template>


<script>
export default {
  props: {
    dataList: {
      default: [],
    },

  },
  data() {
    return {
      sortId: null,
      value: "",
      showFlag: false, //控制下拉框展示
      sortIndex: null,
      visible: false
    };
  },
  methods: {
    handleValue(data, index) {
      this.value = data.name;
      this.sortIndex = index;
      this.$emit('changeSort',data)
    },
    handShow() {
      this.showFlag = !this.showFlag;
    },
  },
  mounted() {
    this.value = this.dataList[0].name;
    this.sortIndex = 0;
  },
};
</script>
<style lang="less" scoped>
.sort-content {
  color: #333;
  font-size: 12px;
  position: relative;
  right: 0px;
  display: flex;
  justify-content: end;
  align-items: center;
  font-family:PingFang SC;
  .select {
    border: 1px solid #dcdee2;
    border-radius: 4px;
    width: 130px;
    height: 32px;
    line-height: 32px;
    margin-right: 14.22px;
    position: relative;
    padding-left: 10px;
    padding-right: 26.71px;
    text-align: center;
    // color: #5585ec;
    cursor: pointer;
    font-family:Source Sans Pro;
    .xian {
        position: absolute;
        display: inline-block;
        right: 26px;
        width: 1px;
        height: 26.71px;
        background: #5585ec;
    }
    .icons {
        position: absolute;
        right: 10.77px;
        top: -3px;
    }
   
  }
   .selet-list {
      background: #fff;
      color: #333;
      display: flex;
      flex-direction: column;
      width: 130px;
      box-shadow: 0px 5px 10px 0px #e9e9e9;
      
      .lists {
        height: 32px;
        line-height: 32px;
        width: 100%;
        text-align: center;
        cursor: pointer;
      }
      .lists:hover {
        background: #f3f3f3;
      }
    }
    /deep/.ivu-dropdown .ivu-select-dropdown {
      left: auto !important;
    }
}
</style>