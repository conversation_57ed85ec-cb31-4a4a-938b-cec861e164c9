<template>
    <div class="postTrajectory">
        <div class="proce_content_box">
            <div class="header">
                <div class="operation">
                    <Checkbox
                        :indeterminate="indeterminate"
                        :value="checkAll"
                        @click.prevent.native="handleCheckAll"
                        >全选
                    </Checkbox>
                   
                </div>
                <div class="proce_operation_right">
                    <sorts
                        :dataList="orderByTypeList"
                        @changeSort="changeSort"
                    ></sorts>
                    <Input
                        v-model="params.keyword"
                        placeholder="请输入关键词"
                        search
                        style="width: 300px"
                        @on-search="handleSearch"
                    >
                        <Select
                            v-model="params.searchPosition"
                            slot="prepend"
                            style="width: 80px"
                        >
                            <Option value="1">全文</Option>
                            <Option value="2">标题</Option>
                            <Option value="3">正文</Option>
                        </Select></Input
                    >
                </div>
            </div>
            <div class="list_box">
                <div class="fbd_box">
                    <div class="fbd_box_left">
                        <div class="map_box" ref="mapBox">

                        </div>
                    </div>
                    <div class="fbd_box_right">

                    </div>
                </div>
               
            </div>
        </div>
    </div>
</template>

<script>
import MonitoringInfoList from "./infoList.vue";
import ListControls from "@/components/listControls/index.vue";
import sorts from "./sorts.vue";
import moment from "moment";
import * as echarts from 'echarts'
import 'echarts/map/js/china'
export default {
    name: "postTrajectory",
    components: {
        MonitoringInfoList,
        ListControls,
        sorts,
        msgAccount: {}
    },
    props: {
        infoData: {
            type: Object,
            default: () => ({}),
        },
        dayNum: {
            type: String,
            default: () => "-1",
        },
        dateList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            loading: false,
            total: 0,
            pageNo: 1,
            pageSize: 10,
            tableData: [],
            indeterminate: false,
            checkAll: false,
            checkAllGroup: [],
            orderByTypeList: [
                {
                    name: "发布时间",
                    key: "1",
                },
                {
                    name: "发布时间",
                    key: "2",
                },
            ],
            params: {
                keyword: "",
                searchPosition: "1",
                orderByType: "1",
            },
            chart: null,
            mapData: [
                { name: '黑龙江', value: 6, itemStyle: { color: '#4CAF50' } },
                { name: '山东', value: 10, itemStyle: { color: '#FFC107' } },
                { name: '北京', value: 8, symbol: 'star', symbolSize: 20, itemStyle: { color: 'yellow' } }
            ],
            // 随机颜色数组，确保不重复
            colors: ['#FF9800', '#4CAF50', '#2196F3', '#9C27B0', '#E91E63', 
                    '#009688', '#795548', '#607D8B', '#3F51B5', '#CDDC39',
                    '#FFC107', '#FF5722', '#795548', '#9E9E9E', '#00BCD4']
        };
    },

    methods: {
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;

            if (this.checkAll) {
                this.checkAllGroup = this.tableData.map((i) => i.mkey);
            } else {
                this.checkAllGroup = [];
            }
        },
        checkAllGroupChange(value) {
            if (value.length === this.tableData.length) {
                this.indeterminate = false;
                this.checkAll = true;
            } else if (value.length > 0) {
                this.indeterminate = true;
                this.checkAll = false;
            } else {
                this.indeterminate = false;
                this.checkAll = false;
            }
        },
        pageNoChange(pageNo) {
            this.pageNo = pageNo;
            this.getTableData();
        },
        pageSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.getTableData();
        },
        handleSearch() {
            this.pageNo = 1;
            this.getTableData();
        },
        changeSort(item) {
            this.params.orderByType = item.key;
            this.getTableData();
        },
        getTableData() {
            this.checkAll = false;
            this.checkAllGroup = [];
            this.indeterminate = false;
            this.loading = true;
            this.tableData = [];
            let params = {
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                userId: this.infoData.userId,
                ...this.params,
            };
            params.dayNum = this.dayNum;
            if (this.dayNum == "-99") {
                params.startTime = moment(this.dateList[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                params.endTime = moment(this.dateList[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            this.$http
                .get("/media/informationList", { params })
                .then((res) => {
                    if (res.body.status === 0) {
                        this.tableData = res.body.data.list || [];
                        this.total = res.body.data.total || 0;
                    } else {
                        // this.$Message.error("服务器错误！");
                    }
                    this.loading = false;
                });
        },
        getListData(){
            let params = {
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                userId: this.infoData.userId,
                ...this.params,
            };
            params.dayNum = this.dayNum;
            if (this.dayNum == "-99") {
                params.startTime = moment(this.dateList[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                params.endTime = moment(this.dateList[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }
            this.$http.get("/media/trajectoryList",{params}).then((res)=>{
                if(res.body.status === 0){
                    this.tableData = res.body.data.list || [];
                }
            })
        },
        initMap() {
            if (this.chart) {
                this.chart.dispose()
            }
            this.chart = echarts.init(this.$refs.mapBox)
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}'
                },
                geo: {
                    map: 'china',
                    aspectScale: 0.75,
                    zoom: 1.2,
                    itemStyle: {
                        areaColor: '#B8E1FF',
                        borderColor: '#fff',
                        borderWidth: 1
                    },
                    emphasis: {
                        itemStyle: {
                            areaColor: '#bf7575'
                        }
                    }
                },
                series: [
                    {
                        name: '省份数据',
                        type: 'scatter',
                        coordinateSystem: 'geo',
                        data: this.convertData(this.mapData),
                        symbolSize: function(val) {
                            return val[2] === 8 ? 20 : 20; // 如果是北京（value为8）则显示五角星
                        },
                        symbol: function(val, params) {
                            const itemData = this.mapData.find(item => item.name === params.name);
                            return itemData && itemData.symbol ? itemData.symbol : 'circle';
                        }.bind(this),
                        label: {
                            formatter: '{b}',
                            position: 'right',
                            show: true,
                            color: '#333',
                            fontSize: 14
                        },
                        emphasis: {
                            label: {
                                show: true
                            }
                        }
                    }
                ]
            }
            
            this.chart.setOption(option)
            
            window.addEventListener('resize', () => {
                this.chart && this.chart.resize()
            })
        },
        
        convertData(data) {
            const geoCoordMap = {
                '黑龙江': [127.9688, 45.368],
                '山东': [117.1582, 36.8701],
                '北京': [116.4551, 40.2539],
                // 可以根据需要添加更多省份坐标
            }
            
            return data.map((item, index) => {
                const geoCoord = geoCoordMap[item.name]
                if (geoCoord) {
                    return {
                        name: item.name,
                        value: [...geoCoord, item.value],
                        itemStyle: item.itemStyle,
                        symbol: item.symbol,
                        symbolSize: item.symbolSize
                    }
                }
                return null
            }).filter(item => item !== null)
        }
    },
    mounted() {
        this.getListData()
        this.$nextTick(() => {
            this.initMap()
        })
    },
    beforeDestroy() {
        if (this.chart) {
            this.chart.dispose()
            this.chart = null
        }
        window.removeEventListener('resize', this.chart && this.chart.resize)
    },
    watch: {
        infoData: {
            handler(newVal) {
                // this.getTableData();
            },
            deep: true,
        },
        dayNum: {
            handler(newVal) {
                if (newVal != "-99") {
                    // this.pageNo = 1;
                    // this.getTableData();
                }
            },
            immediate: true,
        },
        dateList: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    this.pageNo = 1;
                    this.getTableData();
                }
            },
        },
    },
};
</script>

<style lang="less" scoped>
.postTrajectory {
    height: ~"calc(100vh - 350px)";
    // overflow: auto;
    // display: flex;
    // justify-content: space-between;
    // gap: 20px;
    .proce_content_box {
        height: 100%;
        background-color: #fff;
        padding: 20px 40px 20px 20px;
        border-radius: 8px;
        .header {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .btn {
                background: #5585ec;
                border-radius: 4px;

                padding: 0 18px;
                color: #fff;
                margin-right: 20px;
                cursor: pointer;
            }

            .operation {
                font-size: 16px;
                display: flex;
                align-items: center;

                .btn {
                    line-height: 30px;
                    height: 30px;
                }

                /deep/ .ivu-checkbox-wrapper {
                    font-size: 16px;
                    margin-right: 50px;
                    line-height: 40px;
                }

                /deep/ .ivu-checkbox-inner {
                    width: 16px;
                    height: 16px;
                }
            }
            .proce_operation_right {
                display: flex;
                gap: 20px;
            }
            .select {
                background: #fff;
                border-radius: 8px;
                font-family: PingFang SC;
                font-size: 14px;
                padding: 10px 20px;
                margin-bottom: 10px;
            }

            .playBack {
                line-height: 40px;
                height: 40px;
            }
        }

        .list_box {
            height: calc(~"100% - 40px");
            overflow-y: auto;
            .fbd_box{
                height: 400px;
                display: flex;
                gap: 20px;
                .fbd_box_left{
                    flex: 1;
                    height: 100%;
                    overflow: hidden;
                    .map_box{
                        width: 100%;
                        height: 100%;
                    }
                }
                .fbd_box_right{
                    flex: 1;
                    height: 100%;
                    overflow: hidden;
                }
            }
        }

    }
}
</style>
