<template>
    <!-- 信息完善 -->
    <div class="index xxws">
        <div class="content">
            <!-- 添加加载状态显示 -->
            <div v-if="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">数据加载中...</div>
            </div>

            <div v-else>
                <div class="form_group">
                    <div class="form left">
                        <div class="form_name">账号信息</div>
                        <div class="form_box">
                            <div class="form-item">
                                <div class="label">昵称：</div>
                                <div class="value">
                                    <Input
                                        disabled
                                        v-model="userData.accountName"
                                        placeholder="请输入昵称" />
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">账号ID：</div>
                                <div class="value">
                                   
                                    <Input
                                        :disabled="
                                            data.userId!==''
                                        "
                                        v-model="userData.userId"
                                        placeholder="请输入账号ID" />
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">平台：</div>
                                <div class="value">
                                    <Input
                                        disabled
                                        v-model="userData.mediumName"
                                        placeholder="请输入平台" />
                                </div>
                            </div>

                            <div class="form-item">
                                <div class="label">注册地：</div>
                                <div class="value">
                                    <Input
                                        v-model="userData.registerAddress"
                                        placeholder="请输入注册地" />
                                </div>
                            </div>

                            <div class="form-item">
                                <div class="label">粉丝数：</div>
                                <div class="value">
                                    <Input
                                        oninput="this.value=this.value.replace(/[^\d]/g,'').replace(/^0+(?=\d)/,'')"
                                        @on-keypress="onlyAllowNumbers"
                                        type="number"
                                        v-model="userData.fansCount"
                                        placeholder="请输入粉丝数" />
                                </div>
                            </div>

                            <div class="form-item">
                                <div class="label">关注数：</div>
                                <div class="value">
                                    <Input
                                        oninput="this.value=this.value.replace(/[^\d]/g,'').replace(/^0+(?=\d)/,'')"
                                        type="number"
                                        @on-keypress="onlyAllowNumbers"
                                        v-model="userData.followCount"
                                        placeholder="请输入关注数" />
                                </div>
                            </div>

                            <div class="form-item">
                                <div class="label">主页链接：</div>
                                <div class="value">
                                    <Input
                                        v-model="userData.url"
                                        placeholder="请输入主页链接" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form right">
                        <div class="form_name">人物信息</div>
                        <div class="form_box">
                            <div class="form-item">
                                <div class="label">姓名：</div>
                                <div class="value">
                                    <template
                                        v-if="!data.name || data.name == ''">
                                        <Input
                                            v-model="userData.name"
                                            placeholder="请输入姓名" />
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.name"
                                            placeholder="请输入姓名" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有姓名信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">性别：</div>
                                <div class="value">
                                    <template
                                        v-if="!data.sex || data.sex == ''">
                                        <RadioGroup v-model="userData.sex">
                                            <Radio label="男">男</Radio>
                                            <Radio label="女">女</Radio>
                                        </RadioGroup>
                                    </template>
                                    <template v-else-if="isApply">
                                        <RadioGroup v-model="userData.sex" disabled>
                                            <Radio label="男">男</Radio>
                                            <Radio label="女">女</Radio>
                                        </RadioGroup>
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有性别信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">身份证号：</div>
                                <div class="value value_phone">
                                    <template
                                        v-if="
                                            !data.idCard || data.idCard == ''
                                        ">
                                        <Input
                                          
                                            v-model="userData.idCard"
                                            :maxlength="18"
                                            placeholder="请输入身份证号" />
                                        <div
                                            class="phone-validate"
                                            v-if="showIdCardError">
                                            身份证号格式不正确
                                        </div>
                                    </template>

                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.idCard" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有身份证号信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">出生日期：</div>
                                <div class="value">
                                    <template
                                        v-if="!data.date || data.date == ''">
                                        <DatePicker
                                            style="width: 100%"
                                            type="date"
                                            placeholder="请选择出生日期"
                                            @on-change="dateChange"
                                            :value="userData.date"></DatePicker>
                                    </template>
                                    <template v-else-if="isApply">
                                        <DatePicker
                                            disabled
                                            style="width: 100%"
                                            type="date"
                                            :value="userData.date"></DatePicker>
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有出生日期信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            
                            <div class="form-item">
                                <div class="label">住址：</div>
                                <div class="value">
                                    <template
                                        v-if="
                                            !data.address || data.address == ''
                                        ">
                                        <Input
                                            v-model="userData.address"
                                            placeholder="请输入住址" />
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.address"
                                            placeholder="请输入住址" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有住址信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">手机号：</div>
                                <div class="value value_phone">
                                    <template
                                        v-if="!data.phone || data.phone == ''">
                                        <Input
                                          oninput="this.value=this.value.replace(/[^\d]/g,'').replace(/^0+(?=\d)/,'')"
                                        type="number"
                                            v-model="userData.phone"
                                            :maxlength="11"
                                            placeholder="请输入手机号" />
                                        <div
                                            class="phone-validate"
                                            v-if="showPhoneError">
                                            手机号码不正确
                                        </div>
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            :disabled="true"
                                            v-model="userData.phone"
                                            placeholder="请输入手机号" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有手机号信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">历史手机号：</div>
                                <div class="value value_phone">
                                    <template
                                        v-if="!data.oldPhone || data.oldPhone == ''">
                                        <Input
                                          oninput="this.value=this.value.replace(/[^\d]/g,'').replace(/^0+(?=\d)/,'')"
                                      
                                            v-model="userData.oldPhone"
                                           
                                            placeholder="请输入历史手机号 多个用,隔开" />
                                        <div
                                            class="phone-validate"
                                            v-if="showOldPhoneError">
                                            手机号码不正确
                                        </div>
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            :disabled="true"
                                            v-model="userData.oldPhone"
                                            placeholder="请输入历史手机号 多个用,隔开" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有历史手机号信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">注册邮箱：</div>
                                <div class="value value_phone">
                                    <template
                                        v-if="!data.email || data.email == ''">
                                        <Input
                                            v-model="userData.email"
                                            placeholder="请输入注册邮箱" />
                                        <div
                                            class="phone-validate"
                                            v-if="showEmailError">
                                            邮箱格式不正确
                                        </div>
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.email"
                                            placeholder="请输入注册邮箱" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有邮箱信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">历史邮箱：</div>
                                <div class="value value_phone">
                                    <template
                                        v-if="!data.oldEmail || data.oldEmail == ''">
                                        <Input
                                            v-model="userData.oldEmail"
                                            placeholder="请输入历史邮箱 多个用,隔开" />
                                        <div
                                            class="phone-validate"
                                            v-if="showOldEmailError">
                                            邮箱格式不正确
                                        </div>
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.oldEmail"
                                            placeholder="请输入历史邮箱 多个用,隔开" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有历史邮箱信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                            <div class="form-item">
                                <div class="label">工作单位：</div>
                                <div class="value">
                                    <template
                                        v-if="
                                            !data.workUnit || data.workUnit == ''
                                        ">
                                        <Input
                                            v-model="userData.workUnit"
                                            placeholder="请输入工作单位" />
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.workUnit"
                                            placeholder="请输入工作单位" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有工作单位信息，暂无查看权限"
                                            disabled />
                                    </template>
                                </div>
                            </div>
                             <div class="form-item">
                                <div class="label">备注：</div>
                                <div class="value">
                                    <template
                                        v-if="
                                            !data.remark || data.remark == ''
                                        ">
                                        <Input
                                            v-model="userData.remark"
                                            placeholder="请输入备注" type="textarea" />
                                    </template>
                                    <template v-else-if="isApply">
                                        <Input
                                            disabled
                                            v-model="userData.remark"
                                            placeholder="请输入备注" type="textarea" />
                                    </template>
                                    <template v-else>
                                        <Input
                                            placeholder="已有备注信息，暂无查看权限"
                                            disabled  type="textarea"/>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-note">*至少补充一项内容后提交</div>

                <div class="footer">
                    <button
                        class="submit-btn"
                        @click="submit"
                        :disabled="loading || submitting">
                        {{ submitting ? "提交中..." : "提交" }}
                    </button>
                    <button
                        class="cancel-btn"
                        @click="close"
                        :disabled="submitting">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
    data() {
        // 这里存放数据
        return {
            phoneTipError: "",
            userData: {
                accountName: "",
                userId: "",
                mediumName: "",
                registerAddress: "",
                fansCount: "",
                followCount: "",
                url: "",
                name: "",
                sex: "",
                idCard: "",
                date: "",
                address: "",
                phone: "",
                email: "",
                workUnit:"",
                remark:"",
                oldPhone:"",
                oldEmail:"",
            },

            showPhoneError: false,
            showIdCardError: false,
            showEmailError: false,
            showOldPhoneError: false,
            showOldEmailError: false,
            loading: true, // 添加加载状态标记
            submitting: false, // 提交状态锁，防止重复提交
            submitTimer: null, // 用于防抖处理的计时器
        };
    },
    // import 引入的组件需要注入到对象中才能使用
    components: {},
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        that: {
            type: Object,
            default: null,
        },
    },
    created() {
        this.initData();
    },

    // 销毁前清除定时器
    beforeDestroy() {
        if (this.submitTimer) {
            clearTimeout(this.submitTimer);
        }
    },
    // 方法集合
    methods: {
        dateChange(date) {
            console.log(date);
            this.userData.date = date;
        },
        initData() {
            // 从 props 中初始化数据
            console.log(this.data);
            if (this.data) {
                this.loading = false;
                Object.keys(this.userData).forEach((key) => {
                    this.userData[key] = this.data[key] || "";
                });
            }
        },

        // 只允许输入数字
        onlyAllowNumbers(event) {
            // 防止输入非数字
            if (
                !/^\d$/.test(event.key) &&
                // 允许一些控制键
                ![
                    "Backspace",
                    "Delete",
                    "ArrowLeft",
                    "ArrowRight",
                    "Tab",
                ].includes(event.key)
            ) {
                event.preventDefault();
            }
        },

        findChangedProperties(obj1, obj2) {
            const changed = {};

            // 检查obj1中的属性是否在obj2中变化
            for (const key in obj1) {
                if (obj1.hasOwnProperty(key) && obj2.hasOwnProperty(key)) {
                    // 忽略 null → '' 的情况
                    if (
                        obj1[key] !== obj2[key] &&
                        !(obj1[key] === null && obj2[key] === "")
                    ) {
                        changed[key] = obj2[key];
                    }
                }
            }

            // 检查obj2中新增的属性
            for (const key in obj2) {
                if (obj2.hasOwnProperty(key) && !obj1.hasOwnProperty(key)) {
                    changed[key] = obj2[key];
                }
            }

            return changed;
        },

        // 提交方法 - 添加防并发机制
        submit() {
            // if(this.showPhoneError||this.showIdCardError||this.showEmailError){
            //     return
            // }
            if (document.querySelector(".xxws .phone-validate")) {
                return;
            }
            // 如果正在提交中，直接返回
            if (this.submitting) {
                return;
            }

            if (!this.that) {
                console.error("父组件引用缺失");
                return;
            }
            // 设置提交状态为true，锁定按钮
            this.submitting = true;

            // 根据不同类型准备不同的参数
            let params = {
                id: this.data.id,
            
            };

            const diff = this.findChangedProperties(this.data, this.userData);
            if (Object.keys(diff).length == 0) {
                this.$Message.warning("没有需要提交的内容");
                this.resetSubmitStatus(0);
                return;
            }
            params = { ...params, ...diff };

            const JCCZ_SERVER=""
            this.that.$http
                .post(
                   JCCZ_SERVER +
                        "/media/completeInformation",
                    params,
                    {
                        emulateJSON: false,
                    }
                )
                .then((res) => {
                    if (res.body.status === 0) {
                        this.$Message.success(res.body.message);
                        this.$emit("close", diff);
                    } else {
                        this.$Message.error(res.body.message);
                        // 提交失败后，3秒后解锁提交按钮
                        this.resetSubmitStatus(3000);
                    }
                })
                .catch((err) => {
                    this.$Message.error(err.message || "提交失败，请稍后重试");
                    // 提交错误后，3秒后解锁提交按钮
                    this.resetSubmitStatus(3000);
                });
        },

        // 重置提交状态的方法
        resetSubmitStatus(delay = 0) {
            if (this.submitTimer) {
                clearTimeout(this.submitTimer);
            }

            this.submitTimer = setTimeout(() => {
                this.submitting = false;
            }, delay);
        },

        close() {
            // 如果正在提交中，阻止关闭
            if (this.submitting) {
                this.$Message.warning("正在提交数据，请稍候...");
                return;
            }

            // 传递一个明确的对象，表明这是关闭操作
            this.$emit("close", "cancel");
        },
    },
    // 计算属性 类似于 data 概念
    computed: {
        isApply() {
            return this.that.phoneTip == 3;
        },
    },
    // 监控 data 中的数据变化
    watch: {
        "userData.phone"(val) {
            // 只检查长度错误，不做其他处理，避免干扰输入    

             if (val == "" || val == null) {
                this.showPhoneError = false;
            } else {
                  const regex = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
                this.showPhoneError = !regex.test(val);
            }
        },
        "userData.idCard"(val) {
            if (val == "" || val == null) {
                this.showIdCardError = false;
            } else {
                this.showIdCardError = val.length > 0 && val.length !== 18;
            }
        },
        "userData.email"(val) {
            if (val == "" || val == null) {
                this.showEmailError = false;
            } else {
                const emailRegex =
                    /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                this.showEmailError = !emailRegex.test(val);
            }
        },
        // "userData.oldPhone"(val) {
        //     if (val == "" || val == null) {
        //         this.showOldPhoneError = false;
        //     } else {
        //         const regex = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
        //         const arr = val.split(',').map(s => s.trim()).filter(Boolean);
        //         this.showOldPhoneError = arr.some(phone => !regex.test(phone));
        //     }
        // },
        // "userData.oldEmail"(val) {
        //     if (val == "" || val == null) {
        //         this.showOldEmailError = false;
        //     } else {
        //         const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        //         const arr = val.split(',').map(s => s.trim()).filter(Boolean);
        //         this.showOldEmailError = arr.some(email => !emailRegex.test(email));
        //     }
        // },
    },
    //过滤器
    filters: {},
    // 生命周期 - 挂载完成（可以访问 DOM 元素）
    mounted() {},
};
</script>

<style scoped lang="less">
.index {
    width: 1200px;
    border-radius: 8px;
    overflow: hidden;

    .content {
        background: white;
        padding: 50px 40px;
        position: relative;
    }

    // 添加加载状态样式
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4a90e2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        .loading-text {
            color: #666;
            font-size: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    }
    .form_group {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .form {
            display: flex;
            width: 49%;
            &.left {
                border: 2px solid rgba(129, 211, 248, 0.2);
                .form_name {
                    background-color: rgba(129, 211, 248, 0.2);
                }
            }
            &.right {
                border: 2px solid rgba(202, 249, 130, 0.2);
                .form_name {
                    background-color: rgba(202, 249, 130, 0.2);
                }
            }
            .form_name {
                font-size: 16px;
                color: #333;
                writing-mode: vertical-rl;
                display: flex;
                align-items: center;
                justify-content: center;
                letter-spacing: 4 px;
                // font-weight: bold;
                // vertical-align: middle;
            }
            .form_box {
                max-height: 490px;
                overflow-y: auto;
                padding: 10px;
                flex: 1;
            }
        }
    }

    .form-item {
        display: flex;
        margin-bottom: 20px;
        font-size: 16px;
        color: #333;
        align-items: center;

        .label {
            width: 97px;
            text-align: right;
            margin-right: 10px;
        }
        
        .value {
            flex: 1;

            &.highlight {
                color: #4a90e2;
                font-weight: bold;
            }

            &.value_phone {
                display: flex;
                align-items: center;
                white-space: nowrap;
            }
            /deep/ .ivu-input {
                font-size: 16px;
                color: #333;
            }
        }

        .phone-validate {
            color: red;
            margin-left: 10px;
            font-size: 14px;
        }
    }

    .form-note {
        color: red;
        font-size: 14px;
        margin: 50px 0 20px 0px;

        text-align: center;
    }

    .footer {
        display: flex;
        justify-content: center;
        gap: 20px;

        button {
            width: 120px;
            height: 40px;
            border-radius: 4px;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: white;
            transition: background-color 0.3s;

            &.submit-btn {
                background-color: #4a90e2;
                &:disabled {
                    background-color: #a0c3ee;
                    cursor: not-allowed;
                }
            }

            &.cancel-btn {
                background-color: #9e9e9e;
                &:disabled {
                    background-color: #cccccc;
                    cursor: not-allowed;
                }
            }
        }
    }

    .form.left{
    .form-item{
        margin-bottom: 35px;
    }
}
}

.xxws {
    // 去除 number 类型 input 的上下箭头
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    input[type="number"] {
        -moz-appearance: textfield;
    }
}

/deep/ .ivu-radio-wrapper{
    font-size: 16px;
}
</style>
