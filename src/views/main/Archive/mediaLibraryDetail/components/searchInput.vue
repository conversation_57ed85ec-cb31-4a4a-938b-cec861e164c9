<template>
  <div style="display: flex; align-items: center; height: 40px;">
    <svg-icon
      icon-class="重点提示-搜索"
      style="width: 25px; height: 25px; margin-right: 10px;"
    />
    <!-- <span>搜索词查找：</span> -->
    <Input v-model="value" placeholder="请输入关键词" style="width: 400px;">
      <Select v-model="select1" slot="prepend" style="width: 100px;">
        <Option value="1">全文</Option>
        <Option value="2">标题</Option>
        <Option value="3">正文</Option>
      </Select></Input
    >
    <span class="btn" @click="handleSearch">确定</span>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  name: "inputs.vue",
  data() {
    // 这里存放数据
    return {
      value: "", //搜索词
      select1: "1",
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 方法集合
  methods: {
    handleSearch() {
      this.$emit("changeSearch", {
        keyword: this.value,
        searchPosition: this.select1,
      });
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style scoped lang="less">
.btn {
  display: inline-block;
  width: 80px;
  height: 40px;
  background: #5585ec;
  border-radius: 4px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  margin-left: 10px;
  cursor: pointer;
}
/deep/.ivu-input {
  height: 40px;
  padding-left: 20px;
}
</style>
