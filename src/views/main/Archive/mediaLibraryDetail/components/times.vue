<template>
  <div>
    <div class="content">
      <div class="title">
        <svg-icon
          icon-class="重点提示-时间"
          style="width: 25px; height: 25px; margin-right: 10px"
        />
        <span> {{name}}：</span>
      </div>
      <div class="title-content">
        <span
          v-for="item in dataList"
          :key="item.id"
          class="items"
          :class="selectId == item.id ? 'choose' : ''"
          @click="handleChange(item)"
          >{{ item.title }}</span
        >
      </div>
      <div v-show="selectId == -1" style="margin-left: 20px">
        <DatePicker
          type="daterange"
          :value="timeValue"
          split-panels
          placeholder="请输入起始时间"
          style="width: 200px; margin-right: 5px"
          @on-change="handleChanges"
        ></DatePicker>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
export default {
  name: "times.vue",
  data() {
    // 这里存放数据
    return {
      selectId: 1, //选中id
      timeValue: [],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    dataList: {
      default: () => [],
    },
    selectIds: "",
    name:{}
  },
  // 方法集合
  methods: {
    moment,
    // 时间切换
    handleChange(data) {
      this.selectId = data.id;
      if (data.id == -1) {
        return;
      } else {
        this.$emit("changeTime", this.selectId);
      }
    },
    handleChanges(val) {
      let data = {
        id: "-1",
        startTime: val[0],
        endTime: val[1],
      };
     if (val[0] && val[1]) {
      this.$emit("changeTime", data);
     } else {
       this.$emit("changeTime", this.dataList[0].id);
     }
      
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    selectIds: {
      handler(val) {
      this.selectId = val
      },
    
    }
  },
  //过滤器
  filters: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    if (this.selectIds) {
      this.selectId = this.selectIds;
    }
     if (this.$route.query.time == 5) {
      let time = new Date()
        this.timeValue = [(time.getFullYear()+'-01-01'),moment(new Date().getTime()).format("YYYY-MM-DD")]
      } else if(this.$route.query.time == -1) {
        this.timeValue = [this.$route.query.startTime,this.$route.query.endTime]
      }
  },
};
</script>

<style scoped lang="less">
.content {
  display: flex;
  align-items: center;
  color: #333333;
  .title {
    display: flex;
    align-items: center;
  }
  .choose {
    background: #5585ec;
    border-radius: 4px;
    color: #fff;
  }
  .title-content {
    .items {
      display: inline-block;
      width: 60px;
      height: 40px;
      line-height: 40px;
      cursor: pointer;
      text-align: center;
      border-radius: 4px;
    }
  }
}
</style>