<template>
  <div class="info-exchange">
    <!-- 左侧评论区域 -->
    <div class="comment-section">
      <!-- 固定在顶部的部分 -->
      <div class="fixed-top">
        <div class="section-header">
          <h3>评论 ({{ commentList.length }})</h3>
        </div>

        <!-- 评论输入框 -->
        <div class="comment-input">
          <div class="avatar">
            <img src="../img/userface_green.svg" width="40" alt="">
          </div>
          <div class="input-area">
            <Input
              v-model="commentContent"
              type="textarea"
              :rows="3"
              placeholder="请输入评论内容..." 
              :maxlength="200"
            />
            <div class="btn-area">
              <Button type="primary" @click="addComment" :loading="commentSubmitting">发表</Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 评论列表 - 可滚动区域 -->
      <div class="comment-list-container" ref="commentListContainer" @scroll="handleCommentScroll">
        <Spin v-if="loading" fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <NoData v-if="!loading && commentList.length === 0" />
        <div class="comment-list">
          <div v-for="item in commentList" :key="item.id" class="comment-item">
            <div class="avatar">
              <img src="../img/userface_blue.svg" width="40" alt="">
            </div>
            <div class="content">
              <div class="user-info">
                <span class="username">{{ item.operatorName }}</span>
                <span v-if="item.userAccount === currentUserAccount" class="delete-btn" @click="deleteExchange(item.id, 1)">
                  删除
                </span>
                <div class="comment-time">
                {{ formatTime(item.createTime) }}
                </div>
              </div>
              <div class="comment-content">{{ item.content }}</div>

            </div>
          </div>
          <!-- 加载更多提示 -->
          <div v-if="commentHasMore && !commentLoading" class="load-more">
            <span>向下滚动加载更多</span>
          </div>
          <div v-if="commentLoading && !loading" class="loading-more">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <span>加载中...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧备注区域 -->
    <div class="note-section">
      <!-- 固定在顶部的部分 -->
      <div class="fixed-top">
        <div class="section-header">
          <h3>备注 ({{ noteList.length }})</h3>
        </div>

        <!-- 备注输入框 -->
        <div class="note-input">
          <div class="input-area">
            <Input
              v-model="noteContent"
              type="textarea"
              :rows="3"
              placeholder="请输入备注内容..."
              :maxlength="200"
            />
            <div class="btn-area">
              <Button type="primary" @click="addNote" :loading="noteSubmitting">添加备注</Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 备注列表 - 可滚动区域 -->
      <div class="note-list-container" ref="noteListContainer" @scroll="handleNoteScroll">
        <Spin v-if="loading" fix>
          <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <NoData v-if="!loading && noteList.length === 0" />
        <div class="note-list">
          <div v-for="item in noteList" :key="item.id" class="note-item">
            <div class="content">
              <div class="note-content">{{ item.content }}</div>
              <div class="user-info">
                <span class="username_bz">{{ item.operatorName }}</span>
                <span v-if="item.userAccount === currentUserAccount" class="delete-btn" @click="deleteExchange(item.id, 2)">
                  删除
                </span>
                <span class="time">{{ formatTime(item.createTime) }}</span>
              </div>
            </div>
          </div>
          <!-- 加载更多提示 -->
          <div v-if="noteHasMore && !noteLoading" class="load-more">
            <span>向下滚动加载更多</span>
          </div>
          <div v-if="noteLoading && !loading" class="loading-more">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <span>加载中...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NoData from '@/components/noData';

export default {
  name: 'InfoExchange',
  components: {
    NoData
  },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      commentLoading: false,
      noteLoading: false,
      commentList: [],
      noteList: [],
      commentContent: '',
      noteContent: '',
      commentSubmitting: false,
      noteSubmitting: false,
      currentUserAccount: '', // 当前用户账号，用于判断是否显示删除按钮
      pageSize: 10, // 每页显示数量
      commentPageNo: 1, // 评论当前页码
      notePageNo: 1, // 备注当前页码
      commentHasMore: true, // 评论是否有更多数据
      noteHasMore: true // 备注是否有更多数据
    };
  },
  mounted() {
    this.getCurrentUserInfo();
    this.fetchExchangeList();
    this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】信息交流页浏览');
  },
  methods: {
    // 获取当前用户信息
    getCurrentUserInfo() {
      // 从localStorage或vuex中获取当前用户账号
      this.currentUserAccount = localStorage.getItem('userAccount') || '';
    },

    // 获取评论列表
    async fetchCommentList(isLoadMore = false) {
      if (!isLoadMore) {
        this.commentLoading = true;
      }

      try {
        const res = await this.$http.get('/accountInfoNew/getExchangeList', {
          params: {
            userId: this.userId,
            type: 1, // 1表示评论
            pageNo: isLoadMore ? this.commentPageNo + 1 : 1,
            pageSize: this.pageSize
          }
        });

        if (res.body && res.body.status === 0) {
          const newList = res.body.data || [];

          if (isLoadMore) {
            // 加载更多时，追加数据
            if (newList.length > 0) {
              this.commentList = [...this.commentList, ...newList];
              this.commentPageNo++;
              this.commentHasMore = newList.length === this.pageSize;
            } else {
              this.commentHasMore = false;
            }
          } else {
            // 首次加载或刷新，替换数据
            this.commentList = newList;
            this.commentPageNo = 1;
            this.commentHasMore = newList.length === this.pageSize;
          }
        }
      } catch (error) {
        console.error('获取评论列表失败:', error);
        this.$Message.error('获取评论列表失败');
      } finally {
        this.commentLoading = false;
      }
    },

    // 获取备注列表
    async fetchNoteList(isLoadMore = false) {
      if (!isLoadMore) {
        this.noteLoading = true;
      }

      try {
        const res = await this.$http.get('/accountInfoNew/getExchangeList', {
          params: {
            userId: this.userId,
            type: 2, // 2表示备注
            pageNo: isLoadMore ? this.notePageNo + 1 : 1,
            pageSize: this.pageSize
          }
        });

        if (res.body && res.body.status === 0) {
          const newList = res.body.data || [];

          if (isLoadMore) {
            // 加载更多时，追加数据
            if (newList.length > 0) {
              this.noteList = [...this.noteList, ...newList];
              this.notePageNo++;
              this.noteHasMore = newList.length === this.pageSize;
            } else {
              this.noteHasMore = false;
            }
          } else {
            // 首次加载或刷新，替换数据
            this.noteList = newList;
            this.notePageNo = 1;
            this.noteHasMore = newList.length === this.pageSize;
          }
        }
      } catch (error) {
        console.error('获取备注列表失败:', error);
        this.$Message.error('获取备注列表失败');
      } finally {
        this.noteLoading = false;
      }
    },

    // 初始化加载所有数据
    async fetchExchangeList() {
      this.loading = true;
      await Promise.all([
        this.fetchCommentList(),
        this.fetchNoteList()
      ]);
      this.loading = false;
    },

    // 添加评论
    async addComment() {
      if (!this.commentContent.trim()) {
        this.$Message.warning('请输入评论内容');
        return;
      }

      this.commentSubmitting = true;
      try {
        const res = await this.$http.post('/accountInfoNew/addExchange', {
          userId: this.userId,
          type: 1, // 1表示评论
          content: this.commentContent.trim()
        });

        if (res.body && res.body.status === 0) {
          this.$Message.success('评论发布成功');
          this.commentContent = '';
          this.fetchCommentList(); // 只刷新评论列表
        } else {
          this.$Message.error(res.body.message || '评论发布失败');
        }
      } catch (error) {
        console.error('发布评论失败:', error);
        this.$Message.error('发布评论失败');
      } finally {
        this.commentSubmitting = false;
      }
    },

    // 添加备注
    async addNote() {
      if (!this.noteContent.trim()) {
        this.$Message.warning('请输入备注内容');
        return;
      }

      this.noteSubmitting = true;
      try {
        const res = await this.$http.post('/accountInfoNew/addExchange', {
          userId: this.userId,
          type: 2, // 2表示备注
          content: this.noteContent.trim()
        });

        if (res.body && res.body.status === 0) {
          this.$Message.success('备注添加成功');
          this.noteContent = '';
          this.fetchNoteList(); // 只刷新备注列表
        } else {
          this.$Message.error(res.body.message || '备注添加失败');
        }
      } catch (error) {
        console.error('添加备注失败:', error);
        this.$Message.error('添加备注失败');
      } finally {
        this.noteSubmitting = false;
      }
    },

    // 删除评论或备注
    async deleteExchange(id, type) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条内容吗？',
        onOk: async () => {
          try {
            const res = await this.$http.get('/accountInfoNew/deleteExchange', {
              params: { id }
            });

            if (res.body && res.body.status === 0) {
              this.$Message.success('删除成功');
              // 根据类型刷新对应列表
              if (type === 1) {
                this.fetchCommentList();
              } else {
                this.fetchNoteList();
              }
            } else {
              this.$Message.error(res.body.message || '删除失败');
            }
          } catch (error) {
            console.error('删除失败:', error);
            this.$Message.error('删除失败');
          }
        }
      });
    },

    // 处理评论列表滚动
    handleCommentScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 当滚动到距离底部20px以内，且有更多数据可加载，且当前没有加载中的请求时
      if (scrollHeight - scrollTop - clientHeight < 20 && this.commentHasMore && !this.commentLoading) {
        this.fetchCommentList(true);
      }
    },

    // 处理备注列表滚动
    handleNoteScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 当滚动到距离底部20px以内，且有更多数据可加载，且当前没有加载中的请求时
      if (scrollHeight - scrollTop - clientHeight < 20 && this.noteHasMore && !this.noteLoading) {
        this.fetchNoteList(true);
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
};
</script>

<style scoped>

.info-exchange {
  display: flex;
  height: 100%;
  /* background-color: #f5f7fa; */
}

.comment-section, .note-section {
  flex: 1;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
}

.comment-section {
  margin-right: 10px;
}

/* 固定在顶部的部分 */
.fixed-top {
  padding: 20px 20px 0;
  background-color: #fff;
  z-index: 10;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.comment-input, .note-input {
  margin-bottom: 20px;
  display: flex;
}

.avatar {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 50%;
  img{
    background: white;
  }

}

.input-area {
  flex: 1;
}

.btn-area {
  margin-top: 10px;
  text-align: right;
}

/* 可滚动的列表容器 */
.comment-list-container, .note-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 0px;
  position: relative;
}

.comment-list, .note-list {
  position: relative;
  min-height: 100px;
  max-height: 100%;
}

.comment-item, .note-item {
  margin-bottom: 20px;
  display: flex;
}

.comment-item .content, .note-item .content {
  flex: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  gap: 20px;
}

.username {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-right: 10px;
  flex:1
}
.username_bz {
  font-size: 14px;
  color: #A2A1A1;

  margin-right: 10px;
  flex:1
}

.delete-btn {
  color: #999;
  cursor: pointer;
  margin-left: auto;
  font-size:14px;
}

.delete-btn:hover {
  color: #f56c6c;
}

.comment-content {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
  word-break: break-all;
}

.comment-time, .time {
  font-size: 14px;
  color: #a2a1a1;
  min-width: 140px;
  text-align: right;
}

.note-content {
  font-size: 16px;
  color: #4e6ef2;
  font-weight: bold;
  margin-bottom: 5px;
  word-break: break-all;
}

.note-item .user-info {
  justify-content: flex-start;
}

.note-item .time {
  margin-left: 10px;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  color: #999;
  padding: 10px 0;
  font-size: 14px;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  padding: 10px 0;
  font-size: 14px;
}

.loading-more .demo-spin-icon-load {
  margin-right: 5px;
}

/* 加载动效样式 */
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
  to { transform: rotate(360deg); }
}
</style>
