<template>
  <div class="StatisticalAnalysis">
    <div class="chartFrame flex sb">
      <div class="item">
        <div class="title flex sb">
          <span>发文趋势分析</span>
          <Select v-model="type" style="width: 100px;">
            <Option
              v-for="item in typeList"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</Option
            >
          </Select>
        </div>
        <NoData v-if="chart1NoData" />
        <div v-else class="chart" ref="chart1"></div>
      </div>
      <div class="item">
        <div class="title">
          <span>情感倾向对比</span>
        </div>
        <NoData v-if="chart2NoData" />
        <div v-else class="chart" ref="chart2"></div>
      </div>
      <div class="item">
        <div class="title">
          <span>信息处置情况</span>

          <Tooltip placement="top">
            <Icon type="md-help-circle" />
            <div slot="content">
              <p>删除信息：删除信息占发布信息的比例；</p>
              <p>打标信息：打标信息占发布信息的比例；</p>
              <p>提示单引用：提示单引用占打标信息比例；</p>
              <p>要报引用：要报引用占打标信息比例；</p>
            </div>
          </Tooltip>
        </div>
        <NoData v-if="chart3NoData" />
        <div v-else class="chart" ref="chart3"></div>
      </div>

      <div class="item">
        <div class="title">
          <span>参与事件统计</span>
        </div>
        <NoData v-if="chart4NoData" />
        <div v-else class="chart" ref="chart4"></div>
      </div>
    </div>
  </div>
</template>

<script>
const typeList = [
  { label: "按天统计", value: 1 },
  { label: "按月统计", value: 2 },
  { label: "按年统计", value: 3 },
];
import echarts from "echarts";
import moment from "moment";
export default {
  name: "",
  data() {
    return {
      moment,
      typeList,
      type: 2,
      chart1NoData: false,
      chart2NoData: false,
      chart3NoData: false,
      chart4NoData: false,
    };
  },
  props: {
    dayNum: {},
    dateList: {
      type: Array,
      default: () => [],
    },
    mediumName: {
      type: String,
      default: "",
    },
    userAccount: {
      type: String,
      default: "",
    },
    userId: {
      default: "",
    },
  },
  watch: {
    type() {
      this.getChart1Data();
    },
    dayNum(val) {
      if (val != "-99") {
        this.getChart1Data();
        this.getChart2Data();
        this.getChart3Data();
        this.getChart4Data();
      }
    },
    dateList(val) {
      if (val.length > 0) {
        this.getChart1Data();
        this.getChart2Data();
        this.getChart3Data();
        this.getChart4Data();
      }
    },
  },
  methods: {
    getChart1Data() {
      let params = {
        dayNum: this.dayNum, // 时间：今日 0、近三日 2 、近一周 6、近一月 30
        startTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        endTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        statType: this.type, // 统计类型：按天 1、按月 2 、按年 3
        userAccount: this.userAccount, //账号名称，传账号信息的userAccount字段
        mediumName: this.mediumName, //传账号信息的mediumName字段
        userId: this.userId,
      };
      this.$http.get("/media/msgTends", { params }).then((res) => {
        console.log(res);
        if (res.body.data) {
          this.chart1NoData = false;
          setTimeout(() => {
            this.setChart1(res.body.data);
          }, 200);
        } else {
          this.chart1NoData = true;
        }
      });
    },
    setChart1(data) {
      let option = {
        title: {
          left: "center",
          textStyle: {
            fontSize: 18,
          },
        },
        xAxis: {
          type: "category",
          data: Object.keys(data).map((i) =>
            moment(Number(i)).format("YYYY-MM-DD")
          ),
          axisLabel: {
            // interval: 0,
            // formatter: function (value) {
            //   return moment(Number(value)).format("YYYY-MM-DD");
            // },
          },
        },
        yAxis: {
          type: "value",
          // interval: 1,
        },
        dataZoom: [
          {
            type: "inside",
            start: 0,
            end: 100,
          },
          {
            start: 0,
            end: 20,
          },
        ],
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            return `${params[0].name} 信息数量：${params[0].data}`;
          },
        },
        series: [
          {
            name: "信息数量",
            type: "line",
            data: Object.values(data),
            itemStyle: {
              color: "#1890ff", // 蓝色主题
            },
            areaStyle: {
              // 新增填充配置
              color: "#3FB6FE", // 与折线同色系
              opacity: 0.7, // 通过透明度控制填充浓度
            },
            lineStyle: {
              width: 2,
            },
            symbolSize: 8,
          },
        ],
        grid: {
          top: 10,
          right: 40,
          left: 60,
          bottom: 80,
        },
      };
      echarts.init(this.$refs.chart1).setOption(option);
    },
    getChart2Data() {
      let params = {
        dayNum: this.dayNum, // 时间：今日 0、近三日 2 、近一周 6、近一月 30
        startTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        endTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        userAccount: this.userAccount, //账号名称，传账号信息的userAccount字段
        mediumName: this.mediumName, //传账号信息的mediumName字段
        userId: this.userId,
      };
      this.$http.get("/media/sentimentNum", { params }).then((res) => {
        console.log(res);
        // this.setChart2();
        if (res.body.data) {
          this.chart2NoData = false;
          this.setChart2(res.body.data);
        } else {
          this.chart2NoData = true;
        }
      });
    },
    setChart2(data) {
      let option = {
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "pie",
            radius: "70%",
            data: [
              { value: data.positiveNum, name: "正面" },
              { value: data.negativeNum, name: "负面" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      echarts.init(this.$refs.chart2).setOption(option);
    },
    getChart3Data() {
      let params = {
        dayNum: this.dayNum, // 时间：今日 0、近三日 2 、近一周 6、近一月 30
        startTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        endTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        userAccount: this.userAccount, //账号名称，传账号信息的userAccount字段
        mediumName: this.mediumName, //传账号信息的mediumName字段
        userId: this.userId,
      };
      this.$http.get("/media/disposeStat", { params }).then((res) => {
        console.log(res);
        this.setChart3(res.body.data);
        if (res.body.data) {
          this.chart3NoData = false;
          this.setChart3(res.body.data);
        } else {
          this.chart3NoData = true;
        }
      });
    },
    setChart3(data) {
      let option = {
        animation: false, // 关闭所有动画
        title: [
          {
            text: "删除信息",
            left: "10%",
            top: "85%",
            textAlign: "center",
            textStyle: {
              fontSize: 14,
              color: "#333"
            }
          },
          {
            text: "打标信息",
            left: "35%",
            top: "85%",
            textAlign: "center",
            textStyle: {
              fontSize: 14,
              color: "#333"
            }
          },
          {
            text: "提示单引用",
            left: "65%",
            top: "85%",
            textAlign: "center",
            textStyle: {
              fontSize: 14,
              color: "#333"
            }
          },
          {
            text: "要报引用",
            left: "90%",
            top: "85%",
            textAlign: "center",
            textStyle: {
              fontSize: 14,
              color: "#333"
            }
          },
        ],
        series: [
          // 删除信息（红色）
          {
            title: "删除信息",
            type: "pie",
            radius: ["40%", "30%"],
            center: ["10%", "45%"],
            label: {
              formatter: (val) => {
                if (val.name !== "总数") {
                  return `{val|${val.value}}{unit|条}`;
                }
                return "";
              },
              rich: {
                val: {
                  fontSize: 30,
                  fontweight: "bold",
                  color: "#333",
                  verticalAlign: "bottom",
                },
                unit: { fontSize: 14, color: "#333", verticalAlign: "center" },
              },
              position: "center",
            },
            hoverAnimation: false,
            data: [
              {
                value: data.deleteNum,
                name: "删除信息",
                itemStyle: { color: "#ff4d4f" },
              },
              {
                value: data.total,
                name: "总数",
                itemStyle: { color: "#D9E3ED" },
              },
            ],
            avoidLabelOverlap: false,
          },
          // 打标信息（蓝色）
          {
            type: "pie",
            radius: ["40%", "30%"],
            center: ["35%", "45%"],
            label: {
              formatter: (val) => {
                if (val.name !== "总数") {
                  return `{val|${val.value}}{unit|条}`;
                }
                return "";
              },
              rich: {
                val: {
                  fontSize: 30,
                  fontweight: "bold",
                  color: "#333",
                  verticalAlign: "bottom",
                },
                unit: { fontSize: 14, color: "#333", verticalAlign: "center" },
              },
              position: "center",
            },
            data: [
              {
                value: data.recommentNum,
                name: "打标信息",
                itemStyle: { color: "#1890ff" },
              },
              {
                value: data.total,
                name: "总数",
                itemStyle: { color: "#D9E3ED" },
              },
            ],
            avoidLabelOverlap: false,
          },
          // 提示单引用（绿色）
          {
            type: "pie",
            radius: ["40%", "30%"],
            center: ["65%", "45%"],
            label: {
              formatter: (val) => {
                if (val.name !== "总数") {
                  return `{val|${val.value}}{unit|条}`;
                }
                return "";
              },
              rich: {
                val: {
                  fontSize: 30,
                  fontweight: "bold",
                  color: "#333",
                  verticalAlign: "bottom",
                },
                unit: { fontSize: 14, color: "#333", verticalAlign: "center" },
              },
              position: "center",
            },
            data: [
              {
                value: data.promptNum,
                name: "提示单引用",
                itemStyle: { color: "#52c41a" },
              },
              {
                value: data.total,
                name: "总数",
                itemStyle: { color: "#D9E3ED" },
              },
            ],
            avoidLabelOverlap: false,
          },
          // 要报引用（橙色）
          {
            type: "pie",
            radius: ["40%", "30%"],
            center: ["90%", "45%"],
            label: {
              formatter: (val) => {
                if (val.name !== "总数") {
                  return `{val|${val.value}}{unit|条}`;
                }
                return "";
              },
              rich: {
                val: {
                  fontSize: 30,
                  fontweight: "bold",
                  color: "#333",
                  verticalAlign: "bottom",
                },
                unit: { fontSize: 14, color: "#333", verticalAlign: "center" },
              },
              position: "center",
            },
            data: [
              {
                value: data.materiaNum,
                name: "要报引用",
                itemStyle: { color: "#fa8c16" },
              },
              {
                value: data.total,
                name: "总数",
                itemStyle: { color: "#D9E3ED" },
              },
            ],
            avoidLabelOverlap: false,
          },
        ],
      };
      echarts.init(this.$refs.chart3).setOption(option);
    },
    getChart4Data() {
      let params = {
        dayNum: this.dayNum, // 时间：今日 0、近三日 2 、近一周 6、近一月 30
        startTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[0]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        endTime:
          this.dayNum != "-99"
            ? null
            : moment(this.dateList[1]).format("YYYY-MM-DD HH:mm:ss"), //自定义开始时间，传入时间后dayNum失效
        userAccount: this.userAccount, //账号名称，传账号信息的userAccount字段
        mediumName: this.mediumName, //传账号信息的mediumName字段
        userId: this.userId,
      };
      this.$http.get("/media/eventStat", { params }).then((res) => {
        console.log(res);
        // this.setChart4(res.body.data);
        if (res.body.data && res.body.data.length > 0) {
          this.chart4NoData = false;
          this.setChart4(res.body.data);
        } else {
          this.chart4NoData = true;
        }
      });
    },
    setChart4(data) {
      let option = {
        // 基础样式
        backgroundColor: "#fff",
        textStyle: { fontFamily: "Arial, sans-serif" },

        // 坐标系配置
        grid: {
          left: 250,
          right: "10%",
          top: 20,
          bottom: 50,
        },

        // X轴（数值轴）
        xAxis: {
          type: "value",
          axisLine: { show: false },
          splitLine: {
            lineStyle: { color: "#f0f0f0", type: "dashed" },
          },
        },

        // Y轴（事件轴）
        yAxis: {
          type: "category",
          inverse: true,
          axisLabel: {
            align: "right",
            formatter: (val) => {
              console.log(val);
              let date = "";
              data.forEach((i) => {
                if (i.eventName == val) {
                  date = moment(i.createTime).format("YYYY-MM-DD HH:mm:ss");
                }
              });
              return `{val|${val}}\n{unit|${date}}`;
            },
            rich: {
              val: {
                fontSize: 22,
                fontweight: "bold",
                color: "#5192E3",
                verticalAlign: "bottom",
              },
              unit: { fontSize: 14, color: "#BEBCC2", verticalAlign: "center" },
            },
          },
          data: data.map((i) => i.eventName),
        },

        // 渐变核心配置
        series: [
          {
            type: "bar",
            barWidth: 25,
            label: {
              show: true,
              position: "right",
              color: "#666",
              fontSize: 14,
              formatter: "{@score}",
            },
            itemStyle: {
              // 智能渐变生成器
              color: (params) => {
                const dataMap = {
                  0: ["#C9F980", "#DA0924"], // 第一项：绿→黄渐变
                  1: ["#C9F980", "#DA0924"], // 第二项：橙→浅橙渐变
                  2: ["#C9F980", "#DA0924"], // 第三项：绿→黄渐变
                  3: ["#C9F980", "#DA0924"], // 第四项：绿→黄渐变
                  4: ["#C9F980", "#DA0924"], // 第四项：绿→黄渐变
                  5: ["#C9F980", "#DA0924"], // 第四项：绿→黄渐变
                  6: ["#C9F980", "#DA0924"], // 第四项：绿→黄渐变
                  7: ["#C9F980", "#DA0924"], // 第四项：绿→黄渐变
                  8: ["#C9F980", "#DA0924"], // 第四项：绿→黄渐变
                };

                // 返回渐变配置
                return {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0, // 水平渐变
                  colorStops: [
                    {
                      offset: 0,
                      color: dataMap[params.dataIndex]?.[0] || "#fac858", // 默认黄色
                    },
                    {
                      offset: 1,
                      color: dataMap[params.dataIndex]?.[1] || "#fac858", // 默认黄色
                    },
                  ],
                };
              },
            },
            data: data.map((i) => {
              return {
                value: i.accountCnt,
                createTime: i.createTime,
              };
            }),
          },
        ],
      };
      echarts.init(this.$refs.chart4).setOption(option);
    },
  },
  mounted() {
    this.getChart1Data();
    this.getChart2Data();
    this.getChart3Data();
    this.getChart4Data();
    this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】统计分析页浏览');
  },
};
</script>

<style lang="less" scoped>
.StatisticalAnalysis {
  height: calc(~"100vh - 300px");
  overflow-y: auto;
}
.chartFrame {
  flex-wrap: wrap;
  //   gap: 10px;
  overflow-y: auto;
  height: 850px;
  .item {
    background-color: #fff;
    width: 850px;
    height: 400px;
    border-radius: 5px;
    margin-bottom: 10px;
    .title {
      height: 50px;
      padding: 10px 20px;
      span {
        display: inline-block;
        border-left: 5px solid #1c7ff1;
        line-height: 24px;
        font-size: 18px;
        padding-left: 10px;
        font-weight: 600;
      }
    }
    .chart {
      width: 100%;
      height: 350px;
    }

    // 信息处置情况图表需要更小的高度以显示底部标题
    &:nth-child(3) .chart {
      height: 260px;
    }
  }
}
</style>
