<template>
    <div class="logbody">
      <!-- 加载中动效 -->
      <Spin v-if="loading" fix>
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>

      <!-- 暂无数据 - 只在数据加载完成且确认没有数据时显示 -->
      <NoData v-if="!loading && logList.length === 0 && dataLoaded" />

      <!-- 横向时间轴 -->
      <div class="timeline-wrapper" v-if="!loading && logList.length > 0">
        <div class="timeline" ref="timeline">
          <div
            v-for="(log, idx) in logList"
            :key="log.id"
            class="timeline-item"
            :class="{
                active: idx === 0 || idx === logList.length - 1 || idx === currentIdx
              }"
             @mouseenter="showDetail(idx, $event)"
            @mouseleave="hideDetail"
          >
            <!-- 上方操作类型 -->
            <div
              class="timeline-title"
              :class="{ hoverable: hasDetailInfo(log) }"

            >
              {{ typeMap[log.type] || '未知操作' }}
              <!-- 悬浮卡片 -->
              <div
                v-if="showIdx === idx && hasDetailInfo(log)"
                class="detail-pop"
              >
                <!-- 账号信息完善 -->
                <div v-if="log.type === 1">
                  <!-- 显示完善内容类型 -->
                  <!-- <div class="improve-type">完善类型：{{ log.improveContent || '账号信息' }}</div> -->
                  <div class="info-list">
                                <div
                                    class="info-item"
                                    v-if="log && log.userId">
                                    <span class="label">账号ID：</span>
                                    <span class="value show-ellipsis" v-change-word="log && (log.userId || '-')">
                                       
                                    </span>
                                </div>
                                <div
                                    class="info-item"
                                    v-if="
                                        log &&
                                        log.registerAddress
                                    ">
                                    <span class="label">注册地：</span>
                                    <span class="value" v-change-word="log && (log.registerAddress || '-')">
                                       </span
                                    >
                                </div>
                                <div
                                    class="info-item"
                                    v-if="log && log.fansCount">
                                    <span class="label">粉丝数：</span>
                                    <span class="value" v-change-word="log && (log.fansCount || '-')"></span>
                                </div>
                                <div
                                    class="info-item"
                                    v-if="
                                        log && log.followCount
                                    ">
                                    <span class="label">关注数：</span>
                                    <span
                                        class="value"
                                        v-change-word="
                                            log &&
                                            log.followCount
                                        "
                                        >{{
                                    }}</span>
                                </div>
                                <div
                                    class="info-item"
                                    v-if="log && log.url">
                                    <span class="label">主页链接：</span>
                                    <span class="value" v-change-word="log && (log.url || '-')"></span>
                                </div>
                            </div>
                  
                 
                </div>
                <!-- 账号入库 -->
                <div v-else-if="log.type === 2">
                  <div>账号名称：{{ log.serviceName || '-' }}</div>
                  <div>平台：{{ log.mediumName || '-' }}</div>
                </div>
                <!-- 申请获取人物信息 -->
                <div v-else-if="log.type === 3">
                  <div>申请人：{{ log.operatorName || '-' }}</div>
                  <div>申请单位：{{ log.operatingUnit || '-' }}</div>
                </div>
                <!-- 审核通过/失败 -->
                <div v-else-if="log.type === 4 || log.type === 5">
                  <div>审核人：{{ log.operatorName || '-' }}</div>
                  <div>审核单位：{{ log.operatingUnit || '-' }}</div>
                </div>
                <!-- 账号浏览 -->
                <div v-else-if="log.type === 6">
                  <div>浏览人：{{ log.operatorName || '-' }}</div>
                  <div>浏览单位：{{ log.operatingUnit || '-' }}</div>
                </div>
              </div>
            </div>
            <!-- 圆点 -->
            <div
              class="timeline-dot"

            ></div>
            <!-- 连接线 -->
            <div class="timeline-line" ></div>
            <!-- 下方时间与操作人 -->
            <div class="timeline-info">
              <div class="timeline-time">{{ formatTime(log.createTime) }}</div>
              <div class="timeline-operator">
                {{ log.operatorName || '-' }}
                <span v-if="log.operatingUnit">（{{ log.operatingUnit }}）</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <script>
  /**
   * 账号操作类型映射
   */
  const typeMap = {
    1: '账号信息完善',
    2: '账号入库',
    3: '申请获取人物信息',
    4: '审核通过',
    5: '审核失败',
    6: '账号浏览',
    7: '打标信息账号自动入库',
    8: '账号浏览',
    9: '账号信息完善',
    10: '账号入库'
  };
  const updateElement = (el, value) => {
    console.log(value);
    if (!value || typeof value !== "string") return;

    const lastParenIndex = value.lastIndexOf("(");

    if (lastParenIndex === -1) {
        el.innerHTML = value;
        return;
    }

    const beforeText = value.substring(0, lastParenIndex);
    const afterText = value.substring(lastParenIndex);

    el.innerHTML = `${beforeText} ${afterText}`;
};
  // 导入 NoData 组件
  import NoData from '@/components/noData';

  export default {
    name: 'operationLog',
    components: {
      NoData // 注册 NoData 组件
    },
    props: {
      infoData: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        logList: [],
        showIdx: null,
        typeMap,
        currentIdx: -1, // 可根据业务高亮当前节点
        loading: true, // 加载状态，默认为 true 表示初始加载中
        dataLoaded: false // 数据是否已加载过的标志
      };
    },
    computed: {
      latestLog() {
        // 取最后一条日志用于顶部卡片
        return this.logList.length ? this.logList[this.logList.length - 1] : null;
      }
    },
    directives: {
        "change-word": {
            bind(el, binding) {
                updateElement(el, binding.value);
            },
            update(el, binding) {
                updateElement(el, binding.value);
            },
        },
    },
    methods: {
      async fetchLogList() {
        var userId = this.infoData.userId!=''?this.infoData.userId: this.$route.query.ukey||'';
        if (!userId || userId=='') {
          this.loading = false;
          this.dataLoaded = true;
          return;
        }

        // 设置加载状态为 true
        this.loading = true;
        this.dataLoaded = false; // 重置数据加载标志

        try {
          const res = await this.$http.get('/accountApply/getLogListNew', {
            params: { userId:userId}
          });
          if (res.body && res.body.status === 0) {
            this.logList = (res.body.data || [])
            // 调试输出
            console.log('日志数据:', this.logList);
            if (this.logList.length > 0) {
              console.log('第一条日志示例:', this.logList[0]);
              // 检查是否有账号信息完善类型的日志
              const infoLog = this.logList.find(log => log.type === 1);
              if (infoLog) {
                console.log('账号信息完善日志示例:', infoLog);
              }
            }
          }
        } catch (e) {
          console.error('获取日志失败:', e);
          this.logList = [];
        } finally {
          // 无论成功还是失败，都将加载状态设置为 false
          this.loading = false;
          // 标记数据已加载过
          this.dataLoaded = true;
        }
      },
      formatTime(ts) {
        if (!ts) return '-';
        const d = new Date(ts);
        const pad = n => (n < 10 ? '0' + n : n);
        return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
      },
      showDetail(idx, event) {
        this.showIdx = idx;

        // 使用nextTick确保DOM已更新
        this.$nextTick(() => {
          const popElement = document.querySelector('.detail-pop');
          if (!popElement) return;

          // 获取触发元素的位置信息
          const titleElement = event.currentTarget;
          const rect = titleElement.getBoundingClientRect();

          // 计算浮层位置
          const popHeight = popElement.offsetHeight;
          const topPosition = rect.top - popHeight - 40; // 在元素上方10px处

          // 设置浮层位置
          popElement.style.top = `${topPosition}px`;
          popElement.style.left = `${rect.left + rect.width / 2}px`;
          popElement.style.transform = 'translateX(-50%)';

          // 确保浮层不超出视口
          const viewportWidth = window.innerWidth;

          // 如果浮层顶部超出视口，则显示在元素下方
          if (topPosition < 0) {
            popElement.style.top = `${rect.bottom + 10}px`;
            popElement.style.transform = 'translateX(-50%)';

            // 添加顶部箭头类
            popElement.classList.add('arrow-top');
          } else {
            // 移除顶部箭头类
            popElement.classList.remove('arrow-top');
          }

          // 如果浮层左侧超出视口
          const popRect = popElement.getBoundingClientRect();
          if (popRect.left < 0) {
            popElement.style.left = '20px';
            popElement.style.transform = 'none';
          }

          // 如果浮层右侧超出视口
          if (popRect.right > viewportWidth) {
            popElement.style.left = 'auto';
            popElement.style.right = '20px';
            popElement.style.transform = 'none';
          }
        });
      },
      hideDetail() {
        this.showIdx = null;
      },
      // 判断是否有详情信息可以显示
      hasDetailInfo(log) {
        // 调试输出
        console.log('检查日志是否有详情:', log);

        // 根据操作类型判断是否有详情可显示
        if (!log) return false;

        // 账号信息完善 - 放宽条件，不再严格检查improveContent
        if (log.type === 1) {
          // 只要是账号信息完善类型，就显示浮层
          return true;
        }

        // 其他操作类型
        // return log.type >= 2 && log.type <= 6;
      },
      // 格式化字段值，处理修改内容的展示
      formatFieldValue(value) {
        if (!value) return '-';

        // 检查是否包含修改内容
        if (typeof value === 'string' && value.includes('改为')) {
          // 提取原始值
          const baseValue = value.replace(/\s*\(.*\)/, '').trim();
          // 返回格式化后的值，包括修改内容
          return baseValue === '' ? '-' : baseValue;
        }

        return value;
      },
      // 解析账号信息完善-修改内容
      parseChangeContent(changeContent, log) {
        console.log('解析修改内容:', changeContent, log);

        if (!changeContent) return [];

        // 尝试不同的分隔符
        let arr = [];
        if (changeContent.includes(',')) {
          arr = changeContent.split(',');
        } else if (changeContent.includes('；')) {
          arr = changeContent.split('；');
        } else {
          arr = [changeContent]; // 单个修改项
        }

        return arr.map(item => {
          // 尝试多种格式匹配
          let key = '';

          // 格式1: "注册地"改为"
          const match1 = item.match(/(.+?)"改为"/);
          if (match1) {
            key = match1[1];
          }
          // 格式2: 注册地改为
          else if (item.includes('改为')) {
            key = item.split('改为')[0].trim();
          }
          // 其他格式，直接显示
          else {
            return item;
          }

          // 获取字段值
          let value = '';
          if (key === '注册地') value = log.registerAddress;
          else if (key === '粉丝数') value = log.fansCount;
          else if (key === '手机号') value = log.phone;
          else if (key === '主页链接') value = log.url;
          else if (key === '姓名') value = log.name;
          else if (key === '性别') value = log.sex;
          else if (key === '出生日期') value = log.date;
          else if (key === '身份证号') value = log.idCard;
          else if (key === '住址') value = log.address;
          else if (key === '注册邮箱') value = log.email;
          else if (key === '工作单位') value = log.workUnit;
          else if (key === '备注') value = log.remark;
          else value = log[key] || '';

          // 检查是否包含修改内容
          if (typeof value === 'string') {
            // 尝试多种格式匹配

            // 格式1: ("xxx"改为"yyy")
            const modifyMatch1 = value.match(/\(\"(.+?)\"改为\"(.*?)\"\)/);
            if (modifyMatch1) {
              const [_, oldValue, newValue] = modifyMatch1;
              return `${key}：${newValue || '-'} （"${oldValue}"改为"${newValue}"）`;
            }

            // 格式2: (xxx改为yyy)
            const modifyMatch2 = value.match(/\((.+?)改为(.*?)\)/);
            if (modifyMatch2) {
              const [_, oldValue, newValue] = modifyMatch2;
              return `${key}：${newValue || '-'} （${oldValue}改为${newValue}）`;
            }

            // 格式3: 直接包含"改为"
            if (value.includes('改为')) {
              const parts = value.split('改为');
              if (parts.length >= 2) {
                return `${key}：${parts[1] || '-'} （${parts[0]}改为${parts[1]}）`;
              }
            }
          }

          // 没有找到修改格式，直接显示值
          return `${key}：${value || '-'}`;
        });
      },
      // 解析账号信息完善-新增内容
      parseAddContent(log) {
        // 展示所有有值的字段
        const fields = [
          { label: '注册地', key: 'registerAddress' },
          { label: '粉丝数', key: 'fansCount' },
          { label: '手机号', key: 'phone' },
          { label: '主页链接', key: 'url' }
        ];
        return fields
          .filter(f => log[f.key])
          .map(f => `${f.label}：${this.formatFieldValue(log[f.key])}`);
      },
      // 解析人物信息完善内容
      parsePersonContent(log) {
        const fields = [
          { label: '姓名', key: 'name' },
          { label: '性别', key: 'sex' },
          { label: '出生日期', key: 'date' },
          { label: '身份证号', key: 'idCard' },
          { label: '住址', key: 'address' },
          { label: '手机号', key: 'phone' },
          { label: '注册邮箱', key: 'email' },
          { label: '工作单位', key: 'workUnit' },
          { label: '备注', key: 'remark' }
        ];
        return fields
          .filter(f => log[f.key])
          .map(f => `${f.label}：${this.formatFieldValue(log[f.key])}`);
      }
    },
    mounted() {
      this.fetchLogList();
      this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】操作日志浏览');
    }
  };
  </script>

  <style scoped>
  .logbody {
    padding: 24px 0;
    background-color: white;
    height: 100%;
    overflow: visible; /* 确保内容溢出时可见 */
    position: relative; /* 为加载动效提供定位参考 */
    min-height: 200px; /* 确保有足够的高度显示暂无数据 */
  }

  /* 加载动效样式 */
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
    to { transform: rotate(360deg); }
  }
  .info-card {
    background: #f4f4f4;
    color: #333;
    border-radius: 4px;
    padding: 16px 24px;
    margin: 0 auto 40px auto;
    width: 500px;
    font-size: 14px;
    line-height: 2;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  }
  .timeline-wrapper {
    height: calc(100% - 40px);
    overflow-x: auto; /* 允许横向滚动 */
    overflow-y: visible; /* 确保纵向内容不被裁剪 */
    max-width: 100%;
    display: flex;
    align-items: center;
    position: relative; /* 为绝对定位的子元素提供参考 */
    scrollbar-width: auto; /* Firefox */
    scrollbar-color: #bbb #f1f1f1; /* Firefox */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供平滑滚动 */
    margin-bottom: 10px; /* 确保滚动条有足够空间 */
  }

  /* 自定义滚动条样式 - Webkit浏览器 */
  .timeline-wrapper::-webkit-scrollbar {
    height: 10px; /* 横向滚动条高度 */
    width: 10px; /* 纵向滚动条宽度 */
  }

  .timeline-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1; /* 滚动条轨道背景色 */
    border-radius: 5px;
  }

  .timeline-wrapper::-webkit-scrollbar-thumb {
    background: #bbb; /* 滚动条滑块颜色 */
    border-radius: 5px;
  }

  .timeline-wrapper::-webkit-scrollbar-thumb:hover {
    background: #999; /* 鼠标悬停时滑块颜色 */
  }
  .timeline {
    display: flex;
    align-items: flex-start;
    min-width: 900px; /* 确保有足够的宽度 */
    padding: 0 24px 10px; /* 底部增加padding，确保滚动条可见 */
    position: relative;
    margin-bottom: 10px; /* 增加底部间距，确保滚动条有足够空间 */

  }
  .timeline-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    min-width: 200px;
    padding:25px 25px;

  }
  .timeline-title {
    color: #4C6DD9;
    font-size: 16px;
    margin-bottom: 16px;
    min-height: 24px;
    position: relative;
    cursor: default;
    text-align: center;
    font-weight: 500;
    position: absolute;
    top:-15px;
  }
  .timeline-title.hoverable {
    /* cursor: pointer; */
  }
  .timeline-title.hoverable:hover {
    color: #2a75d8;
    /* text-decoration: underline; */
  }
  .timeline-dot {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #4C6DD9;

    transition: all 0.2s;
    position: relative;
    z-index: 2;
  }
  .timeline-item.active .timeline-dot {
    background: #4C6DD9;

  }
  /* 添加连接线 */
  .timeline-line {
    position: absolute;
    top: 50%;
    right: 0;
    width: 100%;
    height: 2px;
    background-color: #E1E1E1;
    transform: translate(-50%,-50%);
    z-index: 1;
    left:0;
  }
  .timeline-item:nth-child(1) .timeline-line{
   display: none;
  }

  .timeline-info {
    text-align: center;
    font-size: 14px;
    color: #666;
    width: 100%;
    position: absolute;
    top:50px;
  }
  .timeline-time {
    margin-bottom: 0px;
    font-weight: 500;
  }
  .timeline-operator {
    font-size: 14px;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
    margin: 0 auto;
  }
  .detail-pop {
    position: fixed; /* 改为固定定位，不受滚动影响 */
    /* 位置将通过JS动态计算 */
    background: #fff;
    color: white;
    /* border: 1px solid #4b96f3; */
    border-radius: 4px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    padding: 16px 20px;
    min-width: 280px;
    z-index: 9999; /* 提高z-index确保在最上层 */
    font-size: 16px;
    line-height: 1.8;
    white-space: pre-line;
    text-align: left;
    max-width: 400px; /* 限制最大宽度 */
    background:rgba(0,0,0,0.6);


  }
  /* 浮层三角箭头 - 默认在底部 */
  /* .detail-pop:after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #fff;
    border-right: 1px solid #4b96f3;
    border-bottom: 1px solid #4b96f3;
    transform: translateX(-50%) rotate(45deg);
    z-index: 1;
  } */

  /* 当浮层在元素下方时的三角箭头样式 */
  .detail-pop.arrow-top:after {
    bottom: auto;
    top: -6px;
    border-right: none;
    border-bottom: none;
    border-left: 1px solid #4b96f3;
    border-top: 1px solid #4b96f3;
    transform: translateX(-50%) rotate(45deg);
  }
  .improve-type {
    font-weight: bold;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #e0e0e0;
    color: #4b96f3;
  }
  </style>