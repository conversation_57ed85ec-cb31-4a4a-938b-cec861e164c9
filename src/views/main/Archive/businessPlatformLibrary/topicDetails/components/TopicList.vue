<template>
  <div class="topic-list">
    <div class="list-table">
      <Spin v-if="loading" fix>
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>加载中...</div>
      </Spin>
      <Table
        v-if="!loading && list.length > 0"
        :columns="columns"
        :data="list"
        border
        stripe
        :loading="loading"
      >
        <template slot-scope="{ row, index }" slot="序号">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </template>

        <template slot-scope="{ row }" slot="单位名称">
          {{ row.dwmc || '-' }}
        </template>

        <template slot-scope="{ row }" slot="许可证编号">
          {{ row.xkzbh }}
        </template>

        <template slot-scope="{ row }" slot="互联网站">
          <span
            class="media-count"
            :class="{ 'has-count': row.hlwz > 0 }"
            @click="row.hlwz > 0 ? viewDetail(row, '互联网站') : null"
          >
            {{ row.hlwz }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="论坛">
          <span
            class="media-count"
            :class="{ 'has-count': row.lt > 0 }"
            @click="row.lt > 0 ? viewDetail(row, '论坛') : null"
          >
            {{ row.lt }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="博客">
          <span
            class="media-count"
            :class="{ 'has-count': row.bk > 0 }"
            @click="row.bk > 0 ? viewDetail(row, '博客') : null"
          >
            {{ row.bk }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="微博客">
          <span
            class="media-count"
            :class="{ 'has-count': row.wbk > 0 }"
            @click="row.wbk > 0 ? viewDetail(row, '微博客') : null"
          >
            {{ row.wbk }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="公众账号">
          <span
            class="media-count"
            :class="{ 'has-count': row.gzzh > 0 }"
            @click="row.gzzh > 0 ? viewDetail(row, '公众账号') : null"
          >
            {{ row.gzzh }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="应用程序">
          <span
            class="media-count"
            :class="{ 'has-count': row.yycx > 0 }"
            @click="row.yycx > 0 ? viewDetail(row, '应用程序') : null"
          >
            {{ row.yycx }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="网络直播">
          <span
            class="media-count"
            :class="{ 'has-count': row.wlzb > 0 }"
            @click="row.wlzb > 0 ? viewDetail(row, '网络直播') : null"
          >
            {{ row.wlzb }}
          </span>
        </template>

        <template slot-scope="{ row }" slot="其他">
          <span
            class="media-count"
            :class="{ 'has-count': row.qt > 0 }"
            @click="row.qt > 0 ? viewDetail(row, '其他') : null"
          >
            {{ row.qt }}
          </span>
        </template>

       

       
        <template slot-scope="{ row }" slot="修改时间">
          <span class="media-count" style="color:#000000">
            {{ formatTime(row.updateTime)}}
          </span>
        </template>
      </Table>

      <div class="no-data" v-if="!loading && list.length === 0">
        暂无数据
      </div>
    </div>

    <div class="pagination-container" v-if="total > 0">
      <Page
        :total="total"
        :current="pageNo"
        :page-size="pageSize"
        @on-change="pageNoChange"
        @on-page-size-change="pageSizeChange"
        show-total
        show-elevator
        show-sizer
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          title: '序号',
          slot: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '单位名称',
          slot: '单位名称',
          minWidth: 160,
          align: 'center'
        },
        {
          title: '许可证编号',
          slot: '许可证编号',
          width: 130,
          align: 'center'
        },
        {
          title: '互联网站',
          slot: '互联网站',
          width: 80,
          align: 'center'
        },
        {
          title: '论坛',
          slot: '论坛',
          width: 70,
          align: 'center'
        },
        {
          title: '博客',
          slot: '博客',
          width: 70,
          align: 'center'
        },
        {
          title: '微博客',
          slot: '微博客',
          width: 80,
          align: 'center'
        },
        {
          title: '公众账号',
          slot: '公众账号',
          width: 80,
          align: 'center'
        },
        {
          title: '应用程序',
          slot: '应用程序',
          width: 80,
          align: 'center'
        },
        {
          title: '网络直播',
          slot: '网络直播',
          width: 80,
          align: 'center'
        },
        {
          title: '其他',
          slot: '其他',
          width: 70,
          align: 'center'
        },
       
        {
          title: '修改时间',
          slot: '修改时间',
          width: 130,
          align: 'center'
        }
      ],
      list: [],
      total: 0,
      loading: false,
      pageNo: 1,
      pageSize: 10
    };
  },
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    queryParams: {
      handler(newVal) {
        if (newVal) {
          this.pageNo = 1;
          this.getList();
        }
      },
      deep: true
    }
  },
  methods: {
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) {
        return '-';
      }

      // 如果是时间戳（数字），转换为Date对象
      let date;
      if (typeof timestamp === 'number') {
        // 判断是秒级时间戳还是毫秒级时间戳
        date = timestamp.toString().length === 10
          ? new Date(timestamp * 1000)
          : new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        // 如果是字符串，尝试解析
        date = new Date(timestamp);
      } else {
        return '-';
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '-';
      }

      // 格式化为 YYYY-MM-DD
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },

    // 获取主体详情列表 - 使用商业平台库的API
    getList() {
      this.loading = true;

      const params = {
        ...this.queryParams,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      };
      console.log('商业平台库主体详情请求参数:', params);
      
      // 使用商业平台库的API接口
      this.$http.get('/resourceslibrary/mainBodyList', { params }).then(res => {
        if (res.body.status === 0) {
          this.list = res.body.data.list || [];
          this.total = res.body.data.count || 0;
        } else {
          this.$Message.error(res.body.message || '获取数据失败');
        }
        this.loading = false;
      }).catch(err => {
        console.error('获取商业平台库主体详情列表失败', err);
        this.$Message.error('获取数据失败');
        this.loading = false;
      });
    },

    // 分页处理
    pageNoChange(pageNo) {
      this.pageNo = pageNo;
      this.getList();
    },

    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNo = 1;
      this.getList();
    },

    // 查看详情 - 跳转到商业平台库的详情页
    viewDetail(row, channel) {
      this.$router.push({
        path: '/main/Archive/businessPlatformLibrary/topicDetails/detail',
        query: {
          licenseNumber: row.xkzbh,
          channel
        }
      });
    },

    // 修改媒体分类
    changeMediaClass(row, value) {
      // 这里可以调用商业平台库的修改接口
      // 暂时使用原有的接口，实际项目中可能需要修改
      this.$http.post(`/media/editClass?licenseNumber=${row.xkzbh}&mediaClassification=${value}`).then(res => {
        if (res.body.status === 0) {
          this.$Message.success(res.body.message || '修改成功');
        } else {
          this.$Message.error(res.body.message || '修改失败');
          // 恢复原值
          this.getList();
        }
      }).catch(err => {
        console.error('修改媒体分类失败', err);
        this.$Message.error('修改失败');
        // 恢复原值
        this.getList();
      });
    }
  },
  mounted() {
    this.getList();
  }
};
</script>

<style lang="less" scoped>
// vue 样式没生效 这么处理
/deep/ .ivu-table-border th{
    height: 60px;
    background:#E6F7FF;
    font-size:16px;
    font-weight: bold;
    border-color:#d7d7d7;
  }
  /deep/ .ivu-table-cell{
    padding:0 5px;
  }
  /deep/ .ivu-table-border td, .ivu-table-border th{
    border-color:#D7D7D7;
    font-size: 16px;
    height:60px;
  }
.topic-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-top:20px;
  margin-right: 16px;

  .list-table {
    flex: 1;
    position: relative;

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: #999;
      font-size: 14px;
    }

    .media-count {
      display: inline-block;
      width: 100%;
      text-align: center;
      color: #999;

      &.has-count {
        color: #5585ec;
        cursor: pointer;
        font-weight: 600;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    text-align: right;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
