<template>
  <div class="topic-filter">
    <div class="header flex sb">
      <div class="title">筛选</div>
      <div class="controls flex">
        <div class="item cp" @click="handleQuery">查询</div>
      </div>
    </div>
    <div class="conditions">
      <!-- 查找 -->
      <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">查找</div>
        </div>
        <div class="options flex">
          <div class="search">
          <Input
            v-model="keyword"
            style="width: 100%;"
          >
            <Select v-model="params.searchType" slot="prepend" style="width: 110px;">
              <Option value="1">按单位名称查找</Option>
              <Option value="2">按许可证编号查找</Option>
            </Select>
          </Input>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      keyword: "",
      params: {
        searchType: "1", // 查找类型，单选
      },
      zhutilist:[
        "中央互联网新闻信息服务单位",
        "北京",
        "天津",
        "河北",
        "山西",
        "内蒙古",
        "辽宁",
        "吉林",
        "黑龙江",
        "上海",
        "江苏",
        "浙江",
        "安徽",
        "福建",
        "江西",
        "山东",
        "河南",
        "湖北",
        "湖南",
        "广东",
        "广西",
        "海南",
        "重庆",
        "四川",
        "贵州",
        "云南",
        "西藏",
        "陕西",
        "甘肃",
        "青海",
        "宁夏",
        "新疆",
        "新疆生产建设兵团"
      ]
    };
  },
  components: {},
  props: {},
  created() {},
  methods: {
    // 搜索
    search() {
      this.handleQuery();
    },

    // 重置
    reset() {
      this.keyword = "";
      this.params = {
        
        searchType: "1",
      };
      this.handleQuery();
    },

    // 查询
    handleQuery() {
      let params = {
        ...this.params,
        keyWord: this.keyword,
      };
      this.$emit("query", params);
    },

    
  }
};
</script>

<style lang="less" scoped>
.search{width: 100%;}
.topic-filter {
  margin-top:20px;
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  padding: 10px 10px;

  .header {
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;

    .title {
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
      font-size: 16px;
      line-height: 16px;
    }

    .controls {
      .item {
        text-align: center;
        line-height: 30px;
        font-weight: 600;
        color: #ffffff;
        font-size: 14px;
        width: 80px;
        height: 30px;
        border-radius: 4px;
        background: #5585ec;
        margin-left: 10px;
      }

      .reset {
        background: #f0f0f0;
        color: #666;
      }
    }
  }

  .conditions {
    height: calc(~"100% - 50px");
    overflow-y: auto;

    & > .item {
      margin-top: 10px;
      position: relative;

      .label {
        align-items: center;

        .line {
          width: 3px;
          height: 12px;
          background: #537be6;
          margin-right: 5px;
        }

        .title {
          color: #333333;
          font-size: 14px;
          line-height: 30px;
        }
      }

      .options {
        flex-wrap: wrap;

        .item {
          min-width: 40px;
          margin-top: 10px;
          margin-right: 10px;
          padding: 0 10px;
          height: 26px;
          border: 1px solid #c4c3c3;
          border-radius: 2px;
          line-height: 24px;
          text-align: center;
          font-size: 14px;
        }

        .active {
          border-color: #537be6;
          color: #537be6;
        }
      }

      .select-container {
        margin-top: 10px;

        /deep/ .ivu-select-selection {
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
