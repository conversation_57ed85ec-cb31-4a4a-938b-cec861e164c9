<template>
    <div class="JiNanMonitor">
        <!-- <FrameName /> -->
        <SelectSituation
        :list="routerList"
        :selectId="selectId"
        @change="situationChange"
        />
        <div class="content">
        <RouterView />
        </div>
    </div>
</template>



<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import SelectSituation from "@/components/selectSituation/index.vue";

const routerList = {
  "/main/Archive/businessPlatformLibrary/topicDetails": "主体详情",
  "/main/Archive/businessPlatformLibrary/platformList": "商业平台清单",
};
export default {
  name: "index.vue",
  data() {
    // 这里存放数据
    return {
      routerList,
      selectId: null
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { SelectSituation },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    let stairRoute = this.$router.options.routes[1].children;
    for (let i = 0; i < stairRoute.length; i++) {
      if (this.$route.path.indexOf(stairRoute[i].path) !== -1) {
        this.navList = stairRoute[i].children;
        break;
      }
    }
  },
  // 方法集合
  methods: {
    // 跳转路由
    goPath(d) {
      this.$router.push(d.path);
    },
    situationChange(d) {
      console.log(d);
      this.selectId = d;
      this.$router.push(d);
    }
  },

  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    // 监听路由变化
    '$route.path': {
      handler(newPath) {
        // 处理 topicDetails/detail 路由，使其高亮 "主体详情" 标签
        if (newPath.includes('/main/Archive/businessPlatformLibrary/topicDetails/detail')) {
          this.selectId = '/main/Archive/businessPlatformLibrary/topicDetails';
        } else {
          this.selectId = newPath;
        }
      }
    }
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    console.log(this.$route.path);
    // 处理 topicDetails/detail 路由，使其高亮 "主体详情" 标签
    if (this.$route.path.includes('/main/Archive/businessPlatformLibrary/topicDetails/detail')) {
      this.selectId = '/main/Archive/businessPlatformLibrary/topicDetails';
    } else {
      this.selectId = this.$route.path;
      this.getLog("全息档案库/商业平台库", '浏览/【商业平台库】主体详情页浏览');
    }
  }
};
</script>

<style lang="less" scoped>
.JiNanMonitor {
  height: 100%;
  display: flex;
  flex-direction: column;

  .nav {
    display: flex;
    padding: 20px 0;

    .item {
      cursor: pointer;
      margin-right: 20px;
      width: 160px;
      line-height: 40px;
      text-align: center;
      background: #ebedf8;
      border-radius: 4px;
      font-size: 16px;
      color: #666;
      box-shadow: 0 3px 6px rgba(80, 80, 80, 0.22);
    }

    .active {
      background-color: #5585ec;
      font-weight: 600;
      color: #ffffff;
    }

    .splitLine {
      width: 2px;
      height: 40px;
      background-color: #5585ec;
    }
  }

  .content {
    flex: 1;
  }
}
</style>
