<template>
  <div class="topic-filter">
    <div class="header flex sb">
      <div class="title">筛选</div>
      <div class="controls flex">
        <div class="item cp" @click="handleQuery">查询</div>
      </div>
    </div>
    <div class="conditions">

      <!-- 属地 -->
      <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">属地</div>
        </div>
        <div class="options flex">
          <!-- 全部 -->
          <div
            :class="[
              'item',
              'cp',
              params.dependency.length === 0 ? 'active' : '',
            ]"
            @click="selectDependency('all')"
          >
            全部
          </div>

          <!-- 属地列表 -->
          <div
            v-for="item in dependencyList"
            :key="item"
            :class="[
              'item',
              'cp',
              params.dependency.includes(item) ? 'active' : '',
            ]"
            @click="selectDependency(item)"
          >
            {{item}}
          </div>
        </div>
      </div>
      <!-- 渠道 -->
      <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">渠道</div>
        </div>
        <div class="options flex">
          <!-- 全部 -->
          <div
            :class="[
              'item',
              'cp',
              params.channel.length === 0 ? 'active' : '',
            ]"
            @click="selectChannel('all')"
          >
            全部
          </div>

          <!-- 渠道列表 -->
          <div
            v-for="item in channelList"
            :key="item"
            :class="[
              'item',
              'cp',
              params.channel.includes(item) ? 'active' : '',
            ]"
            @click="selectChannel(item)"
          >
            {{item}}
          </div>
        </div>
      </div>

      

      <!-- 舆论场 -->
      <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">舆论场</div>
        </div>
        <div class="options flex">
          <!-- 全部 -->
          <div
            :class="[
              'item',
              'cp',
              params.situation.length === 0 ? 'active' : '',
            ]"
            @click="selectSituation('all')"
          >
            全部
          </div>

          <!-- 舆论场列表 -->
          <div
            v-for="item in situationList"
            :key="item.value"
            :class="[
              'item',
              'cp',
              params.situation.includes(item.value) ? 'active' : '',
            ]"
            @click="selectSituation(item.value)"
          >
            {{item.label}}
          </div>
        </div>
      </div>

      <!-- 服务形式 -->
      <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">服务形式</div>
        </div>
        <div class="options flex">
          <!-- 全部 -->
          <div
            :class="[
              'item',
              'cp',
              params.mediaType.length === 0 ? 'active' : '',
            ]"
            @click="selectMediaType('all')"
          >
            全部
          </div>

          <!-- 服务形式列表 -->
          <div
            v-for="item in mediaTypeList"
            :key="item.value"
            :class="[
              'item',
              'cp',
              params.mediaType.includes(item.value) ? 'active' : '',
            ]"
            @click="selectMediaType(item.value)"
          >
            {{item.label}}
          </div>
        </div>
      </div>


      <!-- 查找 -->
      <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">查找</div>
        </div>
        <div class="options flex">
          <div class="search">
          <Input
            v-model="keyword"
            style="width: 100%;"
          >
            <!-- <Select v-model="params.searchType" slot="prepend" style="width: 110px;">
              <Option value="1">按单位名称查找</Option>
              <Option value="2">按服务地址查找</Option>
            </Select> -->
            </Input>
          </div>
        </div>
      </div>
      <!-- 申请主体 -->
      <!-- <div class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">申请主体</div>
        </div>
        <div class="options flex">

          <div
            :class="[
              'item',
              'cp',
              params.applicationSubject == '' ? 'active' : '',
            ]"
            @click="selectApplicationSubject('all')"
          >
            全部
          </div>

     
          <div
            v-for="item in zhutilist"
            :key="item"
            :class="[
              'item',
              'cp',
              params.applicationSubject == item ? 'active' : '',
            ]"
            @click="selectApplicationSubject(item)"
          >
            {{item}}
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import { getAllYulunchanMap } from '@/utils/yulunchang'

export default {
  data() {
    return {
      keyword: "",
      params: {
        channel: [], // 渠道，多选
        dependency: [], // 属地，多选
        situation: [], // 舆论场，多选
        mediaType: [], // 服务形式，多选
        searchType: "3", // 查找类型，单选
        applicationSubject: '', // 申请主体，单选
      },
      // 渠道列表
      channelList: [
        "互联网站",
        "论坛",
        "博客",
        "微博客",
        "公众账号",
        "应用程序",
        "网络直播",
        "其他"
      ],
      // 属地列表
      dependencyList: [
        "中央",
        "北京",
        "天津",
        "河北",
        "山西",
        "内蒙古",
        "辽宁",
        "吉林",
        "黑龙江",
        "上海",
        "江苏",
        "浙江",
        "安徽",
        "福建",
        "江西",
        "山东",
        "河南",
        "湖北",
        "湖南",
        "广东",
        "广西",
        "海南",
        "重庆",
        "四川",
        "贵州",
        "云南",
        "西藏",
        "陕西",
        "甘肃",
        "青海",
        "宁夏",
        "新疆"
      ],
      // 省份名称映射
      provinceMap: {
        "3": "北京",
        "4": "天津",
        "5": "河北",
        "6": "山西",
        "7": "内蒙古",
        "8": "辽宁",
        "9": "吉林",
        "10": "黑龙江",
        "11": "上海",
        "12": "江苏",
        "13": "浙江",
        "14": "安徽",
        "15": "福建",
        "16": "江西",
        "17": "山东",
        "18": "河南",
        "19": "湖北",
        "20": "湖南",
        "21": "广东",
        "22": "广西",
        "23": "海南",
        "24": "重庆",
        "25": "四川",
        "26": "贵州",
        "27": "云南",
        "28": "西藏",
        "29": "陕西",
        "30": "甘肃",
        "31": "青海",
        "32": "宁夏",
        "33": "新疆",
        "34": "新疆生产建设兵团"
      },
      zhutilist:[
        "中央互联网新闻信息服务单位",
        "其他互联网新闻信息服务单位"
      ],
      // 舆论场列表 - 使用统一的 yulunchang.js 数据
      situationList: [],
      // 服务形式列表
      mediaTypeList: [
        { label: "传统媒体", value: 0 },
        { label: "新媒体", value: 1 }
      ],
    };
  },
  components: {},
  props: {},
  created() {
    // 初始化舆论场列表，使用统一的 yulunchang.js 数据
    this.initSituationList();
  },
  methods: {
    // 初始化舆论场列表
    initSituationList() {
      const yulunchanMap = getAllYulunchanMap();
      this.situationList = Object.entries(yulunchanMap).map(([value, label]) => ({
        label,
        value: Number(value)
      }));
    },

    // 搜索
    search() {
      this.handleQuery();
    },

    // 重置
    reset() {
      this.keyword = "";
      this.params = {
        channel: [],
        dependency: [],
        situation: [],
        mediaType: [],
        searchType: "3",
        applicationSubject: '',
      };
      this.handleQuery();
    },

    // 查询
    handleQuery() {
      // 创建一个新的参数对象
      let params = {
        keyWord: this.keyword,
        searchType: this.params.searchType,
        applicationSubject: this.params.applicationSubject,
      };

      // 处理渠道参数，多选时用逗号分隔
      if (this.params.channel && this.params.channel.length > 0) {
        // 直接传递数组，让父组件处理
        params.channel = [...this.params.channel];
      }

      // 处理属地参数，多选时用逗号分隔
      if (this.params.dependency && this.params.dependency.length > 0) {
        // 直接传递数组，让父组件处理
        params.dependency = [...this.params.dependency];
      }

      // 处理舆论场参数，多选时用逗号分隔
      if (this.params.situation && this.params.situation.length > 0) {
        // 直接传递数组，让父组件处理
        params.situation = [...this.params.situation];
      }

      // 处理服务形式参数，多选时用逗号分隔
      if (this.params.mediaType && this.params.mediaType.length > 0) {
        // 直接传递数组，让父组件处理
        params.mediaType = [...this.params.mediaType];
      }

      console.log('筛选参数:', params);
      this.$emit("query", params);
    },

    // 选择渠道
    selectChannel(value) {
      if (value === 'all') {
        this.params.channel = [];
        return;
      }

      if (this.params.channel.includes(value)) {
        // 如果已选中，则取消选中
        const index = this.params.channel.indexOf(value);
        this.params.channel.splice(index, 1);
      } else {
        // 如果未选中，则添加选中
        this.params.channel.push(value);
      }
    },

    // 选择属地
    selectDependency(value) {
      if (value === 'all') {
        this.params.dependency = [];
        return;
      }

      if (this.params.dependency.includes(value)) {
        // 如果已选中，则取消选中
        const index = this.params.dependency.indexOf(value);
        this.params.dependency.splice(index, 1);
      } else {
        // 如果未选中，则添加选中
        this.params.dependency.push(value);
      }
    },
    selectApplicationSubject(value){
      if(value == 'all'){
        this.params.applicationSubject = '';
        return;
      }
      this.params.applicationSubject = value;
    },

    // 选择舆论场
    selectSituation(value) {
      if (value === 'all') {
        this.params.situation = [];
        return;
      }

      if (this.params.situation.includes(value)) {
        // 如果已选中，则取消选中
        const index = this.params.situation.indexOf(value);
        this.params.situation.splice(index, 1);
      } else {
        // 如果未选中，则添加选中
        this.params.situation.push(value);
      }
    },

    // 选择媒体类型
    selectMediaType(value) {
      if (value === 'all') {
        this.params.mediaType = [];
        return;
      }

      if (this.params.mediaType.includes(value)) {
        // 如果已选中，则取消选中
        const index = this.params.mediaType.indexOf(value);
        this.params.mediaType.splice(index, 1);
      } else {
        // 如果未选中，则添加选中
        this.params.mediaType.push(value);
      }
    },

    // 根据ID获取省份名称
    getProvinceNameById(id) {
      return this.provinceMap[id.toString()] || `未知地区${id}`;
    },

    // 更新渠道筛选器
    updateChannelFilter(channels) {
      // 确保 channels 是数组
      if (!Array.isArray(channels)) {
        if (typeof channels === 'string') {
          // 如果是字符串，转换为数组
          this.params.channel = channels.split(',');
        } else {
          // 如果不是字符串，设置为空数组
          this.params.channel = [];
        }
      } else {
        // 如果已经是数组，直接赋值
        this.params.channel = [...channels];
      }
    },

    // 更新属地筛选器
    updateDependencyFilter(dependencies) {
      // 确保 dependencies 是数组
      if (!Array.isArray(dependencies)) {
        if (typeof dependencies === 'string') {
          // 如果是字符串，转换为数组
          this.params.dependency = dependencies.split(',');
        } else {
          // 如果不是字符串，设置为空数组
          this.params.dependency = [];
        }
      } else {
        // 如果已经是数组，直接赋值
        this.params.dependency = [...dependencies];
      }
    },
    updateApplicationSubject(applicationSubject){
      this.params.applicationSubject = applicationSubject;
    },

    // 更新舆论场筛选器
    updateSituationFilter(situations) {
      // 确保 situations 是数组
      if (!Array.isArray(situations)) {
        if (typeof situations === 'string') {
          // 如果是字符串，转换为数组
          this.params.situation = situations.split(',');
        } else {
          // 如果不是字符串，设置为空数组
          this.params.situation = [];
        }
      } else {
        // 如果已经是数组，直接赋值
        this.params.situation = [...situations];
      }
    },

    // 更新媒体类型筛选器
    updateMediaTypeFilter(mediaTypes) {
      // 确保 mediaTypes 是数组
      if (!Array.isArray(mediaTypes)) {
        if (typeof mediaTypes === 'string') {
          // 如果是字符串，转换为数组
          this.params.mediaType = mediaTypes.split(',');
        } else {
          // 如果不是字符串，设置为空数组
          this.params.mediaType = [];
        }
      } else {
        // 如果已经是数组，直接赋值
        this.params.mediaType = [...mediaTypes];
      }
    }
  }
};
</script>

<style lang="less" scoped>
.search{width: 100%;}
.topic-filter {
  margin-top:20px;
  width: 300px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  padding: 10px 10px;

  .header {
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;

    .title {
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
      font-size: 16px;
      line-height: 16px;
    }

    .controls {
      .item {
        text-align: center;
        line-height: 30px;
        font-weight: 600;
        color: #ffffff;
        font-size: 14px;
        width: 80px;
        height: 30px;
        border-radius: 4px;
        background: #5585ec;
        margin-left: 10px;
      }

      .reset {
        background: #f0f0f0;
        color: #666;
      }
    }
  }

  .conditions {
    height: calc(~"100% - 50px");
    overflow-y: auto;

    & > .item {
      margin-top: 10px;
      position: relative;

      .label {
        align-items: center;

        .line {
          width: 3px;
          height: 12px;
          background: #537be6;
          margin-right: 5px;
        }

        .title {
          color: #333333;
          font-size: 14px;
          line-height: 30px;
        }
      }

      .options {
        flex-wrap: wrap;

        .item {
          min-width: 40px;
          margin-top: 10px;
          margin-right: 10px;
          padding: 0 10px;
          height: 26px;
          border: 1px solid #c4c3c3;
          border-radius: 2px;
          line-height: 24px;
          text-align: center;
          font-size: 14px;
        }

        .active {
          border-color: #537be6;
          color: #537be6;
        }
      }

      .select-container {
        margin-top: 10px;

        /deep/ .ivu-select-selection {
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
