<template>
  <div class="topic-list">
    <!-- <div class="list-header">
      <div class="header-title">主体详情</div>
      <div class="header-count" v-if="total > 0">共 <span class="count-num">{{ total }}</span> 条数据</div>
    </div> -->

    <div class="list-table">
      <Spin v-if="loading" fix>
        <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
        <div>加载中...</div>
      </Spin>
      <Table
        v-if="!loading && list.length > 0"
        :columns="columns"
        :data="list"
        border
        stripe
        :loading="loading"
      >
        <template slot-scope="{ row, index }" slot="序号">
          {{ (pageNo - 1) * pageSize + index + 1 }}
        </template>
        <template slot-scope="{ row }" slot="服务名称">
          <span style="cursor: pointer;" @click="goDetail(row)">{{ factorSName(row) }}</span>
          <svg-icon icon-class="电话" v-if="row.hasYinsi && row.hasYinsi==1" style="width: 16px;height: 16px;" />
        </template>
        <template slot-scope="{ row }" slot="服务地址">
          {{ getServiceAddress(row) }}
        </template>
        <template slot-scope="{ row }" slot="舆论场">
          <Select v-model="row.situation" style="width: 100%;" placeholder="请选择舆论场" @on-change="updateSituation(row)">
            <Option
              v-for="option in situationOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </Option>
          </Select>
        </template>
        <template slot-scope="{ row }" slot="服务形式">
          {{ getMediaTypeLabel(row.mediaType) }}
        </template>
      </Table>

      <div class="no-data" v-if="!loading && list.length === 0">
        暂无数据
      </div>
    </div>

    <div class="pagination-container" v-if="total > 0">
      <Page
        :total="total"
        :current="pageNo"
        :page-size="pageSize"
        @on-change="pageNoChange"
        @on-page-size-change="pageSizeChange"
        show-total
        show-elevator
        show-sizer
      />
    </div>
  </div>
</template>

<script>
import { getAllYulunchanMap, getYulunchangName } from '@/utils/yulunchang'

export default {
  data() {
    return {
      columns: [
        {
          title: '序号',
          slot: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '服务名称',
          slot:'服务名称',
          minWidth: 150,
          align: 'center'
        },
        {
          title: '服务地址/平台',
          slot: '服务地址',
          minWidth: 150,
          align: 'center'
        },
        {
          title: '渠道',
          key: 'channel',
          width: 120,
          align: 'center'
        },
        {
          title: '舆论场',
          slot: '舆论场',
          width: 120,
          align: 'center'
        },
        {
          title: '属地',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('span', this.getDependencyLabel(params.row.dependency));
          }
        },
        {
          title: '服务形式',
          slot: '服务形式',
          width: 120,
          align: 'center'
        },
        {
          title: '修改时间',
          key: 'createTime',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('span', this.formatTime(params.row.createTime));
          }
        }
      ],
      list: [],
      total: 0,
      loading: false,
      pageNo: 1,
      pageSize: 10,
      // 舆论场选项 - 使用统一的 yulunchang.js 数据
      situationOptions: []
    };
  },
  props: {
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    // 移除 queryParams 监听器，避免重复请求
  },
  methods: {
    goDetail(item) {
      if(!item.userId || item.userId==""){
        //提示 "该媒体没有账号ID，暂时无法查看详情页"
        this.$Message.warning('该媒体没有账号ID，暂时无法查看详情页');
        return 
      }
      const { href } = this.$router.resolve({
        path: "/main/mediaLibraryDetail",
        query: {
          // id: item.id,
          ukey: item.userId,
          situation: item.situation,

        },
      });
      window.open(href, "_blank");
    },
    // 初始化舆论场选项
    initSituationOptions() {
      const yulunchanMap = getAllYulunchanMap();
      this.situationOptions = Object.entries(yulunchanMap).map(([value, label]) => ({
        value,
        label
      }));
    },

    // 获取媒体清单列表
    getList() {
      this.loading = true;

      // 创建一个新的参数对象，避免修改原始对象
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize
      };

      // 处理查询参数，确保数组参数正确传递
      if (this.queryParams) {
        // 处理渠道参数
        if (this.queryParams.channel && this.queryParams.channel.length > 0) {
          if (Array.isArray(this.queryParams.channel)) {
            params.channel = this.queryParams.channel.join(',');
          } else {
            params.channel = this.queryParams.channel;
          }
        }

        // 处理属地参数
        if (this.queryParams.dependency && this.queryParams.dependency.length > 0) {
          if (Array.isArray(this.queryParams.dependency)) {
            params.dependency = this.queryParams.dependency.join(',');
          } else {
            params.dependency = this.queryParams.dependency;
          }
        }

        // 处理舆论场参数
        if (this.queryParams.situation && this.queryParams.situation.length > 0) {
          if (Array.isArray(this.queryParams.situation)) {
            params.situation = this.queryParams.situation.join(',');
          } else {
            params.situation = this.queryParams.situation;
          }
        }

        // 处理服务形式参数
        if (this.queryParams.mediaType && this.queryParams.mediaType.length > 0) {
          if (Array.isArray(this.queryParams.mediaType)) {
            params.mediaType = this.queryParams.mediaType.join(',');
          } else {
            params.mediaType = this.queryParams.mediaType;
          }
        }

        // 处理其他参数
        if (this.queryParams.dataType) {
          params.dataType = this.queryParams.dataType;
        }

        if (this.queryParams.keyWord) {
          params.keyWord = this.queryParams.keyWord;
        }

        if (this.queryParams.searchType) {
          params.searchType = this.queryParams.searchType;
        }
        if (this.queryParams.applicationSubject) {
          params.applicationSubject = this.queryParams.applicationSubject;
        }
      }

      // 不再区分传统媒体和新媒体，整合为一个列表
      console.log('请求参数:', JSON.stringify(params));
      this.$http.get('/media/mediaList', { params }).then(res => {
        if (res.body.status === 0) {
          const list = res.body.data.list || [];
          // 为每个项目添加默认的舆论场值和服务形式，确保数据类型为字符串
          this.list = list.map(item => ({
            ...item,
            situation: item.situation ? String(item.situation) : null, // 确保为字符串类型
            mediaType: this.getServiceType(item)
          }));
          this.total = res.body.data.count || 0;
        } else {
          this.$Message.error(res.body.message || '获取数据失败');
        }
        this.loading = false;
      }).catch(err => {
        console.error('获取媒体清单列表失败', err);
        this.$Message.error('获取数据失败');
        this.loading = false;
      });
    },
    factorSName(row){
      var ba=row.isRegister == 1 ? '' : '(未备案)'
      return row.serviceName + ba;
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) {
        return '-';
      }

      // 如果是时间戳（数字），转换为Date对象
      let date;
      if (typeof timestamp === 'number') {
        // 判断是秒级时间戳还是毫秒级时间戳
        date = timestamp.toString().length === 10
          ? new Date(timestamp * 1000)
          : new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        // 如果是字符串，尝试解析
        date = new Date(timestamp);
      } else {
        return '-';
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '-';
      }

      // 格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} `;
    },
    // 获取服务地址/平台显示内容
    getServiceAddress(row) {
      // 互联网站、论坛、博客、微博有服务地址
      if (['互联网站', '论坛', '博客', '微博客'].includes(row.channel)) {
        return row.serviceAddress || '-';
      }

      // 应用程序、网络直播、其他没有服务地址
      if (['应用程序', '网络直播', '其他'].includes(row.channel)) {
        return '-';
      }

      // 公众账号显示平台
      if (row.channel === '公众账号') {
        return row.serviceAddress || '-';
      }

      return row.serviceAddress || '-';
    },

    // 获取服务形式（保留兼容性）
    getServiceType(row) {
      return this.getMediaTypeLabel(row.mediaType);
    },

    // 获取媒体类型标签
    getMediaTypeLabel(mediaType) {
      // 根据mediaType字段判断服务形式
      var mediaType=Number(mediaType)
      if (mediaType === 0) {
        return '传统媒体';
      } else if (mediaType === 1) {
        return '新媒体';
      } else {
        return '-'; // 默认值
      }
    },

    // 获取舆论场标签 - 使用统一的 yulunchang.js 工具函数
    getSituationLabel(value) {
      return getYulunchangName(value);
    },

    // 获取属地标签 - 有数据显示数据，没有数据显示"-"
    getDependencyLabel(value) {
      if (!value && value !== 0) {
        return '-';
      }
      return value;
    },

    // 更新舆论场
    async updateSituation(row) {
      console.log('舆论场变化:', row.id, row.situation);

      try {
        const params = {
          id: row.id,
          situation: row.situation
        };

        // 使用GET请求调用舆论场更新接口
        const response = await this.$http.get('/media/updateSituation', { params });

        if (response.body.status === 0) {
          this.$Message.success(response.body.message || '舆论场信息修改成功');
          console.log('舆论场已更新:', {
            id: row.id,
            situation: row.situation,
            label: this.getSituationLabel(row.situation)
          });
        } else {
          this.$Message.error(response.body.message || '修改失败');
          // 如果失败，重新获取数据以恢复原值
          this.getList();
        }
      } catch (error) {
        console.error('修改舆论场失败:', error);
        this.$Message.error('修改失败');
        // 如果失败，重新获取数据以恢复原值
        this.getList();
      }
    },

    // 分页处理
    pageNoChange(pageNo) {
      this.pageNo = pageNo;
      this.getList();
    },

    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNo = 1;
      this.getList();
    },

    // 这里不需要查看详情和修改媒体分类的方法
  },
  mounted() {
    // 初始化舆论场选项
    this.initSituationOptions();
    // 不在组件挂载时自动调用 getList 方法，避免重复请求
  }
};
</script>

<style lang="less" scoped>
// vue 样式没生效 这么处理

/deep/ .ivu-table-border th{
    height: 60px;
    background:#E6F7FF;
    font-size:16px;
    font-weight: bold;
    border-color:#d7d7d7;
  }
  /deep/ .ivu-table-cell{
    padding:0 5px;
  }
  /deep/ .ivu-table-border td, .ivu-table-border th{
    border-color:#D7D7D7;
    font-size: 16px;
    height:60px;
  }

  // 舆论场下拉选择框样式
  /deep/ .ivu-select {
    .ivu-select-selection {
      border: 1px solid #dcdee2;
      border-radius: 4px;
      height: 32px;

      .ivu-select-selected-value {
        font-size: 14px;
        line-height: 30px;
      }
    }

    .ivu-select-arrow {
      font-size: 14px;
    }
  }
.topic-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-top:20px;
  margin-right: 16px;



  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .header-count {
      font-size: 14px;
      color: #666;

      .count-num {
        color: #5585ec;
        font-weight: 600;
      }
    }
  }

  .list-table {
    flex: 1;
    position: relative;

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: #999;
      font-size: 14px;
    }

    .media-count {
      display: inline-block;
      width: 100%;
      text-align: center;
      color: #999;

      &.has-count {
        color: #5585ec;
        cursor: pointer;
        font-weight: 600;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    text-align: right;
  }
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
