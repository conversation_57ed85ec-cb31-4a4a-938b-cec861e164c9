<template>
    <div class="media-list">
        <div class="list-view flex">

        <MediaList
            :queryParams="queryParams"
            ref="MediaList"
        />
        <MediaFilters @query="handleQuery" ref="MediaFilters" />
        </div>
    </div>
</template>

<script>
import MediaFilters from "./components/MediaFilters.vue";
import MediaList from "./components/MediaList.vue";
export default {
    data() {
        return {
            // 查询参数
            queryParams: {},

            // 路由参数
            dataType: '',  // 数据类型：centreResult, otherResult, proResult
            channelParam: '', // 渠道参数
            dependencyParam: '', // 属地参数

            // 列表数据
            list: [],
            total: 0,
            loading: false,
            pageNo: 1,
            pageSize: 10,

            // 排序参数
            orderBy: 'createTime',
            orderByType: 'desc',

            // 控制标志
            isUpdatingUrl: false // 标记是否正在更新URL，避免重复调用
        };
    },
    components: {
        MediaFilters,
        MediaList
    },
    props: {},
    created() {
        // 设置标志位，表示正在初始化
        this.isUpdatingUrl = true;

        // 获取路由参数
        if (this.$route.query.dataType) {
            this.dataType = this.$route.query.dataType;
        }

        // 处理渠道参数
        if (this.$route.query.channel) {
            this.channelParam = this.$route.query.channel;

            // 将 channel 添加到查询参数中
            // 如果 channel 包含逗号，说明是多选
            if (this.channelParam.includes(',')) {
                this.queryParams.channel = this.channelParam.split(',');
            } else {
                this.queryParams.channel = [this.channelParam];
            }
        } else if (this.$route.query.channelName) {
            // 兼容旧的 channelName 参数
            this.channelParam = this.$route.query.channelName;

            // 将 channelName 添加到查询参数中
            // 如果 channelName 包含逗号，说明是多选
            if (this.channelParam.includes(',')) {
                this.queryParams.channel = this.channelParam.split(',');
            } else {
                this.queryParams.channel = [this.channelParam];
            }
        }

        // 处理属地参数
        if (this.$route.query.dependency) {
            this.dependencyParam = this.$route.query.dependency;

            // 将 dependency 添加到查询参数中
            // 如果 dependency 包含逗号，说明是多选
            if (this.dependencyParam.includes(',')) {
                this.queryParams.dependency = this.dependencyParam.split(',');
            } else {
                this.queryParams.dependency = [this.dependencyParam];
            }
        } else if (this.$route.query.dependencyName) {
            // 兼容旧的 dependencyName 参数
            this.dependencyParam = this.$route.query.dependencyName;

            // 将 dependencyName 添加到查询参数中
            // 如果 dependencyName 包含逗号，说明是多选
            if (this.dependencyParam.includes(',')) {
                this.queryParams.dependency = this.dependencyParam.split(',');
            } else {
                this.queryParams.dependency = [this.dependencyParam];
            }
        }
        if (this.$route.query.applicationSubject) {
            this.queryParams.applicationSubject = this.$route.query.applicationSubject;
        }

        // 更新筛选器的渠道值和属地值
        this.$nextTick(() => {
            if (this.$refs.MediaFilters && this.queryParams.channel) {
                this.$refs.MediaFilters.updateChannelFilter(this.queryParams.channel);
            }

            if (this.$refs.MediaFilters && this.queryParams.dependency) {
                this.$refs.MediaFilters.updateDependencyFilter(this.queryParams.dependency);
            }
            if (this.$refs.MediaFilters && this.queryParams.applicationSubject) {
                this.$refs.MediaFilters.updateApplicationSubject(this.queryParams.applicationSubject);
            }

            // 调用 getMediaList 方法获取数据
            this.handleQuery(this.queryParams);
        });
    },
    methods: {
        // 处理筛选查询
        handleQuery(params) {
            // 设置标志位，表示正在处理查询
            this.isUpdatingUrl = true;

            // 确保 queryParams 是一个新对象，避免引用问题
            this.queryParams = { ...(params || {}) };

            // 确保 channel、dependency、publicOpinion、serviceType 是数组
            if (this.queryParams.channel && !Array.isArray(this.queryParams.channel)) {
                if (typeof this.queryParams.channel === 'string') {
                    this.queryParams.channel = this.queryParams.channel.split(',');
                } else {
                    this.queryParams.channel = [];
                }
            }

            if (this.queryParams.dependency && !Array.isArray(this.queryParams.dependency)) {
                if (typeof this.queryParams.dependency === 'string') {
                    this.queryParams.dependency = this.queryParams.dependency.split(',');
                } else {
                    this.queryParams.dependency = [];
                }
            }

            if (this.queryParams.situation && !Array.isArray(this.queryParams.situation)) {
                if (typeof this.queryParams.situation === 'string') {
                    this.queryParams.situation = this.queryParams.situation.split(',');
                } else {
                    this.queryParams.situation = [];
                }
            }

            if (this.queryParams.mediaType && !Array.isArray(this.queryParams.mediaType)) {
                if (typeof this.queryParams.mediaType === 'string') {
                    this.queryParams.mediaType = this.queryParams.mediaType.split(',');
                } else {
                    this.queryParams.mediaType = [];
                }
            }

            console.log('处理后的查询参数:', this.queryParams);

            // 更新 channelParam 和 dependencyParam
            if (this.queryParams.channel && this.queryParams.channel.length > 0) {
                this.channelParam = Array.isArray(this.queryParams.channel)
                    ? this.queryParams.channel.join(',')
                    : this.queryParams.channel;
            } else {
                this.channelParam = '';
            }

            if (this.queryParams.dependency && this.queryParams.dependency.length > 0) {
                this.dependencyParam = Array.isArray(this.queryParams.dependency)
                    ? this.queryParams.dependency.join(',')
                    : this.queryParams.dependency;
            } else {
                this.dependencyParam = '';
            }

            // 重置页码
            this.pageNo = 1;

            // 构建请求参数
            const requestParams = {
                ...this.queryParams,
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                orderBy: this.orderBy,
                orderByType: this.orderByType
            };

            // 处理数组参数
            if (requestParams.channel && Array.isArray(requestParams.channel)) {
                requestParams.channel = requestParams.channel.join(',');
            }

            if (requestParams.dependency && Array.isArray(requestParams.dependency)) {
                requestParams.dependency = requestParams.dependency.join(',');
            }

            if (requestParams.situation && Array.isArray(requestParams.situation)) {
                requestParams.situation = requestParams.situation.join(',');
            }

            if (requestParams.mediaType && Array.isArray(requestParams.mediaType)) {
                requestParams.mediaType = requestParams.mediaType.join(',');
            }

            // 直接调用 MediaList 组件的 API 请求，避免触发多次请求
            if (this.$refs.MediaList) {
                this.$refs.MediaList.loading = true;
                this.$refs.MediaList.pageNo = 1;

                // 不再区分传统媒体和新媒体，整合为一个列表
                // 发送请求
                this.$http.get('/media/mediaList', { params: requestParams }).then(res => {
                    if (res.body.status === 0) {
                        const list = res.body.data.list || [];
                        // 为每个项目添加默认的舆论场值和服务形式，确保数据类型为字符串
                        this.$refs.MediaList.list = list.map(item => ({
                            ...item,
                            situation: item.situation ? String(item.situation) : null, // 确保为字符串类型
                            mediaType: item.mediaType // 保持原有的mediaType值
                        }));
                        this.$refs.MediaList.total = res.body.data.count || 0;
                    } else {
                        this.$Message.error(res.body.message || '获取数据失败');
                    }
                    this.$refs.MediaList.loading = false;

                    // 更新 URL 参数，但不触发数据加载
                    this.updateUrlParams();

                    // 重置标志位
                    setTimeout(() => {
                        this.isUpdatingUrl = false;
                    }, 100);
                }).catch(err => {
                    console.error('获取媒体清单列表失败', err);
                    this.$Message.error('获取数据失败');
                    this.$refs.MediaList.loading = false;

                    // 重置标志位
                    setTimeout(() => {
                        this.isUpdatingUrl = false;
                    }, 100);
                });
            } else {
                // 如果组件不存在，直接更新 URL 并重置标志位
                this.updateUrlParams();
                setTimeout(() => {
                    this.isUpdatingUrl = false;
                }, 100);
            }
        },

        // 更新 URL 参数
        updateUrlParams() {
            // 构建查询参数
            const query = { ...this.$route.query };

            // 更新 channel 参数
            if (this.queryParams.channel && this.queryParams.channel.length > 0) {
                // 如果是数组，转换为逗号分隔的字符串
                if (Array.isArray(this.queryParams.channel)) {
                    query.channel = this.queryParams.channel.join(',');
                } else {
                    query.channel = this.queryParams.channel;
                }

                // 更新 channelParam 属性
                this.channelParam = query.channel;

                // 删除旧的参数和可能存在的带方括号的参数
                delete query.channelName;
                delete query['channel[]'];
            } else {
                // 如果没有选择渠道，删除相关参数
                delete query.channel;
                delete query.channelName;
                delete query['channel[]'];
                this.channelParam = '';
            }

            // 更新 dependency 参数
            if (this.queryParams.dependency && this.queryParams.dependency.length > 0) {
                // 如果是数组，转换为逗号分隔的字符串
                if (Array.isArray(this.queryParams.dependency)) {
                    query.dependency = this.queryParams.dependency.join(',');
                } else {
                    query.dependency = this.queryParams.dependency;
                }

                // 更新 dependencyParam 属性
                this.dependencyParam = query.dependency;

                // 删除旧的参数和可能存在的带方括号的参数
                delete query.dependencyName;
                delete query['dependency[]'];
            } else {
                // 如果没有选择属地，删除相关参数
                delete query.dependency;
                delete query.dependencyName;
                delete query['dependency[]'];
                this.dependencyParam = '';
            }
            if (this.queryParams.applicationSubject) {
                query.applicationSubject = this.queryParams.applicationSubject;
            }

            // 构建新的 URL
            const urlParams = new URLSearchParams();
            for (const key in query) {
                if (query[key]) {
                    urlParams.set(key, query[key]);
                }
            }

            // 使用 History API 的 replaceState 方法更新 URL，不触发路由变化
            const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
            window.history.replaceState(null, '', newUrl);
        },

        // 获取媒体清单数据（已移至 MediaList 组件，此方法保留用于兼容）
        getMediaList() {
            // 构建请求参数
            const params = {
                ...this.queryParams,
                mediaType: 0,
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                orderBy: this.orderBy,
                orderByType: this.orderByType
            };

            // 根据筛选条件添加参数
            if (this.dataType) {
                params.dataType = this.dataType;
            }

            // 更新筛选器的渠道值和属地值
            this.$nextTick(() => {
                if (this.$refs.MediaFilters && this.queryParams.channel) {
                    this.$refs.MediaFilters.updateChannelFilter(this.queryParams.channel);
                }

                if (this.$refs.MediaFilters && this.queryParams.dependency) {
                    this.$refs.MediaFilters.updateDependencyFilter(this.queryParams.dependency);
                }
                if (this.$refs.MediaFilters && this.queryParams.applicationSubject) {
                    this.$refs.MediaFilters.updateApplicationSubject(this.queryParams.applicationSubject);
                }
            });

            // 调用 MediaList 组件的 getList 方法
            if (this.$refs.MediaList) {
                this.$refs.MediaList.queryParams = params;
                this.$refs.MediaList.getList();
            }
        },

        // 分页处理
        pageNoChange(pageNo) {
            this.pageNo = pageNo;
            this.getMediaList();
        },

        pageSizeChange(pageSize) {
            this.pageSize = pageSize;
            this.pageNo = 1;
            this.getMediaList();
        },

        // 清除筛选条件
        clearFilter() {
            // 设置标志位，表示正在处理查询
            this.isUpdatingUrl = true;

            this.dataType = '';
            this.channelParam = '';
            this.dependencyParam = '';
            this.queryParams = {};
            this.pageNo = 1;

            // 更新筛选器
            if (this.$refs.MediaFilters) {
                this.$refs.MediaFilters.updateChannelFilter([]);
                this.$refs.MediaFilters.updateDependencyFilter([]);
                this.$refs.MediaFilters.updateSituationFilter([]);
                this.$refs.MediaFilters.updateMediaTypeFilter([]);
                this.$refs.MediaFilters.updateApplicationSubject('');
            }

            // 构建请求参数
            const requestParams = {
                pageNo: this.pageNo,
                pageSize: this.pageSize,
                orderBy: this.orderBy,
                orderByType: this.orderByType
            };

            // 直接调用 MediaList 组件的 API 请求，避免触发多次请求
            if (this.$refs.MediaList) {
                this.$refs.MediaList.loading = true;
                this.$refs.MediaList.pageNo = 1;

                // 不再区分传统媒体和新媒体，整合为一个列表
                // 发送请求
                this.$http.get('/media/mediaList', { params: requestParams }).then(res => {
                    if (res.body.status === 0) {
                        const list = res.body.data.list || [];
                        // 为每个项目添加默认的舆论场值和服务形式，确保数据类型为字符串
                        this.$refs.MediaList.list = list.map(item => ({
                            ...item,
                            situation: item.situation ? String(item.situation) : null, // 确保为字符串类型
                            mediaType: item.mediaType // 保持原有的mediaType值
                        }));
                        this.$refs.MediaList.total = res.body.data.count || 0;
                    } else {
                        this.$Message.error(res.body.message || '获取数据失败');
                    }
                    this.$refs.MediaList.loading = false;

                    // 更新 URL 参数，但不触发数据加载
                    this.updateUrlParams();

                    // 重置标志位
                    setTimeout(() => {
                        this.isUpdatingUrl = false;
                    }, 100);
                }).catch(err => {
                    console.error('获取媒体清单列表失败', err);
                    this.$Message.error('获取数据失败');
                    this.$refs.MediaList.loading = false;

                    // 重置标志位
                    setTimeout(() => {
                        this.isUpdatingUrl = false;
                    }, 100);
                });
            } else {
                // 如果组件不存在，直接更新 URL 并重置标志位
                this.updateUrlParams();
                setTimeout(() => {
                    this.isUpdatingUrl = false;
                }, 100);
            }
        },

        // 获取数据类型标签
        getDataTypeLabel(type) {
            const typeMap = {
                'centreResult': '中央互联网新闻信息服务单位',
                'otherResult': '其他互联网新闻信息服务单位',
                'proResult': '各地网信办许可信息'
            };
            return typeMap[type] || '未知类型';
        },

        // 获取服务形式
        getServiceType(row) {
            // 根据mediaType字段判断服务形式
            if (row.mediaType === 0) {
                return '传统媒体';
            } else if (row.mediaType === 1) {
                return '新媒体';
            } else {
                return '传统媒体'; // 默认值
            }
        }
    },
    computed: {},
    watch: {
        // 我们不再监听路由参数变化，因为我们使用 History API 直接修改 URL
        // 如果需要处理外部 URL 变化（如用户手动修改 URL 或从书签打开），可以在 created 钩子中处理
    },
    filters: {},
    mounted() {
      this.getLog("全息档案库/媒体库", '浏览/【媒体库】传统媒体库清单页浏览');
    }
};
</script>

<style lang="less" scoped>
.list-view{
    height: 100%;
}
.media-list {


    height: 100%;
    display: flex;
    flex-direction: column;


}
</style>
