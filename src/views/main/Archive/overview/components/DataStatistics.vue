<template>
  <div class="data-statistics">
    <h4 class="component-title">数据量统计</h4>
    
    <div class="stats-grid">
      <!-- 账号总量 -->
      <div class="stat-card primary clickable" @click="goToSelfMediaLibrary">
        <div class="stat-content">
          <div class="stat-label">入库账号总量</div>
          <div class="stat-number">{{ formatNumber(accountTotal) }}</div>

        </div>
        <div class="stat-icon">
          <img src="../img/用户账户.svg" alt="">
        </div>
        <img class="line" src="../img/线.svg" alt="">
      </div>

      <!-- 精准画像总量 -->
      <div class="stat-card info clickable" @click="goToSelfMediaLibrary">
        <div class="stat-content">
          <div class="stat-label">精准画像总量</div>
          <div class="stat-number">{{ formatNumber(contactInfoTotal) }}</div>

        </div>
        <div class="stat-icon">
          <img src="../img/自媒体概览-白名单1.svg" alt="">
        </div>
        <img class="line" src="../img/线.svg" alt="">
      </div>

      <!-- 市办完善账号数量 -->
      <div class="stat-card success">
        <div class="stat-content">
          <div class="stat-label">市办完善账号数量</div>
          <div class="stat-number">{{ formatNumber(cityAccountCount) }} </div>
          
        </div>
        <div class="stat-icon">
          <img src="../img/白名单1备份 2.svg" alt="">
        </div>
        <img class="line" src="../img/线blue.svg" alt="">
      </div>

      <!-- 区县完善账号数量 -->
      <div class="stat-card warning">
        <div class="stat-content">
          <div class="stat-label">区县完善账号数量</div>
          <div class="stat-number">{{ formatNumber(districtAccountCount) }}</div>
          
        </div>
        <div class="stat-icon">
          <img src="../img/个人信息.svg" alt="">
        </div>
        <img class="line" src="../img/线blue.svg" alt="">
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>
  </div>
</template>

<script>
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'DataStatistics',
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      accountTotal: 0,
      contactInfoTotal: 0,  // 联系方式总量（有手机号的账号总量）
      cityAccountCount: 0,    // 新增账号（所有人工添加的数量，包括市办和区县）
      districtAccountCount: 0 // 完善账号信息（所有完善过的账号数量，包括市办和区县）
    }
  },
  watch: {
    timeRange: {
      handler() {
        console.log("时间范围修改了")
        // 只获取时间相关的数据，不影响总量数据
        this.fetchTimeBasedData()
      },
      deep: true
    }
  },
  mounted() {
    // 组件挂载时获取所有数据
    this.fetchData()
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        // 并行调用两个接口
        await Promise.all([
          this.fetchTotalData(),      // 获取总量数据（不受时间影响）
          this.fetchTimeBasedData()   // 获取时间相关数据
        ])
      } catch (error) {
        console.error('获取数据统计失败:', error)
        this.$Message.error('获取数据统计失败')
      } finally {
        this.loading = false
      }
    },

    // 获取总量数据（账号总量和精准画像总量，不受时间条件影响）
    async fetchTotalData() {
      try {
        // 不传时间参数，获取全部数据
        const response = await this.$http.get('/holoArchiveChart/getMediaDataCount')

        if (response.body.status === 0) {
          const data = response.body.data
          // 只更新总量相关字段
          this.accountTotal = data.totalAccounts || 0
          this.contactInfoTotal = data.totalUserInfo || 0
        } else {
          console.error('获取总量数据失败:', response.body.message)
        }
      } catch (error) {
        console.error('获取总量数据失败:', error)
        throw error
      }
    },

    // 获取时间相关数据（新增账号数量和修改账号数量）
    async fetchTimeBasedData() {
      try {
        const params = buildTimeParams(this.timeRange)
        const response = await this.$http.get('/holoArchiveChart/getMediaDataCount', { params })

        if (response.body.status === 0) {
          const data = response.body.data
          // 只更新时间相关字段
          this.cityAccountCount = data.cityAccountCount || 0
          this.districtAccountCount = data.districtAccountCount || 0
        } else {
          console.error('获取时间相关数据失败:', response.body.message)
        }
      } catch (error) {
        console.error('获取时间相关数据失败:', error)
        throw error
      }
    },



    // 格式化数字显示
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    },

    // 跳转到自媒体库页面
    goToSelfMediaLibrary() {
      const routeData = this.$router.resolve({
        path: '/main/Archive/selfMediaLibrary'
      })
      window.open(routeData.href, '_blank')
    }
  },
  mounted() {
    this.fetchData() // 启用接口调用
  }
}
</script>

<style lang="less" scoped>
.data-statistics {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    width:222px;
    height: 36px;
    line-height: 36px;
    position: relative;
    margin:-15px auto 24px auto;
    text-align: center;
    background-image: url("../img/矩形.svg");
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    display: flex;
    justify-content: space-between;
    padding: 16px 12px;
    border-radius: 4px;
    color: white;
    position: relative;
    overflow: hidden;
    height: 120px;

    &.clickable {
      cursor: pointer;
      transition: transform 0.2s, box-shadow 0.2s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    &.primary {
      background: linear-gradient( 135deg, #7177FF 0%, #5A73FF 100%);
    }

    &.info {
      background: linear-gradient( 132deg, #3DA1FF 0%, #3084FF 100%);
    }

    &.success {
      background: #D8E3FF;
       .stat-label {
        color:#333;
       }
       .stat-number {
        color:#5482EA;
       }
    }

    &.warning {
      background: #D8E3FF;
      .stat-label {
        color:#333;
       }
       .stat-number {
        color:#5482EA;
       }
    }

    .stat-content {
      flex: 1;

      .stat-number {
        font-weight: 500;
        font-size: 28px;
        margin-bottom: 5px;
        line-height: 1.2;
      }

      .stat-label {
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        opacity: 0.9;
      }
    }

    .stat-icon {
      font-size: 0;
      margin-left: 10px;
      position: absolute;
      bottom:0;
      right: 0;
    }
    .line{
       position: absolute;
       width: 55px;

      bottom:17px;
      left: 13px;
    }
    // &::before {
    //   content: '';
    //   position: absolute;
    //   top: 0;
    //   right: 0;
    //   width: 100px;
    //   height: 100px;
    //   background: rgba(255, 255, 255, 0.1);
    //   border-radius: 50%;
    //   transform: translate(30px, -30px);
    // }
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-statistics .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
