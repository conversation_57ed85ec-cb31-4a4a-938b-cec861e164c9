<template>
  <div class="personal-info-ranking">
    <h4 class="component-title">区县完善账号情况 haha 不知道接口要换么</h4>

    <div class="chart-container">
      <div v-show="!hasData && !loading" class="empty-container">
        <EmptyState text="暂无数据" description="当前时间范围内没有个人信息完善数据" height="300px" />
      </div>
      <div v-show="hasData && !loading" ref="barChart" class="bar-chart"></div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import EmptyState from './EmptyState.vue'
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'PersonalInfoRanking',
  components: {
    EmptyState
  },
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      chart: null,
      chartData: []
    }
  },
  computed: {
    hasData() {
      return this.chartData && this.chartData.length > 0
    }
  },
  watch: {
    timeRange: {
      handler() {
        this.fetchData() // 暂时注释掉接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        const params = buildTimeParams(this.timeRange)
        console.log("aaaa",this.timeRange)
        // 调用接口获取个人信息完善排行
        const response = await this.$http.get('/holoArchiveChart/getProfileRank', { params })

        if (response.body.status === 0) {
          this.chartData = this.processApiData(response.body.data) || []
        } else {
          this.chartData = []
        }
      } catch (error) {
        // console.error('获取个人信息完善排行失败:', error)
        // // 使用模拟数据
        // this.chartData = this.getMockData()
        //显示空数据
        this.chartData = []
      } finally {
        this.loading = false
        // 确保在数据加载完成后更新图表
        this.$nextTick(() => {
          this.updateChart()
        })
      }
    },

    // 处理接口返回的数据
    processApiData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => ({
        name: (item.groupBy || '未知区域').replace('网信办', ''),
        value: item.statNum || 0
      }))
    },

    // 获取模拟数据 - 16个机构录入手机号信息的数量排行
    getMockData() {
      return [
        { name: '市委网信办', value: 230 },
        { name: '历下区', value: 210 },
        { name: '市中区', value: 190 },
        { name: '槐荫区', value: 180 },
        { name: '天桥区', value: 170 },
        { name: '历城区', value: 160 },
        { name: '长清区', value: 140 },
        { name: '章丘区', value: 135 },
        { name: '济阳区', value: 130 },
        { name: '莱芜区', value: 115 },
        { name: '钢城区', value: 110 },
        { name: '平阴县', value: 95 },
        { name: '商河县', value: 85 },
        { name: '高新区', value: 65 },
        { name: '南部山区', value: 55 },
        { name: '起步区', value: 50 }
      ]
    },



    // 初始化图表
    initChart() {
      if (!this.hasData) return
      if (!this.$refs.barChart) return

      this.chart = echarts.init(this.$refs.barChart)
      this.updateChart()
    },

    // 更新图表
    updateChart() {
      if (!this.hasData) {
        if (this.chart) {
          this.chart.clear()
        }
        return
      }

      if (!this.chart && this.$refs.barChart) {
        this.chart = echarts.init(this.$refs.barChart)
      }

      if (!this.chart) return

      const categories = this.chartData.map(item => item.name)
      const values = this.chartData.map(item => item.value)
      const maxValue = Math.max(...values)

      const option = {
        grid: {
          top: 20,
          right: 30,
          bottom: 60,
          left: 60
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 0,
            fontSize: 10,
            color: '#666',
            interval: 0,
            overflow: 'truncate',
            width: 40
          },
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          }
        },
        yAxis: {
          type: 'value',
          max: Math.ceil(maxValue * 1.1),
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed'
            }
          }
        },
        series: [{
          type: 'bar',
          data: values.map((value) => ({
            value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#4E57D2' },
                { offset: 1, color: '#5585EC' }
              ])
            }
          })),
          barWidth: '50%',
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
            color: '#333'
          }
        }],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            return `${data.name}<br/>录入手机号数量: ${data.value}`
          }
        }
      }

      this.chart.setOption(option)
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData()
    })

    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="less" scoped>
.personal-info-ranking {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    width:222px;
    height: 36px;
    line-height: 36px;
    position: relative;
    margin:-15px auto 0px auto;
    text-align: center;
    background-image: url("../img/矩形.svg");
  }

  .chart-container {
    height: 390px;

    .bar-chart {
      width: 100%;
      height: 100%;
    }
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }
}
</style>
