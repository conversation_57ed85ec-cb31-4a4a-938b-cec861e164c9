<template>
  <div class="media-library-stats">

    <!-- 顶部统计卡片 -->
    <div class="top-stats-cards">
      <div class="stat-card total">
        <div class="card-icon">
          <svg-icon icon-class="总计" style="font-size: 40px; color: #4A90E2;" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ formatNumber(totalMediaCount) }}</div>
          <div class="card-label">媒体主体总计</div>
        </div>
      </div>

      <div class="stat-card traditional">
        <div class="card-icon">
          <svg-icon icon-class="传统媒体" style="font-size: 40px; color: #50C8C8;" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ formatNumber(traditionalMediaCount) }}</div>
          <div class="card-label">传统媒体</div>
        </div>
      </div>

      <div class="stat-card new-media">
        <div class="card-icon">
          <svg-icon icon-class="新媒体" style="font-size: 40px; color: #9B59B6;" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ formatNumber(newMediaCount) }}</div>
          <div class="card-label">新媒体</div>
        </div>
      </div>

      <!-- <div class="stat-card business">
        <div class="card-icon">
          <svg-icon icon-class="商业平台" style="font-size: 40px; color: #2ECC71;" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ formatNumber(businessPlatformCount) }}</div>
          <div class="card-label">商业平台</div>
        </div>
      </div> -->
    </div>

    <!-- 服务许可项统计 -->
    <div class="service-stats-section">
      <div class="service-stats-left">
        
        <div class="service-content">
          <div class="service-icon">
          <svg-icon icon-class="服务许可项" style="font-size: 40px; color: #4A90E2;" />
          </div>
          <div class="service-number">{{ formatNumber(totalServiceCount) }}</div>
          <div class="service-label">媒体信源</div>
          <div class="service-details">
            <div class="detail-item">
              <span class="detail-label">已备案</span>
              <span class="detail-value">{{ formatNumber(registeredCount) }}</span>个
            </div>
            <div class="detail-item">
              <span class="detail-label">未备案</span>
              <span class="detail-value">{{ formatNumber(unregisteredCount) }}</span>个
            </div>
            
          </div>
          <div class="service-details">
            <div class="detail-item">
              <span class="detail-label">传统媒体数量</span>
              <span class="detail-value">{{ formatNumber(num_traditionalMedia) }}</span>个
            </div>
            <div class="detail-item">
              <span class="detail-label">新媒体数量</span>
              <span class="detail-value">{{ formatNumber(num_newMedia) }}</span>个
            </div>
            <!-- <div class="detail-item">
              <span class="detail-label">商业平台数量</span>
              <span class="detail-value">{{ formatNumber(num_businessPlatform) }}</span>个
            </div> -->
          </div>

        </div>
      </div>

      <div class="service-chart-container">
        <!-- 左侧图例 -->
        <div class="chart-legend left-legend">
          <div
            v-for="(item, index) in leftLegendData"
            :key="index"
            class="legend-item"
          >
            <div class="legend-dot" :style="{ backgroundColor: item.color }"></div>
            <div class="legend-content">
              <div class="legend-name">{{ item.name }}</div>
              <div class="legend-count">{{ item.value }} | {{ item.percentage }}%</div>
            </div>
          </div>
        </div>

        <!-- 中间图表 -->
        <div ref="serviceChart" class="service-chart"></div>

        <!-- 右侧图例 -->
        <div class="chart-legend right-legend">
          <div
            v-for="(item, index) in rightLegendData"
            :key="index"
            class="legend-item"
          >
            <div class="legend-dot" :style="{ backgroundColor: item.color }"></div>
            <div class="legend-content">
              <div class="legend-name">{{ item.name }}</div>
              <div class="legend-count">{{ item.value }} | {{ item.percentage }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { buildTimeParams } from '../utils/timeParamsBuilder.js'

export default {
  name: 'MediaLibraryStats',
  props: {
    timeRange: {
      type: Object,
      default: () => ({
        type: 'all',
        startDate: null,
        endDate: null,
        label: '全部'
      })
    }
  },
  data() {
    return {
      loading: false,
      serviceChart: null,
      // 媒体库统计数据
      traditionalMediaCount: 0,
      newMediaCount: 0,
      businessPlatformCount: 0,
      totalMediaCount:0,
      // 服务许可项数据
      totalServiceCount: 0,
      registeredCount: 0,
      unregisteredCount: 0,
      num_traditionalMedia:0,//传统媒体数量
      num_newMedia:0,//新媒体数量
      num_businessPlatform:0,//商业平台数量
      serviceChartData: []
    }
  },
  computed: {
    // totalMediaCount() {
    //   return this.traditionalMediaCount + this.newMediaCount + this.businessPlatformCount
    // },
    // 左侧图例数据（前4个）
    leftLegendData() {
      return this.serviceChartData.slice(0, 4)
    },
    // 右侧图例数据（后4个）
    rightLegendData() {
      return this.serviceChartData.slice(4, 8)
    }
  },
  watch: {
    timeRange: {
      handler() {
        this.fetchData() // 启用接口调用
      },
      deep: true
    }
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        // 构建时间参数
        const params = buildTimeParams(this.timeRange)

        // 并行调用两个接口
        const [mediaCountResponse, serviceSumResponse] = await Promise.all([
          this.$http.get('/holoArchiveChart/getMediaLibraryCount', { params }),
          this.$http.get('/holoArchiveChart/getMediaSumCount', { params })
        ])

        // 处理媒体库数量统计接口返回
        if (mediaCountResponse.body.status === 0) {
          const mediaData = mediaCountResponse.body.data
          this.traditionalMediaCount = mediaData.traditional_media_count || 0
          this.newMediaCount = mediaData.new_media_count || 0
          this.totalMediaCount = mediaData.total_media_units || 0
          this.businessPlatformCount = mediaData.commercial_platform_count || 0
        } else {
          console.error('媒体库数量统计接口错误:', mediaCountResponse.body.message)
          this.$Message.error(mediaCountResponse.body.message || '获取媒体库数量统计失败')
        }

        // 处理服务许可项统计接口返回
        if (serviceSumResponse.body.status === 0) {
          const serviceData = serviceSumResponse.body.data
          this.totalServiceCount = serviceData.count || 0
          this.registeredCount = serviceData.register || 0
          this.unregisteredCount = serviceData.unRegistered || 0
          this.num_traditionalMedia=serviceData.traditional_media_count ||0
          this.num_newMedia=serviceData.new_media_count ||0
          this.num_businessPlatform=serviceData.commercial_platform_count ||0

          // 更新服务许可项图表数据
          this.updateServiceChartData(serviceData.mediaSum || [])
        } else {
          console.error('服务许可项统计接口错误:', serviceSumResponse.body.message)
          this.$Message.error(serviceSumResponse.body.message || '获取服务许可项统计失败')
        }

        // 更新图表
        this.updateServiceChart()

      } catch (error) {
        console.error('获取媒体库统计失败:', error)
        this.$Message.error('获取媒体库统计失败')
        // 使用默认数据
        this.updateServiceChartData([])
        this.updateServiceChart()
      } finally {
        this.loading = false
      }
    },

    // 更新服务许可项图表数据
    updateServiceChartData(mediaSumData = []) {
      // 定义颜色映射
      const colorMap = {
        '公众账号': '#4A90E2',
        '网络直播': '#2ECC71',
        '微博客': '#E74C3C',
        '其他': '#1ABC9C',
        '互联网站': '#87CEEB',
        '应用程序': '#FF69B4',
        '论坛': '#FFA500',
        '博客': '#20B2AA'
      }

      if (mediaSumData.length > 0) {
        // 使用接口返回的真实数据
        this.serviceChartData = mediaSumData.map(item => ({
          name: item.channel,
          value: item.count,
          percentage: item.percent,
          color: colorMap[item.channel] || '#999999'
        }))
      } else {
        // 使用默认数据
        this.serviceChartData = [
          {
            name: '公众账号',
            value: 0,
            percentage: 0,
            color: '#4A90E2'
          },
          {
            name: '网络直播',
            value: 0,
            percentage: 0,
            color: '#2ECC71'
          },
          {
            name: '微博客',
            value: 0,
            percentage: 0,
            color: '#E74C3C'
          },
          {
            name: '其他',
            value: 0,
            percentage: 0,
            color: '#1ABC9C'
          },
          {
            name: '互联网站',
            value: 0,
            percentage: 0,
            color: '#87CEEB'
          },
          {
            name: '应用程序',
            value: 0,
            percentage: 0,
            color: '#FF69B4'
          },
          {
            name: '论坛',
            value: 0,
            percentage: 0,
            color: '#FFA500'
          },
          {
            name: '博客',
            value: 0,
            percentage: 0,
            color: '#20B2AA'
          }
        ]
      }
    },



    // 格式化数字
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    },

    // 初始化服务许可项图表
    initServiceChart() {
      this.serviceChart = echarts.init(this.$refs.serviceChart)
      this.updateServiceChartData()
      this.updateServiceChart()
    },

    // 更新服务许可项图表
    updateServiceChart() {
      if (!this.serviceChart) return

      const option = {
        series: [{
          type: 'pie',
          radius: ['50%', '80%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          data: this.serviceChartData.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }],
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>数量: ${params.value}<br/>占比: ${params.percent}%`
          }
        }
      }

      this.serviceChart.setOption(option)
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.serviceChart) {
        this.serviceChart.resize()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initServiceChart()
      this.fetchData() // 启用接口调用
    })

    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.serviceChart) {
      this.serviceChart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="less" scoped>
.media-library-stats {
  position: relative;

  .component-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-left: 8px;
    border-left: 4px solid #4A90E2;
  }

  // 顶部统计卡片
  .top-stats-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    background: #F4F5FB;
    border-radius: 4px;
    overflow: hidden;
    .stat-card {
      flex: 1;
      border-radius: 8px;
      padding: 16px 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;
      &::before{
        content:"";
        width: 1px;
        height: 55px;
        background:#DBDBDB ;
        position: absolute;
        top:16px;
        left:-1px;
      }
      &:nth-child(1){
        min-width: 243px;
      }
      .card-icon {
        flex-shrink: 0;
      }

      .card-content {
        flex: 1;

        .card-number {
          font-size: 24px;
          font-weight: bold;
          color:#5482EA;
        }

        .card-label {
          font-size: 14px;
          color: #333;
          line-height: 1.4;
          font-weight: 400;
        }
      }

      // &.total .card-number {
      //   color: #4A90E2;
      // }

      // &.traditional .card-number {
      //   color: #50C8C8;
      // }

      // &.new-media .card-number {
      //   color: #9B59B6;
      // }

      // &.business .card-number {
      //   color: #2ECC71;
      // }
    }
  }

  // 服务许可项统计区域
  .service-stats-section {
    background: #fff;
    border-radius: 8px;
     height: 310px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 50px;

    .service-stats-left {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      width: 192px;
      background:#F5FEFF;
      flex-direction: column;
      .service-icon {
        flex-shrink: 0;
        margin-bottom:15px;
        position: relative;
        
      }

      .service-content {
        position: relative;
        box-sizing: border-box;
       
        &::before{
          content:"";
          width: 3px;
          height: 50px;
          background: #5482EA;
          display: block;
          position: absolute;
          left:0;
          top:60px;
          border-bottom:22px solid #C3DAF8;
        }
        .service-number {
          line-height: 1;
          font-size: 32px;
          font-weight: bold;
          color: #4A90E2;
          // margin-bottom: 5px;
           padding-left:15px;
        }

        .service-label {
          font-size: 16px;
          color: #333;
          margin-bottom: 10px;
           padding-left:15px;
        }

        .service-details {
          border-top: 1px solid #D3D3D3;
          margin-top:10px;
          padding-top:10px;
          .detail-item {
            display: flex;
            justify-content:baseline;
            // margin-bottom: 8px;
            min-width: 120px;

            .detail-label {
              font-size: 14px;
              color: #000;
            }

            .detail-value {
              font-size: 14px;
              font-weight: 500;
              color: #5482EA;
            }
          }
        }
      }
    }

    .service-chart-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;

      .service-chart {
        width: 300px;
        height: 300px;
        flex-shrink: 0;
      }

      .chart-legend {
        display: flex;
        flex-direction: column;
        gap: 15px;
        min-width: 120px;
        

        &.left-legend {
          padding-left:20px;
          align-items: flex-start;
        }

        &.right-legend {
          align-items: flex-start;
        }

        .legend-item {
          display: flex;
          align-items: flex-start;
          gap: 8px;

          .legend-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            flex-shrink: 0;
            margin-top: 2px;
          }

          .legend-content {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .legend-name {
              font-size: 14px;
              color: #333;
              font-weight: 500;
              line-height: 1.2;
            }

            .legend-count {
              font-size: 12px;
              color: #666;
              line-height: 1.2;
            }
          }
        }
      }
    }
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .media-library-stats {
    .service-stats-section {
      flex-direction: column;

      .service-chart-container {
        justify-content: center;

        .chart-legend {
          grid-template-columns: 1fr 1fr 1fr 1fr;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .media-library-stats {
    .top-stats-cards {
      flex-direction: column;
    }

    .service-stats-section {
      .service-stats-left {
        display: flex;
        flex-direction: column;
        text-align: center;
        align-items: center;
      }

      .service-chart-container {
        .chart-legend {
          grid-template-columns: 1fr 1fr;
        }
      }
    }
  }
}
</style>
