<template>
  <div class="relates-information">
    <!-- <div class="relates-information-item">
        <div class="relates-information-item-title">
            <span>
                <svg-icon class="icon" icon-class="Word-彩色" />
                备注
            </span>
            <span>
                <svg-icon class="icon opera" icon-class="shengcheng-s" @click.native="openRemarkModal" />
            </span>
        </div>
        <div class="list_box">
            <no-data v-show="remarks.length === 0" />
            <div class="list" v-if="remarks.length > 0">
                <div class="remark-item" v-for="(item, index) in remarks" :key="index">
                    <div class="remark-content">
                        {{ item.remark }}
                    </div>
                    <div class="remark-remark">
                        <div class="user-info">
                            <div class="user-name">
                                {{ item.opOrganName }}
                            </div>
                            <div class="user-time">
                                {{ item.opName }}
                            </div>
                           
                        </div>
                        <div class="user-time">
                            {{ item.createTime | formatTime }}
                        </div>
                    </div>
                </div>
                <div class="load-more" v-if="total > remarks.length" @click="loadMore">
                    加载更多
                </div>
            </div>
        </div>
    </div> -->
    <div class="relates-information-item">
        <div class="relates-information-item-title">
            <span>
                <!-- <svg-icon class="icon" icon-class="账号详情-关联" /> -->
                <!-- 关联账号 -->
            </span>
            <span>
                <svg-icon class="icon opera" icon-class="shengcheng-s" @click.native="openAccountModal" />
            </span>
        </div>
        <div class="list_box">
            <Spin v-show="relatedLoading" fix>
                <Icon
                type="ios-loading"
                    size="18"
                    class="demo-spin-icon-load"
                    ></Icon>
                <div>Loading</div>
            </Spin>
            <no-data v-show="relatedAccounts.length === 0 && !relatedLoading" />
            <div class="related-list" v-if="relatedAccounts.length > 0 && !relatedLoading">
                <div class="related-item" v-for="(item, index) in relatedAccounts" :key="index" @click="openAccountDetail(item)">
                    <div class="icon_logo"> 
                    <platformIcon :size="40" v-if="item.situation" :id="item.situation" />
                  </div>
                  <div class="related-item-info par">
                    账号ID：{{ item.accountId && item.accountId.includes('addJcHand') ? '' : item.accountId }}
                  </div>
                  <div class="related-item-info par">
                    账号昵称：{{ item.userName }}
                  </div>
                  <div class="related-item-info">
                    主页地址：<span class="blue">{{ item.accountUrl }}</span>
                  </div>
                </div>
            </div>
        </div>
    </div>
    <Modal
        v-model="remarkModal"
        width="720"
        class="remark-modal">
        <template #header>
            <div class="remark-header">
                <span>为“</span>
                <platformIcon
                :size="20"
                v-if="infoData.situation"
                :id="infoData.situation"
                ></platformIcon>
                <span class="account-name">{{ infoData.accountName }}</span>
                <span>”添加备注：</span>
            </div>
        </template>
        <div class="modal-content">
            <textarea 
                v-model="remarkContent" 
                placeholder="请在此添加备注" 
                :class="{'error-input': showError}"
            ></textarea>
            <div class="error-tip" v-show="showError">备注内容不能为空</div>
        </div>
        <div slot="footer" class="modal-footer">
            <!-- <Button @click="cancel">取消</Button> -->
            <Button type="primary" size="large" @click="ok">保存备注</Button>
        </div>
    </Modal>
    <Modal
        v-model="accountModal"
        width="720"
        class="account-modal remark-modal">
        <template #header>
            <div class="remark-header">
                <span>为“</span>
                <platformIcon
                    :size="20"
                    v-if="infoData.situation"
                    :id="infoData.situation"
                ></platformIcon>
                <span class="account-name">{{ infoData.accountName }}</span>
                <span>”添加关联账号：</span>
            </div>
        </template>
        <div class="modal-content">
            <div class="form-item">
                <div class="label">
                    <span class="required">*</span>平台
                </div>
                <div class="control">
                    <Select v-model="accountForm.platform" :class="{'error-input': platformError}" placeholder="请选择">
                        <Option v-for="item in platformList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                    <div class="error-tip" v-show="platformError">请选择平台</div>
                </div>
            </div>
            <div class="form-item">
                <div class="label">
                    <span class="required">*</span>账号昵称
                </div>
                <div class="control">
                    <Input v-model="accountForm.nickname" :class="{'error-input': nicknameError}" placeholder="请输入账号昵称" />
                    <div class="error-tip" v-show="nicknameError">请输入账号昵称</div>
                </div>
            </div>
            <div class="form-item">
                <div class="label">账号ID</div>
                <div class="control">
                    <Input v-model="accountForm.accountId" placeholder="请输入账号ID" />
                </div>
            </div>
            <div class="form-item">
                <div class="label">主页地址</div>
                <div class="control">
                    <Input v-model="accountForm.url" placeholder="请输入主页地址" />
                </div>
            </div>
        </div>
        <div slot="footer" class="modal-footer">
            <Button type="primary" size="large" @click="saveAccount">保存</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
import platformIcon from '@/components/platformIcon'
import situations from "@/assets/json/platform.json";
import moment from 'moment';
export default {
    name: 'RelatesInformation',
    components: {
        platformIcon,
    },
    props: {
        infoData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            remarkModal: false,
            remarkContent: '',
            remarks: [],
            showError: false,
            loading: false,
            total: 0,
            pageNo: 1,
            pageSize: 20,
            relatedAccounts: [],
            relatedLoading: false,
            accountModal: false,
            accountForm: {
                platform: '',
                nickname: '',
                accountId: '',
                url: ''
            },
            platformError: false,
            nicknameError: false,
            platformList: Object.keys(situations).filter(key => key !== '0').map(key => ({
                value: key,
                label: situations[key]
            }))
        }
    },
    methods: {
        openAccountDetail(item){
            //新开页跳转
            window.open(`/main/archiveDetail?ukey=${item.accountId}&situation=${item.situation}`, '_blank');
           
        },
        openRemarkModal() {
            this.remarkModal = true;
            console.log(11)
            this.remarkContent = '';
            this.showError = false;
        },
        ok() {
            if (!this.remarkContent.trim()) {
                this.showError = true;
                return;
            }
            
            this.showError = false;
            
            // 添加新备注
            this.$http.post('/medium/related/addRemark', {
                userId: this.infoData.id,
                remark: this.remarkContent,
                type:1//媒体库传 2 自媒体传 1
            }, {
                emulateJSON: true
            }).then(res => {
                if (res.body.status === 0) {
                    this.$Message.success('备注添加成功');
                    this.pageNo = 1;
                    this.remarks = [];
                    this.getRemarks();
                }else{
                    this.$Message.error(res.body.message);
                }
                this.remarkModal = false;
            })
            
            // 在实际应用中，这里应该调用API保存备注

        },
        cancel() {
            this.remarkModal = false;
        },
        // 获取备注列表
        getRemarks() {
            this.loading = true;
            // 实际应用中，这里应该调用API获取备注列表
            this.$http.get('/medium/related/remarkList/' + this.infoData.id, {
                params: {
                   pageNo: this.pageNo,
                   pageSize: this.pageSize
                }
            }).then(res => {
                if (res.body.status === 0) {
                    this.remarks = this.remarks.concat(res.body.data.list || []);
                    this.total = res.body.data.total || 0;
                }
                this.loading = false;
            });
        },
        loadMore() {
            this.pageNo++;
            this.getRemarks();
        },
        getRelatedAccounts() {
            this.relatedLoading = true;
            this.$http.get('/medium/related/accountList/' + this.infoData.id+"?type=1", {
                
            }).then(res => {
                this.relatedLoading = false;
                if (res.body.status === 0) {
                    this.relatedAccounts = res.body.data || [];
                }
               
            }); 
        },
        openAccountModal() {
            this.accountModal = true;
            this.resetAccountForm();
        },
        resetAccountForm() {
            this.accountForm = {
                platform: '',
                nickname: '',
                accountId: '',
                url: ''
            };
            this.platformError = false;
            this.nicknameError = false;
        },
        async saveAccount() {
            let valid = true;
            
            if (!this.accountForm.platform) {
                this.platformError = true;
                valid = false;
            }
            
            if (!this.accountForm.nickname.trim()) {
                this.nicknameError = true;
                valid = false;
            }
            
            if (!valid) {
                return;
            }
            if(this.accountForm.accountId){
                const res = await this.$http.get('/medium/related/checkAccount', {
                    params: {
                        accountId: this.accountForm.accountId,
                        situation: this.accountForm.platform
                    }
                })
                if(res.body.status === 0){
                    const listArr = res.body.data && res.body.data.length > 0 ? res.body.data : [];
                    if(listArr.length > 0){
                        this.$Modal.confirm({
                            title: '提示',
                            content: '该账号已关联“' + listArr.map(item => item.userName + '（' + item.userId + '）').join('、') +"”等账号",
                            onOk: () => {
                                this.confirmSaveAccount();
                            }
                        })
                        return;
                    }else{
                        this.confirmSaveAccount();
                    }
                }else{
                    this.confirmSaveAccount();
                }
               
            }else {
                this.confirmSaveAccount();
            }

        },
        confirmSaveAccount(){
            this.$http.post('/medium/related/addAccount', {
                userId: this.infoData.id,
                situation: this.accountForm.platform,
                userName: this.accountForm.nickname,
                accountId: this.accountForm.accountId,
                accountUrl: this.accountForm.url,
                 type:1//媒体库传 2 自媒体传 1
            }, {
                emulateJSON: true
            }).then(res => {
                if (res.body.status === 0) {
                    this.$Message.success('关联账号添加成功');
                    this.getRelatedAccounts(); // 刷新关联账号列表
                } else {
                    this.$Message.error(res.body.message || '添加失败');
                }
                this.accountModal = false;
            });
        }
    },
    mounted() {
        this.getRemarks();
        this.getRelatedAccounts();
        this.getLog("全息档案库/自媒体库", '查看账号详情/【'+this.$route.query.uname+'】关联账号页浏览');
    },
    watch: {
        remarkContent(val) {
            if (val.trim() && this.showError) {
                this.showError = false;
            }
        },
        'accountForm.platform'(val) {
            if (val && this.platformError) {
                this.platformError = false;
            }
        },
        'accountForm.nickname'(val) {
            if (val.trim() && this.nicknameError) {
                this.nicknameError = false;
            }
        }
    },
    filters: {
        formatTime(time) {
            return moment(Number(time)).format('YYYY-MM-DD HH:mm');
        }
    }
}
</script>

<style lang="less" scoped>
.relates-information{
    height: ~'calc(100vh - 350px)';
    overflow: auto;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    .relates-information-item{
        padding: 20px;
        flex: 1;
        overflow: hidden;
        height: 100%;
        background-color: #fff;
        border-radius: 4px;
        .relates-information-item-title{
            font-size: 16px;
            line-height: 1.5;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 1px solid #e6dfdf;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 5px;
            span{
                display: flex;
                align-items: center;
            }
            .icon{
                width: 20px;
                height: 20px;
                margin-right: 5px;
            }
            .opera{
                cursor: pointer;
            }
        }
        .list_box {
            height: ~'calc(100% - 40px)';
            overflow-y: auto;
            position: relative;
            .list{
                .remark-item {
                    word-break: break-all;
                    padding: 10px;
                    border-bottom: 1px dashed #a2a1a1;
                    .remark-content{
                        font-size: 14px;
                        line-height: 1.5;
                    }
                    // #a2a1a1
                    .remark-remark{
                        margin-top: 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        font-size: 12px;
                        line-height: 1.5;
                        color: #a2a1a1;
                        .user-info{
                            display: flex;
                            align-items: center;
                            gap: 20px;    
                        }
                    }
                }
                .load-more{
                    margin-top: 20px;
                    font-size: 14px;
                    text-align: center;
                    color: #5585ec;
                    cursor: pointer;
                }
            }
            .related-list{
                display: flex;
                flex-wrap: wrap;
                justify-content: start;
                gap: 20px;
                .related-item{
                    padding: 15px;
                    width: 23%;
                    background-color: #f2f2f2;
                    border-radius: 8px;
                    box-sizing: border-box;
                    overflow: hidden;
                    position: relative;
                    cursor: pointer;
                    .icon_logo{
                        position: absolute;
                        right: 20px;
                        top: 20px;
                    }
                    .related-item-info{
                        font-size: 14px;
                        line-height: 1.5;
                        &:not(:last-child){
                            margin-bottom: 10px;
                        }
                        &.par{
                            padding-right: 50px;
                        }
                        .blue{
                            color: #5585ec;
                            // cursor: pointer;
                        }
                    }
                }
            }
           
        }
    }
}

.remark-modal {
    /deep/ .ivu-modal-header{
        background: #fff;
        padding: 0;
        border-bottom: none;
    }
    /deep/ .ivu-modal-mask{
        background: rgba(55, 55, 55, 0.6);
    }
    .remark-header{
        padding: 30px 20px 0;
        // display: flex;
        // align-items: center;
        // flex-wrap: wrap;
        font-size: 16px;
        line-height: 20px;
        .account-name{
            color: #5585ec;
        }
    }
    
    .modal-content {
        padding: 0 10px 10px 10px;
        position: relative;
        
        textarea {
            width: 100%;
            height: 200px;
            border: 1px solid #dcdee2;
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
            
            &:focus {
                border-color: #5585ec;
            }
            
            &.error-input {
                border-color: #ed4014;
                &:focus {
                    border-color: #ed4014;
                    box-shadow: 0 0 0 2px rgba(237, 64, 20, 0.2);
                }
            }
        }
        
        .error-tip {
            position: absolute;
            color: #ed4014;
            font-size: 12px;
            margin-top: 5px;
        }
    }
    
    .ivu-modal-footer {
        border-top: none;
        padding-top: 0;
        .modal-footer{
            display: flex;
            justify-content: flex-end;
        }
    }
}

.related-item {
    padding: 10px;
    border-bottom: 1px dashed #eee;
    
    .related-info {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 5px;
        
        .account-name {
            font-weight: bold;
        }
    }
    
    .related-extra {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #999;
    }
}

.account-modal {
    /deep/ .ivu-modal-header {
        background: #fff;
        padding: 0;
        border-bottom: none;
    }
    
    /deep/ .ivu-modal-mask {
        background: rgba(55, 55, 55, 0.6);
    }
    
    .modal-header {
        padding: 30px 20px 0;
        font-size: 16px;
        line-height: 20px;
        
        .account-name {
            color: #5585ec;
        }
    }
    
    .modal-content {
        padding: 0 20px 10px 20px;
        
        .form-item {
            margin-bottom: 25px;
            display: flex;
            
            .label {
                width: 80px;
                line-height: 32px;
                flex-shrink: 0;
                padding-right: 20px;
                text-align: right;                
                .required {
                    color: #ed4014;
                    margin-right: 2px;
                }
            }
            
            .control {
                flex: 1;
                position: relative;
                
                .error-tip {
                    position: absolute;
                    color: #ed4014;
                    font-size: 12px;
                    margin-top: 5px;
                }
                
                /deep/ .ivu-select.error-input .ivu-select-selection,
                /deep/ .ivu-input.error-input {
                    border-color: #ed4014;
                }
                
                /deep/ .ivu-select {
                    width: 100%;
                }
            }
        }
    }
    
    /deep/ .ivu-modal-footer {
        border-top: none;
        padding-top: 0;
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
        }
    }
}
</style>