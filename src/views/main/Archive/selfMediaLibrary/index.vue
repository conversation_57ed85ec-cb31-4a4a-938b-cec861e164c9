<template>
  <div class="libiray_box">
    <div class="header">
      <div class="left">
        <div class="left_item"  style="cursor: pointer;" @click="getCount()">
          <p>涉济账号总量</p>
          <div class="num_box">
            <span class="num">{{ accountTotal }}</span>
            <span></span>
          </div>
        </div>
        <div class="left_item" style="cursor: pointer;" @click="getCount('hasYinsi=1')">
          <p>网上精准画像</p>
          <div class="num_box">
            <span class="num">{{ hasYinsiCount }}</span>
          </div>
        </div>
      </div>
      <div class="right">
        <!-- <div class="ttt">系统内容涉密，请勿外传！</div> -->
        <div class="box_list">
          <div
            class="list_item"
            v-for="(value, key, index) in platformTotal"
            :key="index"
          >
            <div class="desc">
              <svg-icon
                :icon-class="platformIconList[key].icon"
                style="width: 28px; height: 28px;"
              ></svg-icon>
              <span
                class="num"
                :style="{ color: platformIconList[key].color }"
                >{{ value || 0 }}</span
              >
            </div>
            <div class="img_bg">
              <svg-icon
                :icon-class="platformIconList[key].bg"
                style="width: 200px; height: 48px;"
              ></svg-icon>
              <div class="text">
                {{ platformIconList[key].name }}
              </div>
            </div>
          </div>
        </div>
        <div class="echarts_box">
          <div class="echarts_ref" ref="echarts"></div>
        </div>
        <!-- <div class="btn" style="right: 152px;" @click="openAddAccountModal">
          <svg-icon icon-class="添加关键词" />
          账号添加
        </div> -->
        <div
          class="btn"
          @click="toAccountInfoAuditResult"
          style="width: 110px; right: 35px;"
        >
          <svg-icon icon-class="用户" />
          账号操作记录
        </div>
      </div>
    </div>
    <div class="content">
      <div class="main_content">
        <div class="controls">
          <Checkbox
            :indeterminate="indeterminate"
            :value="checkAll"
            @click.prevent.native="handleCheckAll"
            >全选
          </Checkbox>
          <Poptip confirm title="确认删除所选中的信息吗?" @on-ok="del">
            <div class="btn">
              <svg-icon icon-class="榜单-批量删除" />
              批量删除
            </div>
          </Poptip>
          <div class="btn" @click="handleLevelUp">
            <svg-icon
              style="font-size: 18px; vertical-align: middle;"
              icon-class="监测列表-编辑白色"
            />
            等级修改
          </div>
           <div
	         style="background:red;color: white;text-align: right; font-weight: 600;margin-left:auto;padding:0 10px;font-size: 20px;"
	       >
	         敏感资料 严禁外泄
	       </div>
        </div>
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
          <infoList
            :list="list"
            :pageNo="pageNo"
            :pageSize="pageSize"
            :orderBy="orderBy"
            :orderByType="orderByType"
            @orderByChange="orderByChange"
            :loading="loading"
            @load="load"
          ></infoList>
        </CheckboxGroup>
        <Page
          v-show="total > 0"
          :total="total"
          :current="pageNo"
          :page-size="pageSize"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
          show-total
          show-elevator
          show-sizer
        />
      </div>
      <Filters @query="query" ref="filters" />
    </div>
  </div>
</template>

<script>
import Filters from "../components/listFilters.vue";
import platformIcon from "@/components/platformIcon";
import infoList from "../components/infoList.vue";
import platform from "@/assets/json/situations";
import echarts from "echarts";

export default {
  data() {
    return {
      accountTotal: 0,
      disposalTimes: 0,
      hasYinsiCount: 0,
      pageNo: 1,
      pageSize: 10,
      list: [],
      total: 0,
      orderBy: "update_time",
      orderByType: "desc",
      loading: false,
      params: {},
      platform,
      // 修改 data 中的 platformTotal
      platformTotal: {
        10: 0, // 新浪微博
        20: 0, // 微信公众号
        30: 0, // 媒体网站
        31: 0, // 客户端
        50: 0, // "快手"new
        60: 0, // 论坛贴吧
        80: 0, // 小红书
        81: 0, // 抖音new
        600: 0, // 今日头条
        601: 0, //其他视频 new
      },
      platformIconList: {
        10: {
          name: "新浪微博",
          icon: "m新浪微博",
          bg: "m新浪微博bg",
          color: "#deb43c",
        },
        20: {
          name: "微信公众号",
          icon: "m微信",
          bg: "m微信bg",
          color: "#33917c",
        },
        30: {
          name: "媒体网站",
          icon: "m媒体网站",
          bg: "m媒体网站bg",
          color: "#6fa4d8",
        },
        31: {
          name: "客户端",
          icon: "m客户端",
          bg: "m客户端bg",
          color: "#6f7fd8",
        },
        60: {
          name: "论坛贴吧",
          icon: "m论坛贴吧",
          bg: "m论坛贴吧bg",
          color: "#5b83db",
        },
        80: {
          name: "小红书",
          icon: "m小红书",
          bg: "m小红书bg",
          color: "#e06984",
        },
        199: {
          name: "短视频",
          icon: "m短视频",
          bg: "m短视频bg",
          color: "#335091",
        },
        600: {
          name: "今日头条",
          icon: "m今日头条",
          bg: "m今日头条bg",
          color: "#b96060",
        },
        50: {
          name: "快手",
          icon: "m快手",
          bg: "m新浪微博bg",
          color: "#deb43c",
        },
        81: {
          name: "抖音",
          icon: "m抖音",
          bg: "m短视频bg",
          color: "#335091",
        },
        601: {
          name: "其他视频",
          icon: "m短视频",
          bg: "m论坛贴吧bg",
          color: "#5b83db",
        }
      },
      checkAllGroup: [],
      indeterminate: false,
      checkAll: false,
      // 新增：ECharts实例管理
      myChart: null,
    };
  },
  components: {
    platformIcon,
    infoList,
    Filters,
  },
  mounted() {
    this.getCount();
    this.getLog(this.$route.meta.moduleName);
  },
  // 新增：组件销毁时清理ECharts实例
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
  },
  methods: {
    getCount(p) {
      var url = "/accountInfoNew/accountNum";
      if (p) {
        url += "?" + p;
      }
      this.$http.get(url).then((res) => {
        const data = res.body;
        this.accountTotal = data.data ? data.data.total || 0 : 0;
        this.disposalTimes = data.data ? data.data.disposalTimes || 0 : 0;
        this.hasYinsiCount = data.data ? data.data.hasYinsiCount || 0 : 0;
        this.platformTotal = data.data ? data.data.data || {} : {};
        this.$nextTick(() => {
          this.setCharts(this.platformTotal);
        });
      });
    },
    getList() {
      this.loading = true;
      this.$http
        .get("/accountInfoNew/infoList", {
          params: {
            pageNo: this.pageNo,
            pageSize: this.pageSize,
            orderBy: this.orderBy,
            orderByType: this.orderByType,
            ...this.params,
          },
        })
        .then((res) => {
          this.list = res.body.data.list || [];
          this.total = res.body.data.count || 0;
          this.loading = false;
        });
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.checkAll = false;
      this.getList();
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.checkAll = false;
      this.getList();
    },
    query(d) {
      console.log(d);
      this.pageNo = 1;
      this.params = {
        ...d,
      };
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.checkAll = false;
      this.getList();
    },
    orderByChange(key, type) {
      this.pageNo = 1;
      this.orderBy = key;
      this.orderByType = type;
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.checkAll = false;
      this.getList();
    },
    load() {
      this.pageNo = 1;
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.checkAll = false;
      this.getList();
    },
    setCharts(chartData) {
      // 修复：确保DOM元素存在
      if (!this.$refs.echarts) {
        console.warn('ECharts容器不存在');
        return;
      }

      // 修复：销毁旧的ECharts实例
      if (this.myChart) {
        this.myChart.dispose();
        this.myChart = null;
      }

      const dataList = [];
      for (let key in chartData) {
        if (this.platformIconList[key]) {
          dataList.push({
            value: chartData[key],
            name: this.platformIconList[key].name,
            color: this.platformIconList[key].color,
          });
        }
      }
      
      if (dataList.length == 0) {
        return;
      }

      // 修复：计算图表数据的总和，用于百分比计算
      const chartTotal = dataList.reduce((sum, item) => sum + item.value, 0);
      
      // 找到数值最大的项目索引
      let currentIndex = dataList.reduce((maxIndex, current, index, arr) => {
        return current.value > arr[maxIndex].value ? index : maxIndex;
      }, 0);

      // 修复：创建新的ECharts实例并保存引用
      this.myChart = echarts.init(this.$refs.echarts);
      
      var option = {
        tooltip: {
          trigger: "item",
          formatter: function(params) {
            const percent = chartTotal > 0 ? ((params.value / chartTotal) * 100).toFixed(1) : '0.0';
            return `${params.name}: ${params.value} (${percent}%)`;
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["60%", "80%"],
            color: dataList.map((item) => item.color),
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            // 修复：添加中心标签显示
            graphic: {
              type: 'text',
              left: 'center',
              top: 'center',
              style: {
                text: `${dataList[currentIndex].value}\n${((dataList[currentIndex].value / chartTotal) * 100).toFixed(1)}%`,
                textAlign: 'center',
                fill: '#333',
                fontSize: 16,
                fontWeight: 'bold'
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: "bold",
                formatter: (params) => {
                  // 修复：使用图表数据总和计算百分比
                  const percent = chartTotal > 0 ? ((params.value / chartTotal) * 100).toFixed(1) : '0.0';
                  return (
                    "{a|" +
                    percent +
                    "%" +
                    "}" +
                    "\n" +
                    "{b|" +
                    params.name +
                    "}"
                  );
                },
                rich: {
                  a: {
                    color: "red",
                    lineHeight: 40,
                    fontSize: 25,
                  },
                  b: {
                    color: "#333",
                    fontSize: 14,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: dataList,
          },
        ],
      };

      // 修复：使用保存的实例引用
      this.myChart.setOption(option);
      
      // 设置默认高亮
      this.myChart.dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        dataIndex: currentIndex,
      });

      // 修复：移除旧的事件监听器，添加新的事件监听器
      this.myChart.off('mouseover'); // 移除之前的事件监听器
      
      // 监听鼠标移入事件
      this.myChart.on("mouseover", (params) => {
        this.myChart.dispatchAction({
          type: "downplay",
          seriesIndex: 0,
        });
        if (params.seriesIndex === 0) {
          // 先取消之前的高亮
          this.myChart.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            dataIndex: params.dataIndex,
          });
        }
      });
    },
    toAccountInfoAuditResult() {
      const { href } = this.$router.resolve({
        path: "/main/accountInfoAuditView",
      });
      window.open(href, "_blank");
    },
    del(id) {
      let params = {};
      if (id) {
        params.ids = id;
      } else {
        if (this.checkAllGroup.length === 0) {
          this.$Message.warning("请选择信息后重试！");
          return false;
        }
        
      }
      this.$http
        .post("/accountInfoNew/deleteAccount", {}, { params })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
            this.load();
          }
        });
    },
    checkAllGroupChange(data) {
      if (data.length === this.list.length) {
        this.checkAll = true;
        this.indeterminate = false;
      } else if (data.length === 0) {
        this.checkAll = false;
        this.indeterminate = false;
      } else {
        this.checkAll = false;
        this.indeterminate = true;
      }
    },
    handleCheckAll() {
      this.checkAll = !this.checkAll;
      this.checkAllGroup = this.checkAll
        ? this.list.map((item) => item.id)
        : [];
    },
    handleLevelUp() {
      if (this.checkAllGroup.length === 0) {
        this.$Message.warning("请选择信息后重试！");
        return false;
      }
      const levelUp = () => import("../components/levelUpNew.vue");
      this.$modal.show({
        component: levelUp,
        componentProps: {
          checkids: this.checkAllGroup,
        },
        componentEvents: {
          close: this.closeLevelUp,
          confirm: this.confirmLevelUp,
        },
        title: "等级修改",
      });
    },
    closeLevelUp() {
      this.$modal.hide();
    },
    confirmLevelUp() {
      this.load();
      this.closeLevelUp();
    },
    openAddAccountModal() {
      const Addxx = () => import("./components/addNewAccount.vue");
      const that = this;
      this.$modal.show({
        component: Addxx,
        componentProps: {
          data: {},
          that,
        },
        componentEvents: {
          close: (d) => {
            if (d != "cancel") {
              this.getCount();
              this.getList();
            }
            that.$modal.hide();
          },
        },
        title: "账号添加", // 传递标题
        zIndexWrap: 10,
        // y: 300,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.libiray_box {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}
.header {
  display: flex;
  justify-content: space-between;
  column-gap: 22px;
  .left {
    width: 250px;
    flex-shrink: 0;
    // background-image: linear-gradient(45deg, #4981fe, #a7d6f6);
    .left_item {
      width: 250px;
      height: 146px;
      border-radius: 20px;
      box-sizing: border-box;
      padding: 17px;
      p {
        height: 40px;
        line-height: 40px;
        font-weight: 600;
        color: #ffffff;
        font-size: 28px;
      }
      .num_box {
        margin-top: 16px;
        display: flex;
        column-gap: 10px;
        justify-content: space-between;
        .num {
          height: 56px;
          line-height: 56px;
          font-family: Impact;
          color: #ffffff;
          font-size: 46px;
        }
      }
      &:first-child {
        background: linear-gradient(270deg, #79a4ff 0%, #4e57d2 100%);
        box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        margin-bottom: 20px;
      }
      &:last-child {
        background: linear-gradient(270deg, #f5c997 0%, #f65177 100%);
        box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
      }
    }
  }
  .right {
    flex: 1;
    background-color: #fff;
    overflow: hidden;
    box-sizing: border-box;
    padding: 40px 30px 35px;
    display: flex;
    position: relative;
    .ttt {
      position: absolute;
      top: 20px;
      left: 30px;
      width: 905px;
      font-size: 28px;
      color: #ea194a;
      font-weight: 600;
      text-align: center;
    }
    .box_list {
      display: flex;
      flex-wrap: wrap;
      column-gap: 25px;
      row-gap: 50px;
      width: 1100px;
      .list_item {
        width: 200px;
        .desc {
          display: flex;
          align-items: center;
          justify-content: center;
          .num {
            margin-left: 4px;
            font-weight: 600;
            color: #33917c;
            font-size: 26px;
            line-height: 1;
          }
        }
        .img_bg {
          position: relative;
          font-size: 0;
          .text {
            color: #ffffff;
            font-size: 18px;
            text-align: center;
            position: absolute;
            z-index: 1;
            top: 16px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
      }
    }
    .echarts_box {
      padding-left: 70px;
      flex: 1;
      overflow: hidden;
      .echarts_ref {
        width: 100%;
        height: 100%;
      }
    }
    .btn {
      color: #2464c4;
      background-color: #e2e8f5;
      width: 100px;
      position: absolute;
      right: 40px;
      top: 30px;
    }
  }
  .num {
    font-size: 20px;
    font-family: PingFang SC;
    color: #fff;
  }
}
.content {
  margin-top: 10px;
  height: calc(~"100% - 100px");
  display: flex;
  column-gap: 10px;
  .main_content {
    width: 1370px;
    background-color: #fff;
  }
  .controls {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    font-size: 16px;
    //border-bottom: 2px solid #faf7fa;

    /deep/ .ivu-checkbox-wrapper {
      font-size: 16px;
      margin-right: 60px;
    }

    .btn {
      background: #5585ec;
      border-radius: 4px;
      line-height: 30px;
      padding: 0 18px;
      color: #fff;
      margin-right: 20px;
      cursor: pointer;
    }
  }
}
</style>
