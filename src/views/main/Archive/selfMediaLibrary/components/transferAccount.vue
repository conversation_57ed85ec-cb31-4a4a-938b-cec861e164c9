<template>
  <div class="transfer-account-container">
    <!-- 转移类型选择 -->
    <div class="transfer-type-selector">
      <div class="radio-group">
        <RadioGroup v-model="transferType">
          <Radio :label="'media'">转移到媒体库</Radio>
          <Radio :label="'business'">转移到商业平台库</Radio>
        </RadioGroup>
      </div>
    </div>

    <div class="transfer-content">
      <!-- 媒体库转移面板 -->
      <div class="left-panel" v-if="transferType === 'media'">
        <div class="panel-title">媒体信息完善</div>
        <div class="form-content">
          <div class="form-item">
            <div class="label">服务名称：</div>
            <Input v-model="mediaFormData.serviceName" disabled placeholder="转移账号的昵称" />
          </div>
          <div class="form-item">
            <div class="label">单位名称：</div>
            <div class="input-with-search">
              <Input
                v-model="mediaFormData.unitName"
                placeholder="输入媒体主体名称"
                @on-change="handleUnitNameChange"
                @on-focus="showUnitSuggestions = true"
              />
              <Icon type="ios-search" class="search-icon" />
              <div class="suggestions" v-show="showUnitSuggestions && unitSuggestions.length > 0">
                <div
                  v-for="(item, index) in unitSuggestions"
                  :key="index"
                  class="suggestion-item"
                  @click="selectUnit(item)"
                > 
                  {{ item.dwmc }} 
                </div>
              </div>
            </div>
          </div>
          <div class="form-item">
            <div class="label">许可证编号：</div>
            <Input v-model="mediaFormData.licenseNumber" disabled placeholder="自动填充许可证编号" />
          </div>
          <div class="form-item">
            <div class="label">渠道：</div>
            <Select v-model="mediaFormData.channel" @on-change="handleChannelChange">
              <Option value="互联网站">互联网站</Option>
              <Option value="论坛">论坛</Option>
              <Option value="博客">博客</Option>
              <Option value="微博客">微博客</Option>
              <Option value="公众账号">公众账号</Option>
              <Option value="应用程序">应用程序</Option>
              <Option value="网络直播">网络直播</Option>
              <Option value="其他">其他</Option>
            </Select>
          </div>
          <div class="form-item" v-if="showServiceAddress">
            <div class="label">服务地址：</div>
            <Input v-model="mediaFormData.serviceAddress" placeholder="填写服务地址" />
          </div>
          <div class="form-item" v-if="showPlatform">
            <div class="label">平台：</div>
            <Input v-model="mediaFormData.platform" placeholder="填写平台名称。如新浪微博平台、抖音平台、今日头条平台等" />
          </div>
          <div class="form-item">
            <div class="label">申请主体：</div>
            <Input v-model="mediaFormData.applicationSubject" disabled placeholder="自动填充申请主体" />
          </div>
          <div class="form-item">
            <div class="label">属地：</div>
            <Select v-model="mediaFormData.dependency" :disabled="isProvinceSelected">
              <Option v-for="(item, index) in provinces" :key="index" :value="item">{{ item }}</Option>
            </Select>
          </div>
          <div class="form-item">
            <div class="label">媒体分类：</div>
            <Select v-model="mediaFormData.mediaClassification">
              <Option value="中央媒体">中央媒体</Option>
              <Option value="省内媒体">省内媒体</Option>
              <Option value="市属媒体">市属媒体</Option>
              <Option value="区县媒体">区县媒体</Option>
              <Option value="省外媒体">省外媒体</Option>
            </Select>
          </div>
          <div class="form-item">
            <div class="label">是否备案：</div>
            <div>
              <RadioGroup v-model="mediaFormData.isRegister">
                <Radio :label="1">已备案</Radio>
                <Radio :label="0">未备案</Radio>
              </RadioGroup>
            </div>
          </div>
        </div>
      </div>

      <!-- 商业平台库转移面板 -->
      <div class="left-panel" v-if="transferType === 'business'">
        <div class="panel-title">商业平台信息完善</div>
        <div class="form-content">
          <div class="form-item">
            <div class="label">服务名称：</div>
            <Input v-model="businessFormData.serviceName" disabled placeholder="转移账号的昵称" />
          </div>
          <div class="form-item">
            <div class="label">单位名称：</div>
            <div class="input-with-search">
              <Input
                v-model="businessFormData.unitName"
                placeholder="输入商业平台主体名称"
                @on-change="handleBusinessUnitNameChange"
                @on-focus="showBusinessUnitSuggestions = true"
              />
              <Icon type="ios-search" class="search-icon" />
              <div class="suggestions" v-show="showBusinessUnitSuggestions && businessUnitSuggestions.length > 0">
                <div
                  v-for="(item, index) in businessUnitSuggestions"
                  :key="index"
                  class="suggestion-item"
                  @click="selectBusinessUnit(item)"
                > 
                  {{ item.dwmc }} 
                </div>
              </div>
            </div>
          </div>
          <div class="form-item">
            <div class="label">许可证编号：</div>
            <Input v-model="businessFormData.licenseNumber" disabled placeholder="自动填充许可证编号" />
          </div>
          <div class="form-item">
            <div class="label">渠道：</div>
            <Select v-model="businessFormData.channel" @on-change="handleBusinessChannelChange">
              <Option value="互联网站">互联网站</Option>
              <Option value="论坛">论坛</Option>
              <Option value="博客">博客</Option>
              <Option value="微博客">微博客</Option>
              <Option value="公众账号">公众账号</Option>
              <Option value="应用程序">应用程序</Option>
              <Option value="网络直播">网络直播</Option>
              <Option value="其他">其他</Option>
            </Select>
          </div>
          <div class="form-item" v-if="showBusinessServiceAddress">
            <div class="label">服务地址：</div>
            <Input v-model="businessFormData.serviceAddress" placeholder="填写服务地址" />
          </div>
          <div class="form-item" v-if="showBusinessPlatform">
            <div class="label">平台：</div>
            <Input v-model="businessFormData.platform" placeholder="填写平台名称。如新浪微博平台、抖音平台、今日头条平台等" />
          </div>
          <div class="form-item">
            <div class="label">属地：</div>
            <Select v-model="businessFormData.dependency" :disabled="isBusinessProvinceSelected">
              <Option v-for="(item, index) in provinces" :key="index" :value="item">{{ item }}</Option>
            </Select>
          </div>
          <div class="form-item">
            <div class="label">是否备案：</div>
            <div>
              <RadioGroup v-model="businessFormData.isRegister">
                <Radio :label="1">已备案</Radio>
                <Radio :label="0">未备案</Radio>
              </RadioGroup>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧查询面板 -->
      <div class="right-panel">
        <div class="panel-title">{{ transferType === 'media' ? '媒体信息查询' : '商业平台信息查询' }}</div>
        <div class="search-box">
          <div class="label">昵称：</div>
          <Input v-model="searchKeyword" placeholder="昵称" />
          <Button type="primary" icon="ios-search" @click="searchMedia"></Button>
        </div>
        <div class="table-container">
          <div class="table-wrapper">
            <table class="table-header">
              <colgroup>
                <col class="index-col">
                <col class="unit-name-col">
                <col class="service-name-col">
                <col class="service-addr-col">
              </colgroup>
              <thead>
                <tr>
                  <th class="index-col">序号</th>
                  <th class="unit-name-col">单位名称</th>
                  <th class="service-name-col">服务名称</th>
                  <th class="service-addr-col">服务地址/平台</th>
                </tr>
              </thead>
            </table>
            <div class="table-body">
              <template v-if="mediaList.length > 0">
                <table class="media-table" v-for="(unit, unitIndex) in mediaList" :key="'unit-'+unitIndex">
                  <colgroup>
                    <col class="index-col">
                    <col class="unit-name-col">
                    <col class="service-name-col">
                    <col class="service-addr-col">
                  </colgroup>
                  <tbody>
                    <!-- 第一行显示单位信息 -->
                    <tr class="unit-row" v-if="unit.children && unit.children.length > 0">
                      <td class="index-col" :rowspan="unit.children.length">{{ unitIndex + 1 }}</td>
                      <td class="unit-name-col" :rowspan="unit.children.length">{{ unit.unitName }}</td>
                      <td class="service-name-col">{{ unit.children[0].serviceName }}</td>
                      <td class="service-addr-col">{{ unit.children[0].serviceAddress }}</td>
                    </tr>

                    <!-- 后续行只显示服务信息 -->
                    <tr class="service-row" v-for="(service, serviceIndex) in unit.children.slice(1)" :key="'service-'+unitIndex+'-'+serviceIndex">
                      <td class="service-name-col">{{ service.serviceName }}</td>
                      <td class="service-addr-col">{{ service.serviceAddress }}</td>
                    </tr>
                  </tbody>
                </table>
              </template>
              <div class="no-data" v-if="mediaList.length === 0">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="footer">
      <Button type="primary" @click="onlyDeleteAccount">仅删除自媒体库账号</Button>
      <Button type="primary" @click="deleteAndTransfer">
        {{ transferType === 'media' ? '删除并转移至媒体库' : '删除并转移至商业平台库' }}
      </Button>
      <Button class="close-button" @click="closeModal">关闭</Button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TransferAccount',
  props: {
    accountInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 转移类型选择
      transferType: 'media', // 'media' 或 'business'
      
      // 媒体库表单数据
      mediaFormData: {
        serviceName: '',
        unitName: '',
        licenseNumber: '',
        channel: '',
        serviceAddress: '',
        platform: '',
        applicationSubject: '',
        dependency: '',
        mediaClassification: '',
        isRegister: 0,
        userId: ''
      },
      
      // 商业平台库表单数据
      businessFormData: {
        serviceName: '',
        unitName: '',
        licenseNumber: '',
        channel: '',
        serviceAddress: '',
        platform: '',
        dependency: '',
        isRegister: 0,
        userId: ''
      },
      
      // 媒体库相关状态
      showServiceAddress: false,
      showPlatform: false,
      isProvinceSelected: false,
      unitSuggestions: [],
      showUnitSuggestions: false,
      
      // 商业平台库相关状态
      showBusinessServiceAddress: false,
      showBusinessPlatform: false,
      isBusinessProvinceSelected: false,
      businessUnitSuggestions: [],
      showBusinessUnitSuggestions: false,
      
      // 通用数据
      provinces: [
        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '新疆生产建设兵团'
      ],
      searchKeyword: '',
      mediaList: []
    };
  },
  created() {
    if (this.accountInfo) {
      console.log("accountInfo", this.accountInfo);
      this.mediaFormData.serviceName = this.accountInfo.accountName || '';
      this.mediaFormData.userId = this.accountInfo.userId || '';/* 用户ID 自媒体库列表接口返回的UserId字段 */
      this.businessFormData.serviceName = this.accountInfo.accountName || '';
      this.businessFormData.userId = this.accountInfo.userId || '';/* 用户ID 自媒体库列表接口返回的UserId字段 */
      this.searchKeyword = this.accountInfo.accountName || '';
      this.searchMedia();
    }
  },
  methods: {
    closeModal() {
      this.$emit('close', 'cancel');
    },
    
    // 媒体库渠道变化处理
    handleChannelChange(value) {
      if (['互联网站', '论坛', '博客', '微博客'].includes(value)) {
        this.showServiceAddress = true;
        this.showPlatform = false;
      } else if (value === '公众账号') {
        this.showServiceAddress = false;
        this.showPlatform = true;
      } else {
        this.showServiceAddress = false;
        this.showPlatform = false;
      }
    },
    
    // 商业平台库渠道变化处理
    handleBusinessChannelChange(value) {
      if (['互联网站', '论坛', '博客', '微博客'].includes(value)) {
        this.showBusinessServiceAddress = true;
        this.showBusinessPlatform = false;
      } else if (value === '公众账号') {
        this.showBusinessServiceAddress = false;
        this.showBusinessPlatform = true;
      } else {
        this.showBusinessServiceAddress = false;
        this.showBusinessPlatform = false;
      }
    },

    // 媒体库单位名称变化处理
    handleUnitNameChange() {
      if (this.mediaFormData.unitName) {
        this.searchUnitByKeyword('media');
      } else {
        this.unitSuggestions = [];
        this.showUnitSuggestions = false;
      }
    },
    
    // 商业平台库单位名称变化处理
    handleBusinessUnitNameChange() {
      if (this.businessFormData.unitName) {
        this.searchUnitByKeyword('business');
      } else {
        this.businessUnitSuggestions = [];
        this.showBusinessUnitSuggestions = false;
      }
    },
    
    // 搜索单位信息
    searchUnitByKeyword(type) {
      const keyword = type === 'media' ? this.mediaFormData.unitName : this.businessFormData.unitName;
      const apiUrl = type === 'media' ? '/media/mainBodyByKey' : '/resourceslibrary/mainBodyByKey';
      
      this.$http.get(apiUrl, {
        params: {
          keyWord: keyword
        }
      }).then(res => {
        if (res.body.status === 0 && res.body.data) {
          if (type === 'media') {
            this.unitSuggestions = res.body.data;
            this.showUnitSuggestions = true;
          } else {
            this.businessUnitSuggestions = res.body.data;
            this.showBusinessUnitSuggestions = true;
          }
        } else {
          if (type === 'media') {
            this.unitSuggestions = [];
            this.showUnitSuggestions = false;
          } else {
            this.businessUnitSuggestions = [];
            this.showBusinessUnitSuggestions = false;
          }
        }
      });
    },
    
    // 选择媒体库单位
    selectUnit(unit) {
      this.mediaFormData.unitName = unit.dwmc;
      this.mediaFormData.licenseNumber = unit.xkzbh;
      this.mediaFormData.applicationSubject = unit.sqzt || '';

      if (unit.sqzt && this.provinces.includes(unit.sqzt)) {
        this.mediaFormData.dependency = unit.sqzt;
        this.isProvinceSelected = true;
      } else {
        this.isProvinceSelected = false;
      }

      this.showUnitSuggestions = false;
    },
    
    // 选择商业平台库单位
    selectBusinessUnit(unit) {
      this.businessFormData.unitName = unit.dwmc;
      this.businessFormData.licenseNumber = unit.xkzbh;

      if (unit.sqzt && this.provinces.includes(unit.sqzt)) {
        this.businessFormData.dependency = unit.sqzt;
        this.isBusinessProvinceSelected = true;
      } else {
        this.isBusinessProvinceSelected = false;
      }

      this.showBusinessUnitSuggestions = false;
    },
    
    // 搜索媒体信息
    searchMedia() {
      const apiUrl = this.transferType === 'media' ? '/media/otherListByKey' : '/resourceslibrary/otherListByKey';
      
      this.$http.get(apiUrl, {
        params: {
          keyWord: this.searchKeyword
        }
      }).then(res => {
        if (res.body.status === 0 && res.body.data && res.body.data.list) {
          this.mediaList = res.body.data.list.slice(0, 5);
        } else {
          this.mediaList = [];
        }
      });
    },
    
    // 验证媒体库表单
    validateMediaForm() {
      const requiredFields = ['serviceName', 'unitName', 'licenseNumber', 'channel',
                             'applicationSubject', 'dependency', 'mediaClassification'];

      if (this.showServiceAddress) {
        requiredFields.push('serviceAddress');
      }
      if (this.showPlatform) {
        requiredFields.push('platform');
      }
      
      for (const field of requiredFields) {
        if (!this.mediaFormData[field]) {
          if(field === 'licenseNumber'){
            this.$Message.error('媒体库中没有该账号主体，请重新填写');
          }else{
            this.$Message.error('请将以上内容全部填写后再转移账号');
          }
          return false;
        }
      }
      return true;
    },
    
    // 验证商业平台库表单
    validateBusinessForm() {
      const requiredFields = ['serviceName', 'unitName', 'licenseNumber', 'channel', 'dependency'];

      if (this.showBusinessServiceAddress) {
        requiredFields.push('serviceAddress');
      }
      if (this.showBusinessPlatform) {
        requiredFields.push('platform');
      }
      
      for (const field of requiredFields) {
        if (!this.businessFormData[field]) {
          if(field === 'licenseNumber'){
            this.$Message.error('商业平台库中没有该账号主体，请重新填写');
          }else{
            this.$Message.error('请将以上内容全部填写后再转移账号');
          }
          return false;
        }
      }
      return true;
    },
    
    // 仅删除自媒体库账号
    onlyDeleteAccount() {
      const apiUrl = this.transferType === 'media' ? '/media/addMedia' : '/resourceslibrary/addResourceslibrary';
      const params = {
        userId: this.transferType === 'media' ? this.mediaFormData.userId : this.businessFormData.userId,
        accountId: this.accountInfo.id, // 自媒体库主键ID
        type: 0 // 仅删除自媒体账号
      };
      
      this.$http.post(apiUrl, params).then(res => {
        if (res.body.status === 0) {
          this.$Message.success(res.body.message || '删除成功！');
          this.$emit('close', 'success');
        } else {
          this.$Message.error(res.body.message || '操作失败');
        }
      });
    },
    
    // 删除并转移
    deleteAndTransfer() {
      if (this.transferType === 'media') {
        this.deleteAndTransferToMedia();
      } else {
        this.deleteAndTransferToBusiness();
      }
    },
    
    // 删除并转移至媒体库
    deleteAndTransferToMedia() {
      if (!this.validateMediaForm()) {
        return;
      }

      const params = {
        serviceName: this.mediaFormData.serviceName,
        unitName: this.mediaFormData.unitName,
        licenseNumber: this.mediaFormData.licenseNumber,
        channel: this.mediaFormData.channel,
        applicationSubject: this.mediaFormData.applicationSubject,
        dependency: this.mediaFormData.dependency,
        mediaClassification: this.mediaFormData.mediaClassification,
        isRegister: this.mediaFormData.isRegister,
        userId: this.mediaFormData.userId,
        accountId: this.accountInfo.id,
        situation: this.accountInfo.situation,
        type: 1
      };

      if (this.showServiceAddress) {
        params.serviceAddress = this.mediaFormData.serviceAddress;
      } else if (this.showPlatform) {
        params.serviceAddress = this.mediaFormData.platform;
      }

      this.$http.post('/media/addMedia', params).then(res => {
        if (res.body.status === 0) {
          this.$Message.success(res.body.message || '转移成功！');
          this.$emit('close', 'success');
        } else {
          this.$Message.error(res.body.message || '操作失败');
        }
      });
    },
    
    // 删除并转移至商业平台库
    deleteAndTransferToBusiness() {
      if (!this.validateBusinessForm()) {
        return;
      }

      const params = {
        serviceName: this.businessFormData.serviceName,
        unitName: this.businessFormData.unitName,
        licenseNumber: this.businessFormData.licenseNumber,
        channel: this.businessFormData.channel,
        dependency: this.businessFormData.dependency,
        isRegister: this.businessFormData.isRegister,
        userId: this.businessFormData.userId,
        accountId: this.accountInfo.id, // 自媒体库主键ID
        situation: this.accountInfo.situation,
        type: 1 // 删除并移至媒体库
      };

      if (this.showBusinessServiceAddress) {
        params.serviceAddress = this.businessFormData.serviceAddress;
      } else if (this.showBusinessPlatform) {
        params.serviceAddress = this.businessFormData.platform;
      }

      this.$http.post('/resourceslibrary/addResourceslibrary', params).then(res => {
        if (res.body.status === 0) {
          this.$Message.success(res.body.message || '转移成功！');
          this.$emit('close', 'success');
        } else {
          this.$Message.error(res.body.message || '操作失败');
        }
      });
    }
  },
  watch: {
    transferType: {
      handler(newVal) {
        if (newVal) {
          this.searchMedia();
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.transfer-account-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 1500px;
  min-height: 600px;
}

// 转移类型选择器样式
.transfer-type-selector {
  padding: 20px;
  border-bottom: 1px solid #dcdee2;
  // background-color: #f8f9fa;
  
  .selector-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
  }
  
  .radio-group {
    display: flex;
    justify-content: center;

    .ivu-radio-group {
      .ivu-radio-wrapper {
        margin-right: 20px;
        font-size: 14px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  border-radius: 50%;
  background-color: #f8f8f9;

  &:hover {
    background-color: #e8eaec;
  }

  .ivu-icon {
    font-size: 20px;
    color: #999;
  }
}

.transfer-content {
  display: flex;
  flex: 1;
}

.left-panel {
  min-width: 500px;
  padding: 20px;
  position: relative;
  
  &::before {
    content: "";
    width: 1px;
    position: absolute;
    top: 20px;
    bottom: 20px;
    right: 0px;
    background: #DCDCDC;
    opacity: 0.5;
  }
}

.right-panel {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}

.form-content {
  .form-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    .label {
      min-width: 100px;
      text-align: right;
      margin-right: 10px;
    }

    .input-with-search {
      position: relative;
      flex: 1;

      .search-icon {
        position: absolute;
        right: 0px;
        text-align: center;
        top: 50%;
        transform: translateY(-50%);
      }

      .suggestions {
        position: absolute;
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
        background: #fff;
        border: 1px solid #dcdee2;
        border-top: none;
        z-index: 10;

        .suggestion-item {
          padding: 8px 12px;
          cursor: pointer;

          &:hover {
            background-color: #f8f8f9;
          }
        }
      }
    }
  }
}

.search-box {
  display: flex;
  margin-bottom: 15px;
  gap: 10px;
  
  .label {
    min-width: 50px;
    text-align: right;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .ivu-input {
    margin-right: 10px;
  }
}

.table-container {
  border: 1px solid #dcdee2;
  border-radius: 4px;
  width: 100%;
  position: relative;

  .table-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  /* 统一列宽设置 */
  .index-col {
    width: 50px;
    min-width: 50px;
  }

  .unit-name-col {
    width: 200px;
    min-width: 200px;
  }

  .service-name-col, .service-addr-col {
    width: calc((100% - 250px) / 2);
    min-width: 200px;
  }

  /* 表头样式 */
  .table-header {
    width: ~"calc(100% - 8px)";
    border-collapse: collapse;
    background-color: #f8f8f9;
    font-weight: bold;
    border-bottom: 1px solid #dcdee2;
    table-layout: fixed;

    th {
      padding: 10px;
      text-align: center;
      border-right: 1px solid #dcdee2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:last-child {
        border-right: 1px solid #dcdee2;
      }
    }
  }

  /* 表格内容区域 */
  .table-body {
    max-height: 300px;
    overflow-y: scroll;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      display: block;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .media-table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
      margin-bottom: 1px;

      td {
        padding: 10px;
        text-align: center;
        border-right: 1px solid #dcdee2;
        border-bottom: 1px solid #dcdee2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:last-child {
          border-right: 1px solid #dcdee2;
        }
      }

      .unit-row {
        // background-color: #f0f0f0;
      }

      .service-row {
        background-color: #fff;

        &:last-child td {
          border-bottom: 1px solid #dcdee2;
        }
      }
    }

    .no-data {
      padding: 30px;
      text-align: center;
      color: #999;
    }

    .tip {
      font-size: 12px;
      color: #ff9900;
      margin-top: 5px;
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #dcdee2;

  .ivu-btn {
    margin: 0 10px;
  }

  .close-button {
    background-color: #909399;
    color: #fff;
    border-color: #909399;

    &:hover {
      background-color: #a6a9ad;
      border-color: #a6a9ad;
    }
  }
}
</style>
