<template>
  <div class="task-list flex">
    <div class="cont_left">
      <!-- 任务概览 -->
       <div class="rwgl">
        <div class="tit">任务概览</div>
        <TaskOverview ref="taskOverview" :timeParams="queryParams" />
       </div>
      
      <div class="tab-container">
      <div class="header flex sb">
        <div class="Toggle flex">
          <div
            :class="['cp', 'flex', 'item', activeTab === 0 ? 'active' : '']"
            @click="switchTab(0)"
          >
            已审核 <span v-if="auditedCount > 0">({{ auditedCount }})</span>
            <div class="triangle"></div>
          </div>
          <div
            :class="['cp', 'flex', 'item', activeTab === 1 ? 'active' : '']"
            @click="switchTab(1)"
            v-if="firstReviewers.includes(currentAccount) || secondReviewers.includes(currentAccount)"
          >
            待审核 <span v-if="pendingCount > 0 ">({{ pendingCount }})</span>
            <div class="triangle"></div>
          </div>
        </div>
        <div class="right-controls">
          <Button
            v-show="activeTab === 0"
            type="primary"
            size="large"
            @click="createNewTask">
            <svg-icon icon-class="新建" style="width:14px;height:14px" />
            新建任务
          </Button>
        </div>
      </div>
      <div class="tab-content">
        <!-- 已审核内容 -->
        <div v-show="activeTab === 0" class="tab-pane">
          <div class="task-card-list" ref="taskTable">
            <Spin fix v-if="loading"></Spin>
            <div
              v-for="(row, index) in taskList"
              :key="row.id"
              class="task-card"
              :class="{ 'deleted': row.status === 3 }"
            >
              <!-- 右上角状态标识 -->
              <div class="card-status-corner">
                <svg-icon :icon-class="getStatusIcon(row)" class="status-icon"></svg-icon>
              </div>
              <!-- 序号 -->
              <div class="card-serial">
                  {{ (currentPage - 1) * pageSize + index + 1 }}.
                </div>

              <div class="left">
                <!-- 卡片头部 -->
              <div class="card-header">

                <!-- 任务标题 -->
                <div class="card-title">
                  <a @click="viewTaskDetail(row)" class="task-name-link2" :title="row.name">
                    {{ row.name }}
                  </a>
                </div>
              </div>

              <!-- 任务信息行 -->
              <div class="card-info">
                <span class="info-item">
                  <span class="info-label">下发时间：</span>
                  <span class="info-value">{{ formatDate(row.issuingTime || row.createTime) }}</span>
                </span>
                <span class="info-item">
                  <span class="info-label">任务发起人：</span>
                  <span class="info-value">{{ row.promoterName }} - {{ row.promoterOrganName }}</span>
                </span>
              </div>

              <!-- 备注信息 -->
              <div class="card-remark">
                <span class="remark-label">备注：</span>
                <Tooltip
                  :content="row.remark || '暂无备注'"
                  placement="top"
                  :disabled="!row.remark || row.remark.length <= 20"
                >
                  <span class="remark-text">
                    {{ row.remark ? (row.remark.length > 20 ? row.remark.substring(0,50) + '...' : row.remark) : '暂无备注' }}
                  </span>
                </Tooltip>
              </div>
              </div>
              
              <div class="right">
                 <!-- 数据统计区域 -->
              <div class="card-stats">
                <div class="stat-item">
                  <div class="stat-label">文章数量</div>
                  <div class="stat-value">{{ row.articleNum || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">完成率</div>
                  <div class="stat-value">{{ row.completionRate ? row.completionRate + '%' : '0.0%' }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">任务数</div>
                  <div class="stat-value">{{ row.taskNumber || 0 }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">完成数</div>
                  <div class="stat-value">{{ row.completedQuantity || 0 }}</div>
                </div>
              </div>

              <!-- 操作区域 -->
              <div class="card-actions">
                <div class="action-left">
                  <span class="action-label">操作：</span>
                  <div class="action-buttons">
                    <!-- 完结按钮 -->
                    <Poptip v-if="row.status === 1"
                      confirm
                      placement="bottom"
                      width="300"
                      @on-ok="confirmEndTask(row)"
                      @on-cancel="cancelEndTask">
                      <div slot="title" class="poptip-title">
                        <span>是否完结"{{ row.name }}"任务？</span>
                      </div>
                      <div slot="content" class="poptip-content">
                        <div class="poptip-buttons">
                          <Button type="default" size="small" @click.stop="$refs['endPoptip' + row.id].handleCancel()">取消</Button>
                          <Button type="primary" size="small" @click.stop="confirmEndTask(row)">确定</Button>
                        </div>
                      </div>
                      <span class="action-btn forward-btn" ref="endPoptip{{row.id}}">完结</span>
                    </Poptip>

                    <!-- 转发按钮 -->
                    <Poptip v-if="(row.status === 1 || row.status === 3) && row.isForward === 0"
                      confirm
                      placement="bottom"
                      width="300"
                      @on-ok="confirmForwardTask(row)"
                      @on-cancel="cancelForwardTask">
                      <div slot="title" class="poptip-title">
                        <span>是否将"{{ row.name }}"任务转发至山东通？</span>
                      </div>
                      <div slot="content" class="poptip-content">
                        <div class="poptip-buttons">
                          <Button type="default" size="small" @click.stop="$refs['forwardPoptip' + row.id].handleCancel()">取消</Button>
                          <Button type="primary" size="small" @click.stop="confirmForwardTask(row)">确定</Button>
                        </div>
                      </div>
                      <span class="action-btn forward-btn" ref="forwardPoptip{{row.id}}">
                        <svg-icon icon-class="转发" />
                        转发</span>
                    </Poptip>

                    <!-- 删除按钮 -->
                    <Poptip v-if="account.includes(currentAccount)"
                      confirm
                      placement="bottom"
                      width="180"
                      @on-ok="confirmDeleteTask(row)"
                      @on-cancel="cancelDeleteTask">
                      <div slot="title" class="poptip-title">
                        <span>确认删除任务吗？</span>
                      </div>
                      <div slot="content" class="poptip-content">
                        <div class="poptip-buttons">
                          <Button type="default" size="small" @click.stop="$refs['deletePoptip' + row.id].handleCancel()">取消</Button>
                          <Button type="error" size="small" @click.stop="$refs['deletePoptip' + row.id].handleOk()">删除</Button>
                        </div>
                      </div>
                      <span class="action-btn delete-btn" ref="deletePoptip{{row.id}}">
                        <svg-icon icon-class="删 除" />
                        删除</span>
                    </Poptip>
                  </div>
                </div>

                <div class="action-right">
                  <span class="action-label">处理结果：</span>
                  <div class="disposal-results-cell">
                    <Select
                      v-model="row.disposalResults"
                      @on-change="updateDisposalResults(row)"
                      placeholder="请选择"
                      style="width: 84px;"
                      size="small"
                    >
                      <Option value="已删除">已删除</Option>
                      <Option value="已限流">已限流</Option>
                      <Option value="其他">其他</Option>
                    </Select>
                  </div>
                </div>
              </div>
              </div>
             

            </div>

            <!-- 空状态 -->
            <div v-if="!loading && taskList.length === 0" class="empty-state">
              <div class="empty-content">
                <svg-icon icon-class="NoData" class="empty-icon"></svg-icon>
                <p class="empty-text">暂无数据</p>
              </div>
            </div>
          </div>

          <div class="pagination">
            <Page
              :total="total"
              :current="currentPage"
              :page-size="pageSize"
              show-total
              show-elevator
              @on-change="handlePageChange"
            />
          </div>
        </div>

        <!-- 待审核内容 -->
        <div v-show="activeTab === 1" class="tab-pane">
          <div class="pending-task-card-list" ref="pendingTaskTable">
            <Spin fix v-if="pendingLoading"></Spin>
            <div
              v-for="(row, index) in pendingTaskList"
              :key="row.id"
              class="pending-task-card"
            >
              <!-- 卡片主要内容 -->
              <div class="card-main">
                <div class="left">
                   <a @click="viewTaskDetail(row)" class="task-name" :title="row.name">
                    {{ row.name }}
                  </a>
                  <!-- 信息行 -->
                  <div class="card-info-row">
                    <span class="info-item">
                      <span class="info-label">任务提交时间：</span>
                      <span class="info-value">{{ formatDate(row.issuingTime || row.createTime) }}</span>
                    </span>
                    <span class="info-item">
                      <span class="info-label">申请单位：</span>
                      <span class="info-value">{{ row.promoterOrganName }}</span>
                    </span>
                    <span class="info-item">
                      <span class="info-label">申请人：</span>
                      <span class="info-value">{{ row.promoterName }}</span>
                    </span>
                  </div>
                  <div class="article-count">
                    <span class="count-label">文章数量</span>
                    <span class="count-value">{{ row.articleNum || 0 }}</span>
                  </div>
                </div>
                <div class="right">
                  <!-- 底部操作行 -->
                <div class="card-bottom-row">
                  

                  <div class="action-area">
                    <span class="operation-label">操作：</span>
                    <!-- 审核按钮 -->
                    <template v-if="canOperate(row)">
                      <Button
                        type="primary"
                        size="small"
                        @click="goToAuditPage(row)"
                        class="audit-btn">
                        <svg-icon icon-class="初审1" v-if="row.reviewType === '初审'"/>
                        <svg-icon icon-class="终审2" v-if="row.reviewType !== '初审'"/>
                        {{ row.reviewType === '初审' ? '初审' : '终审' }}
                      </Button>
                      <!-- 删除按钮 -->
                    <Poptip v-if="account.includes(currentAccount)"
                      confirm
                      placement="bottom"
                      width="180"
                      @on-ok="confirmDeleteTask(row)"
                      @on-cancel="cancelDeleteTask">
                      <div slot="title" class="poptip-title">
                        <span>确认删除任务吗？</span>
                      </div>
                      <div slot="content" class="poptip-content">
                        <div class="poptip-buttons">
                          <Button type="default" size="small" @click.stop="$refs['deletePoptip' + row.id].handleCancel()">取消</Button>
                          <Button type="error" size="small" @click.stop="$refs['deletePoptip' + row.id].handleOk()">删除</Button>
                        </div>
                      </div>
                      <Button
                        type="error"
                        size="small"
                        class="delete-btn"
                        ref="deletePoptip{{row.id}}">
                         <svg-icon icon-class="删 除" />
                        删除
                      </Button>
                    </Poptip>
                    </template>
                    <template v-else>
                      <Button
                        disabled
                        size="small"
                        class="disabled-btn">
                         <svg-icon icon-class="初审1" v-if="row.reviewType === '初审'"/>
                          <svg-icon icon-class="终审2" v-if="row.reviewType !== '初审'"/>
                        {{ getDisabledText(row) }}
                      </Button>
                      <Button
                        type="error"
                        size="small"
                        class="disabled-btn"
                          disabled>
                         <svg-icon icon-class="删 除" />
                        删除
                      </Button>
                    </template>
                  </div>
                </div>
                </div>
                <!-- 标题行 -->
              </div>

              <!-- 右上角状态标识 -->
              <div class="status-corner">
                <svg-icon :icon-class="getStatusIcon2(row)" class="status-icon"></svg-icon>
              </div>
              <!-- 序号 -->
              <div class="card-serial">
                 {{ (pendingCurrentPage - 1) * pendingPageSize + index + 1 }}.
                </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!pendingLoading && pendingTaskList.length === 0" class="empty-state">
              <div class="empty-content">
                <svg-icon icon-class="NoData" class="empty-icon"></svg-icon>
                <p class="empty-text">暂无待审核数据</p>
              </div>
            </div>
          </div>

          <div class="pagination">
            <Page
              :total="pendingTotal"
              :current="pendingCurrentPage"
              :page-size="pendingPageSize"
              show-total
              show-elevator
              @on-change="handlePendingPageChange"
            />
          </div>
        </div>
      </div>
    </div>
    </div>
    

    <!-- 右侧筛选 -->
    <TaskFilters @query="handleFilterQuery" :active="activeTab" />

    <Modal
      v-model="disposalResultModalVisible"
      :width="800"
      :closable="false"
    >
      <p
        slot="header"
        style="height: 80px; line-height: 80px; text-align: center;"
      >
        <modalHeader :title="modalTitle" />
      </p>
      <Input
        v-model="disposalResultInput"
        type="textarea"
        :rows="6"
        placeholder="请输入处置结果"
    
      />
      <div slot="footer">
        <div class="foot">
          <span class="inBlock foots" @click="saveDisposalResult">{{ "确定" }}</span>
          <span
            class="inBlock foots"
            style="margin-left: 70px; background: #999999;"
             @click="disposalResultModalVisible = false"
            >{{ "关闭" }}</span
          >
        </div>
      </div>
      <!-- <div style="text-align: center;">
        <Button @click="disposalResultModalVisible = false">关闭</Button>
        <Button type="primary" @click="saveDisposalResult" style="margin-left: 16px;">保存</Button>
      </div> -->
    </Modal>

  </div>
</template>

<script>
import TaskFilters from './components/TaskFilters.vue';
import TaskOverview from './components/TaskOverview.vue';
import modalHeader from "@/views/main/publicOpinionTips/components/modalHeader.vue";
import permissions from "./permissions.json"
export default {
  name: 'TaskList',
  components: {
    TaskFilters,
    TaskOverview,
    modalHeader
  },
  data() {
    return {
      modalTitle: '处置结果',
      tableHeight: 0,
      activeTab: 0, // 0: 已审核, 1: 待审核
      auditedCount: 0,
      pendingCount: 0,
      account:["qianhongguo1","chensu3","chenzhe","weishujie1","dingtong","bijianjun","xupengxiang","test","test1"],
      currentAccount: localStorage.getItem("userAccount"),
      // 审核人员配置
      firstReviewers: permissions.firstReviewers, // 处长（初审）
      secondReviewers: permissions.secondReviewers, // 主任（终审）
      isDataLoading: false, // 标记是否正在加载数据 
      // 已审核任务列表相关
      loading: false,
      taskList: [],
      total: 0,
      currentPage: 1,
      pageSize: 6,
      selectedTasks: [],
      queryParams: {
        dayNum:30
      }, // 查询参数

      // 待审核任务列表相关
      pendingLoading: false,
      pendingTaskList: [],
      pendingTotal: 0,
      pendingCurrentPage: 1,
      pendingPageSize: 6,
      pendingSelectedTasks: [],
      pendingQueryParams: {}, // 待审核查询参数

      // 弹窗相关 - 不再需要Modal状态

      // 表格列定义
      columns: [
        {
          title: '序号',
          slot: 'serialNumber',
          width: 60,
          align: 'center'
        },
        {
          title: '任务名',
          slot: 'taskName',
          key: 'name',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'left'
        },
        {
          title: '文章数量',
          slot: 'articleCount',
          key: 'articleNum',
          width: 80,
          align: 'center'
        },
        {
          title: '完成率',
          slot: 'completionRate',
          key: 'completionRate',
          width: 70,
          align: 'center'
        },
        {
          title: '任务数',
          slot: 'taskCount',
          key: 'taskNumber',
          width: 70,
          align: 'center'
        },
        {
          title: '完成数',
          slot: 'completedCount',
          key: 'completedQuantity',
          width: 90,
          align: 'center'
        },
        {
          title: '任务状态',
          slot: 'status',
          key: 'status',
          width: 90,
          align: 'center'
        },
        {
          title: '下发时间',
          slot: 'issueTime',
          key: 'issuingTime',
          width: 150,
          align: 'center'
        },
        {
          title: '任务发起人',
          slot: 'promoter',
          key: 'promoterName',
          width: 140,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 150,
          align: 'center'
        },
        {
          title: '处理结果',
          slot: 'disposalResults',
          width: 90,
          align: 'center'
        },
        {
          title: '备注',
          slot: 'remark',
          width: 180,
          align: 'center'
        }
      ],

      pendingColumns: [
        {
          title: '序号',
          slot: 'serialNumber',
          width: 60,
          align: 'center'
        },
        {
          title: '任务名',
          slot: 'taskName',
          key: 'name',
          minWidth: 200,
          align: 'left'
        },
        {
          title: '文章数量',
          slot: 'articleCount',
          key: 'articleNum',
          width: 80,
          align: 'center'
        },
        {
          title: '审核状态',
          slot: 'reviewType',
          key: 'reviewType',
          width: 80,
          align: 'center'
        },
        {
          title: '任务提交时间',
          slot: 'issueTime',
          key: 'issuingTime',
          width: 150,
          align: 'center'
        },
        {
          title: '申请单位',
          slot: 'promoter',
          key: 'promoterOrganName',
          width: 120,
          align: 'center'
        },
        {
          title: '申请人',
          slot: 'promoterName',
          key: 'promoterName',
          width: 80,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 130,
          align: 'center'
        }
      ],
      disposalResultModalVisible: false,
      disposalResultInput: '',
      disposalResultRow: null,
    };
  },
  created() {
    // 从URL参数初始化查询参数
    this.initFromUrlParams();

    // 获取任务列表
    this.fetchTaskList();
    this.fetchPendingTaskList();
},
  mounted() {
    this.$nextTick(() => {
      // 设置一个默认高度，避免 this.$refs.taskTable 为 undefined
      this.tableHeight = this.$refs.taskTable ? this.$refs.taskTable.offsetHeight : 500;

      // 监听窗口大小变化，动态调整表格高度
      window.addEventListener('resize', this.updateTableHeight);
    });
  },

  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.updateTableHeight);
  },
  methods: {
    // 获取状态图标
    getStatusIcon(row) {
      // 根据任务状态返回对应的图标
      // 转数字
      let status =Number(row.status)
      console.log(status)
      switch (status) {
        case 0: // 待初审
          return '待初审-三角';
        case 1: // 进行中
          return '进行中-三角';
        case 2: // 未通过
          return '审核未通过-三角';
        case 3: // 已结束
          return '已结束-三角';
        case 4: // 已转发
          return '待终审-三角'; // 使用已结束图标，因为转发后任务也是结束状态
        default:
          return '已结束-三角';
      }
    },

    // 更新表格高度
    updateTableHeight() {
      this.$nextTick(() => {
        // 根据当前激活的标签页选择对应的 ref
        const refName = this.activeTab === 0 ? 'taskTable' : 'pendingTaskTable';
        // 如果 ref 存在则使用其高度，否则使用默认高度
        this.tableHeight = this.$refs[refName] ? this.$refs[refName].offsetHeight : 500;
      });
    },

    // 从URL参数初始化查询参数
    initFromUrlParams() {
      const query = this.$route.query;

      // 如果URL中有参数，使用URL参数
      if (Object.keys(query).length > 0) {
        this.queryParams = { ...query };

        // 处理分页参数
        if (query.pageNo) {
          this.currentPage = parseInt(query.pageNo);
        }

        if (query.pageSize) {
          this.pageSize = parseInt(query.pageSize);
        }

        // 处理标签页参数
        if (query.tab === '1') {
          this.activeTab = 1;
          //如果没有权限
          if(!this.firstReviewers.includes(this.currentAccount) && !this.secondReviewers.includes(this.currentAccount)){
            this.activeTab = 0;
            this.$router.push('/main/jointDisposal/taskList');
          }
        }
      }
    },

    // 更新URL参数
    updateUrlParams() {
      // 构建新的URL参数
      const urlParams = new URLSearchParams();

      // 添加查询参数
      for (const key in this.queryParams) {
        if (this.queryParams[key]) {
          urlParams.set(key, this.queryParams[key]);
        }
      }

      // 添加分页参数
      urlParams.set('pageNo', this.currentPage.toString());
      urlParams.set('pageSize', this.pageSize.toString());

      // 添加标签页参数
      urlParams.set('tab', this.activeTab.toString());

      // 使用History API更新URL，不触发路由变化
      const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
      window.history.replaceState(null, '', newUrl);
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(Number(timestamp));
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${month}-${day} ${hours}:${minutes}`;
    },

    // 切换标签页
    switchTab(tabIndex) {
      this.activeTab = tabIndex;

      // 更新URL参数
      this.updateUrlParams();

      if (tabIndex === 0) {
        //已审核
        this.fetchTaskList();
      } else {
        // 待审核
        this.fetchPendingTaskList();
      }

      // 切换标签页后滚动到顶部并更新表格高度
      this.$nextTick(() => {
        this.scrollToTop();
        this.updateTableHeight();
      });
    },

    // 滚动到顶部
    scrollToTop() {
      // 滚动已审核列表到顶部
      if (this.$refs.taskTable) {
        this.$refs.taskTable.scrollTop = 0;
      }

      // 滚动待审核列表到顶部
      if (this.$refs.pendingTaskTable) {
        this.$refs.pendingTaskTable.scrollTop = 0;
      }

      // 滚动整个页面到顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    },

    // 创建新任务
    createNewTask() {
      this.$router.push('/main/jointDisposal/createTask');
    },

    // 查看任务详情
    viewTaskDetail(task) {
      this.$router.push(`/main/jointDisposal/taskDetail?id=${task.id}&status=${task.status}`);
    },

    // 处理筛选查询
    handleFilterQuery(params) {
      // 更新查询参数
      this.queryParams = { ...params };

      // 打印查询参数，用于调试
      console.log('筛选查询参数:', this.queryParams);

      // 重置分页
      this.currentPage = 1;

      // 更新URL参数
      this.updateUrlParams();

      // 根据当前标签页获取数据
      if (this.activeTab === 0) {
        this.fetchTaskList();
      } else {
        this.fetchPendingTaskList();
      }
    },

    // 获取已审核任务列表
    fetchTaskList() {
      this.loading = true;
      this.isDataLoading = true; // 设置数据加载标志

      const params = {
        ...this.queryParams,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      };

      // 如果没有指定状态，默认获取已审核状态
      if (params.status==0) {
        params.status = ''; // 已审核状态：进行中、未通过、已结束
      }

      // 打印最终请求参数，用于调试
      console.log('已审核任务列表请求参数:', params);

      this.$http.get('/linkAge/auditTaskList', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.taskList = res.body.data.list || [];
            this.total = res.body.data.count || 0;

            // 获取已审核任务数量
            this.auditedCount = this.total;
          } else {
            this.$Message.error(res.body.message || '获取任务列表失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取任务列表失败');
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
          // 延迟重置标志，确保数据渲染完成
          this.$nextTick(() => {
            setTimeout(() => {
              this.isDataLoading = false;
            }, 100);
          });
        });
    },

    // 获取待审核任务列表
    fetchPendingTaskList() {
      if(!this.firstReviewers.includes(this.currentAccount) && !this.secondReviewers.includes(this.currentAccount)){
        return;
      }
      this.pendingLoading = true;
      this.isDataLoading = true; // 设置数据加载标志

      // 检查用户是否同时拥有初审和终审权限
      const hasFirstReview = this.firstReviewers.includes(this.currentAccount);
      const hasSecondReview = this.secondReviewers.includes(this.currentAccount);
      const hasBothPermissions = hasFirstReview && hasSecondReview;

      if (hasBothPermissions) {
        // 如果用户同时拥有初审和终审权限，需要获取两种状态的数据
        this.fetchBothReviewTasks();
      } else {
        // 如果用户只有一种权限，按原逻辑处理
        this.fetchSingleReviewTasks();
      }
    },

    // 获取单一审核权限的任务列表
    fetchSingleReviewTasks() {
      // 待审核任务不传递时间相关参数，只传递关键词和申请单位
      const params = {
        pageNo: this.pendingCurrentPage,
        pageSize: this.pendingPageSize
      };

      // 只传递非时间相关的查询参数
      if (this.queryParams.keyWord) {
        params.keyWord = this.queryParams.keyWord;
      }
      if (this.queryParams.organName) {
        params.organName = this.queryParams.organName;
      }

      // 根据当前用户角色决定状态参数
      const hasFirstReview = this.firstReviewers.includes(this.currentAccount);
      const hasSecondReview = this.secondReviewers.includes(this.currentAccount);

      if (hasFirstReview && !hasSecondReview) {
        // 只有初审权限：看状态0和4的数据，用逗号分隔
        params.status = '0,4';
      } else if (!hasFirstReview && hasSecondReview) {
        // 只有终审权限：只看状态4的数据
        params.status = '4';
      } else {
        // 其他用户默认看待审核
        params.status = '0';
      }

      // 打印最终请求参数，用于调试
      console.log('单一权限待审核任务列表请求参数:', params);

      this.$http.get('/linkAge/auditTaskList', { params })
        .then(res => {
          if (res.body.status === 0) {
            // 为返回的数据添加审核类型标识
            this.pendingTaskList = (res.body.data.list || []).map(item => {
              // 根据实际状态判断审核类型
              let reviewType = '审核';
              if (item.status === 0) {
                reviewType = '初审';
              } else if (item.status === 4) {
                reviewType = '终审';
              }
              return {
                ...item,
                reviewType: reviewType
              };
            });

            this.pendingTotal = res.body.data.count || 0;
            this.pendingCount = this.pendingTotal;
          } else {
            this.$Message.error(res.body.message || '获取待审核任务列表失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取待审核任务列表失败');
          console.error(err);
        })
        .finally(() => {
          this.pendingLoading = false;
          // 延迟重置标志，确保数据渲染完成
          this.$nextTick(() => {
            setTimeout(() => {
              this.isDataLoading = false;
            }, 100);
          });
        });
    },

    // 获取双重审核权限的任务列表（初审+终审）
    fetchBothReviewTasks() {
      // 待审核任务不传递时间相关参数，只传递关键词和申请单位
      const params = {
        pageNo: this.pendingCurrentPage,
        pageSize: this.pendingPageSize,
        status: '0,4' // 同时获取状态0和4的数据，用逗号分隔
      };

      // 只传递非时间相关的查询参数
      if (this.queryParams.keyWord) {
        params.keyWord = this.queryParams.keyWord;
      }
      if (this.queryParams.organName) {
        params.organName = this.queryParams.organName;
      }

      console.log('双重权限待审核任务列表请求参数:', params);

      this.$http.get('/linkAge/auditTaskList', { params })
        .then(res => {
          if (res.body.status === 0) {
            // 为返回的数据添加审核类型标识
            this.pendingTaskList = (res.body.data.list || []).map(item => {
              // 根据实际状态判断审核类型
              let reviewType = '审核';
              if (item.status === 0) {
                reviewType = '初审';
              } else if (item.status === 4) {
                reviewType = '终审';
              }
              return {
                ...item,
                reviewType: reviewType
              };
            });

            // 按创建时间排序（最新的在前）
            this.pendingTaskList.sort((a, b) => {
              const timeA = new Date(a.createTime || a.issuingTime);
              const timeB = new Date(b.createTime || b.issuingTime);
              return timeB - timeA;
            });

            this.pendingTotal = res.body.data.count || 0;
            this.pendingCount = this.pendingTotal;

            console.log('双重权限待审核任务列表:', {
              total: this.pendingTotal,
              currentPage: this.pendingCurrentPage,
              pageSize: this.pendingPageSize,
              displayList: this.pendingTaskList
            });
          } else {
            this.$Message.error(res.body.message || '获取待审核任务列表失败');
          }
        })
        .catch(err => {
          this.$Message.error('获取待审核任务列表失败');
          console.error(err);
        })
        .finally(() => {
          this.pendingLoading = false;
          // 延迟重置标志，确保数据渲染完成
          this.$nextTick(() => {
            setTimeout(() => {
              this.isDataLoading = false;
            }, 100);
          });
        });
    },

    // 处理已审核任务列表分页变化
    handlePageChange(page) {
      this.currentPage = page;

      // 更新URL参数
      this.updateUrlParams();

      this.fetchTaskList();
    },

    // 处理待审核任务列表分页变化
    handlePendingPageChange(page) {
      this.pendingCurrentPage = page;

      // 更新URL参数
      this.updateUrlParams();

      this.fetchPendingTaskList();
    },

    // 设置表格行的类名
    rowClassName(_row, index) {
      return (index % 2 === 1) ? 'even-row' : '';
    },

    // 处理完结任务 - 不再需要，直接在Poptip中调用confirmEndTask
    handleEndTask(task) {
      // 保留此方法以便兼容现有代码
      this.confirmEndTask(task);
    },

    // 确认完结任务
    confirmEndTask(task) {
      const params = {
        id: task.id,
        status: 3 // 完结状态
      };

      this.$http.get('/linkAge/taskEnd', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已完结');
            this.fetchTaskList();
            // 刷新任务概览数据
            this.refreshTaskOverview();
          } else {
            this.$Message.error(res.body.message || '完结任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('完结任务失败');
          console.error(err);
        });
    },

    // 取消完结任务
    cancelEndTask() {
      // 不需要做任何操作
    },

    // 处理转发任务 - 不再需要，直接在Poptip中调用confirmForwardTask
    handleForwardTask(task) {
      // 保留此方法以便兼容现有代码
      this.confirmForwardTask(task);
    },

    // 确认转发任务
    confirmForwardTask(task) {
      const params = {
        id: task.id,
        status: 4 // 设置为已转发
      };

      this.$http.get('/linkAge/taskEnd', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已转发至山东通');
            // 更新本地数据，避免重新请求
            const index = this.taskList.findIndex(item => item.id === task.id);
            if (index !== -1) {
              this.taskList[index].isForward = 1;
              // 使用Vue.set确保视图更新
              this.$set(this.taskList, index, {...this.taskList[index]});
            } else {
              // 如果找不到，则重新获取列表
              this.fetchTaskList();
            }
            // 刷新任务概览数据
            this.refreshTaskOverview();
          } else {
            this.$Message.error(res.body.message || '转发任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('转发任务失败');
          console.error(err);
        });
    },

    // 取消转发任务
    cancelForwardTask() {
      // 不需要做任何操作
    },

    // 处理审核通过任务
    handleApproveTask(task) {
      const params = {
        id: task.id,
        status: 1 // 审核通过状态
      };

      this.$http.get('/linkAge/auditTask', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已审核通过');
            this.fetchPendingTaskList();
            this.fetchTaskList();
            // 刷新任务概览数据
            this.refreshTaskOverview();
          } else {
            this.$Message.error(res.body.message || '审核任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('审核任务失败');
          console.error(err);
        });
    },

    // 处理驳回任务
    handleRejectTask(task) {
      const params = {
        id: task.id,
        status: 2 // 驳回状态
      };

      this.$http.get('/linkAge/auditTask', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务已驳回');
            this.fetchPendingTaskList();
            this.fetchTaskList();
            // 刷新任务概览数据
            this.refreshTaskOverview();
          } else {
            this.$Message.error(res.body.message || '驳回任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('驳回任务失败');
          console.error(err);
        });
    },

    // 跳转到审核页面
    goToAuditPage(row) {
      const routeData = this.$router.resolve({
        path: '/main/jointDisposal/taskAudit',
        query: { id: row.id }
      });
      window.open(routeData.href, '_blank');
    },

    // 获取审核状态样式类
    getReviewStatusClass(row) {
      if (row.reviewType === '初审') {
        return 'first-review';
      } else if (row.reviewType === '终审') {
        return 'final-review';
      }
      return 'default-review';
    },

    // 获取审核状态文本
    getReviewStatusText(row) {
      if (row.reviewType === '初审') {
        return '初审';
      } else if (row.reviewType === '终审') {
        return '终审';
      }
      return '审核';
    },

    // 判断是否可以操作（审核）
    canOperate(row) {
      // 状态为0的可以进行初审操作
      if (row.status === 0 && row.reviewType === '初审') {
        return this.firstReviewers.includes(this.currentAccount);
      }
      // 状态为4的可以进行终审操作
      if (row.status === 4 && row.reviewType === '终审') {
        return this.secondReviewers.includes(this.currentAccount);
      }
      return false;
    },

    // 获取禁用状态文本
    getDisabledText(row) {
      if (row.status === 4 && row.reviewType === '终审') {
        return '终审';
      }
      return '无法操作';
    },

    // // 获取状态图标
    getStatusIcon2(row) {
      if (row.reviewType === '初审') {
        return '待初审-三角'; // 初审状态图标
      } else if (row.reviewType === '终审') {
        return '待终审-三角'; // 终审状态图标
      }
      return '待初审-三角'; // 默认图标
    },

    showDisposalResultModal(row) {
      this.disposalResultRow = row;
      this.disposalResultInput = row.disposalResults || '';
      this.disposalResultModalVisible = true;
    },
    saveDisposalResult() {
      this.$http.get('/linkAge/taskResult', {
        params: {
          id: this.disposalResultRow.id,
          disposalResults: this.disposalResultInput
        }
      }).then(res => {
        if (res.data && res.data.status === 0) {
          this.$Message.success('保存成功');
          this.disposalResultRow.disposalResults = this.disposalResultInput;
          this.disposalResultModalVisible = false;
        } else {
          this.$Message.error(res.data?.message || '保存失败');
        }
      });
    },

    // 更新处理结果
    updateDisposalResults(row) {
      // 如果正在加载数据，不执行更新操作
      if (this.isDataLoading) {
        console.log('数据加载中，跳过处理结果更新');
        return;
      }

      console.log('用户手动更新处理结果:', row.id, row.disposalResults);

      const params = {
        id: row.id,
        disposalResults: row.disposalResults
      };

      this.$http.get('/linkAge/taskResult', { params })
        .then(res => {
          if (res.body && res.body.status === 0) {
            this.$Message.success('处理结果更新成功');
            // 更新成功后，刷新任务概览组件的数据
            this.refreshTaskOverview();
          } else {
            this.$Message.error(res.body?.message || '更新失败');
            // 如果更新失败，恢复原值
            this.$nextTick(() => {
              row.disposalResults = '';
            });
          }
        })
        .catch(err => {
          this.$Message.error('更新失败');
          console.error(err);
          // 如果更新失败，恢复原值
          this.$nextTick(() => {
            row.disposalResults = '';
          });
        });
    },

    // 刷新任务概览数据
    refreshTaskOverview() {
      // 通过ref调用TaskOverview组件的刷新方法
      if (this.$refs.taskOverview) {
        this.$refs.taskOverview.fetchData();
      }
    },

    // 确认删除任务
    confirmDeleteTask(task) {
      const params = {
        id: task.id
      };

      this.$http.get('/linkAge/deleteTask', { params })
        .then(res => {
          if (res.body.status === 0) {
            this.$Message.success('任务删除成功');
            // 刷新当前列表
            if (this.activeTab === 0) {
              this.fetchTaskList();
            } else {
              this.fetchPendingTaskList();
            }
            // 刷新任务概览数据
            this.refreshTaskOverview();
          } else {
            this.$Message.error(res.body.message || '删除任务失败');
          }
        })
        .catch(err => {
          this.$Message.error('删除任务失败');
          console.error(err);
        });
    },

    // 取消删除任务
    cancelDeleteTask() {
      // 不需要做任何操作
    }
  }
};
</script>

<style lang="less" scoped>
// /deep/.ivu-modal-header .ivu-modal-header-inner{
//   height:60px;
//   line-height:60px;
// }
.foot {
  text-align: center;
  margin-bottom: 18px;
  margin-top: 20px;

  .foots {
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
  }
}

.inBlock {
  display: inline-block;
}
/deep/ .ivu-modal{
  top:50%;
  margin-top: ~'calc(-260px / 2)';
}
/deep/ .ivu-table-cell{
  font-size:14px;
  padding:0 5px;
}
/deep/.ivu-poptip-footer{
  padding:0px 20px 15px;
  border-top:0;
  button.ivu-btn{
    height:24px;
    font-size:14px;
    padding-top:0;
    padding-bottom:0;
  }
}
/deep/.ivu-poptip-confirm .ivu-poptip-body-message{
  padding-left:0;
}
/deep/.ivu-table-wrapper{
  overflow: visible!important;
}
/deep/.ivu-table-default{
  overflow: visible;
}
/deep/.ivu-table{
  overflow: visible;
}
/deep/.ivu-table-body table{
  width:100%!important
}
/deep/.ivu-table-header table{
  width:100%!important
}
/deep/.ivu-table-cell{
  padding:0 10px;
}
.ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: inline-block;
}

// 任务名称样式 - 支持两行显示
.task-name-link {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 2.8em; /* 2行的高度 */
  word-break: break-all;
  color: #2d8cf0;
  cursor: pointer;
  text-decoration: none;
  white-space:break-spaces;

  &:hover {
    text-decoration: underline;
  }
}

// 卡片列表样式
.task-card-list {
  flex:1;
  padding-right:10px;
  overflow-y: scroll;

  .task-card {
    
    background: #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.16);
    margin-bottom: 16px;
    padding:12px 20px;
    transition: all 0.3s ease;
    display:flex;
    flex-direction: row;
    position: relative;
    border-radius:8px;
    &:hover,&.hover{
      z-index: 1;
    }
    .left{
      width:850px;
      max-width:897px;
      padding-left:20px;
    }
    .right{
      flex:1
    }
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: #5482EA;
    }

    &.deleted {
    }
    .card-serial {
        color: #333;
        font-weight: 500;
        margin-right: 8px;
        flex-shrink: 0;
        line-height: 1.4;
        position:absolute;
        top:12px;
        left:12px;
      }
    // 右上角状态标识
    .card-status-corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 55px;
      height: 55px;
      overflow: hidden;

      .status-icon {
        width: 100%;
        height: 100%;
      
      }
    }

    // 卡片头部
    .card-header {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      padding-right: 50px; // 为右上角图标留出空间
      gap: 10px;
      

      .card-title {
        flex: 1;
        
        .task-name-link2{
          font-weight: 500;
          font-size: 18px;
          color: #333333;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          color:#333
        }
      }
    }

    // 任务信息行
    .card-info {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 16px;
      font-size: 14px;

      .info-item {
        display: flex;
        align-items: center;

        .info-label {
          color: #666;
          margin-right: 4px;
        }

        .info-value {
          color: #666;
        }
      }
    }

    // 备注信息
    .card-remark {
      margin-bottom: 16px;
      font-size: 14px;

      .remark-label {
        color: #7E8EB0;
        margin-right: 4px;
      }

      .remark-text {
       color: #7E8EB0;
      }
    }

    // 数据统计区域
    .card-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 0 12px 12px;
      //虚线
      border-bottom: 1px dashed #D3D4D5;

      .stat-item {
        text-align: center;
        flex: 1;

        .stat-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }
      }
    }

    // 操作区域
    .card-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .action-left,
      .action-right {
        display: flex;
        align-items: center;

        .action-label {
          color: #666;
          font-size: 14px;
       
        }

        .action-buttons {
          display: flex;
          gap: 8px;

          .action-btn {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s ease;

            &.end-btn {
              background-color: #19be6b;
              color: white;

              &:hover {
                background-color: #17a85a;
              }
            }

            &.forward-btn {
              background: #FFFFFF;
              border: 1px solid #BCD9F5;
              color:#666;

              &:hover {
                background-color: #BCD9F5;
              }
            }

            &.delete-btn {
              background: #FFFFFF;
              border: 1px solid #FFCCD8;
              color:#666;
              &:hover {
                background-color: #FFCCD8;
              }
            }
          }
        }
      }

      .disposal-results-cell {
        // margin-left: 8px;
      }
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;

    .empty-content {
      text-align: center;

      .empty-icon {
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        color: #999;
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

// 待审核卡片列表样式
.pending-task-card-list {
  padding-right:10px;
  flex:1;
  overflow-y: scroll;
  .pending-task-card {
    background: #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
    margin-bottom: 16px;
    padding: 12px 20px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: row;
    position: relative;
    border-radius: 8px;

    &:hover,&.hover{
      z-index: 1;
    }
    .card-serial {
        color: #333;
        font-weight: 500;
        margin-right: 8px;
        flex-shrink: 0;
        line-height: 1.4;
        position:absolute;
        top:12px;
        left:12px;
      }
    // 卡片主要内容
    .card-main {
      width: 100%;
      display:flex;
      flex-direction: row;
      .left{
       
        position: relative;
        padding-left:20px;
        padding-right:90px;
        box-sizing: border-box;
        display: flex;
        flex: 1;
        flex-direction: column;
      }
      .right{
  
        border-left:1px dashed #D3D4D5;
        min-width:250px;
      }

      .task-name {
          font-weight: 500;
          font-size: 18px;
          line-height: 1.3em;
          color: #333333;

          cursor: pointer;
          height:1.3em;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom:12px;
          display: block;
        }
     
      .article-count {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        position: absolute;
        right:0;
        width:90px;
        top:10px;
        height:58px;

        .count-label {
          color: #666;
          font-size:14px;
          height:14px;
        }

        .count-value {
          flex: 1;
          font-weight: 500;
          color: #333;
          font-size:18px;
        }
      }

      // 信息行
      .card-info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 12px;
        font-size: 14px;
        color: #666;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            margin-right: 4px;
          }

          .info-value {
            color: #666;
          }
        }
      }

      // 底部操作行
      .card-bottom-row {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height:100%;
        

        .action-area {
          display: flex;
          align-items: center;
          gap: 8px;

          .operation-label {
            color: #666;
            font-size: 14px;
          }

          .audit-btn {
            min-width: 60px;
          }
          /deep/.ivu-btn-primary{
                background: #FFFFFF;
                border: 1px solid #BCD9F5;
                color: #666;
          }
          /deep/.ivu-btn-error{
            background: #FFFFFF;
        border: 1px solid #FFCCD8;
        color: #666;
          }

          .disabled-btn {
            min-width: 60px;
            color: #999 !important;
            border-color: #ddd !important;
            background-color: #f5f5f5 !important;
            filter:grayscale()
          }

         
        }
      }
    }

    // 右上角状态标识
    .status-corner {
      position: absolute;
      top: 0;
      right: 0;
      width:55px;
      height:55px;
     .status-icon {
        width: 100%;
        height: 100%;
      
      }
    }


  }

  // 空状态
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;

    .empty-content {
      text-align: center;

      .empty-icon {
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        color: #999;
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

.task-list {
  height: 100%;
  display: flex;

  .tab-container {
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    margin-right: 16px;
    height:100px;
    .header {
      margin: 0px 0px;
      padding:0 16px;
      border-bottom: 1px solid #F2F2F2;;
      align-items: center;


      .Toggle {
        position: relative;

        .item {
          padding: 20px 0px;
          font-size: 16px;
          cursor: pointer;
          position: relative;
          align-items: center;
          margin-right: 40px;

          &.active {
            color: #5482EA;
            font-weight: 500;

            .triangle {
              position: absolute;
              bottom: -1px;
              right:0;
              height: 3px;
              background: #5482EA;;
              left:0;
              
            }
          }
        }
      }

      .right-controls {
        display: flex;
        align-items: center;
      }
    }

    .tab-content {
      flex: 1;
      
      display: block;
      height:300px;
      .tab-pane {
        padding: 16px 0px 16px 16px;
        flex:1;
        display: flex;
        flex-direction: column;
        height:100%;
        .task-table {
          margin-bottom: 16px;
          flex: 1;
        }

        .pagination {
      
          display: flex;
          justify-content: center;
          margin-top: 16px;
        }
      }
    }
  }

  .action-buttons {
    button + button {
      margin-left: 8px;
    }

    .audit-link {
      color: #2d8cf0;
      cursor: pointer;
      padding:0 5px;
      &:hover {
        text-decoration: underline;
      }
    }

    .forwarded-text {
      color: #808695; /* 灰色文本，表示已完成的操作 */
      padding:0 5px;
    }
  }
}

// 全局样式
:deep(.ivu-table-wrapper) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.ivu-table th) {
  background-color: #f8f8f9;
  font-weight: 500;
}

:deep(.ivu-table-row) {
  td {
    // height: 60px; // 增加行高以适应两行文本
    // vertical-align: top; // 文本顶部对齐
    // padding-top: 8px;
    // padding-bottom: 8px;
  }
}

:deep(.ivu-tag) {
  border-radius: 2px;
  padding: 0 6px;
  height: 22px;
  line-height: 20px;
}

:deep(.ivu-btn-small) {
  padding: 4px 15px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.ivu-poptip .ivu-btn-primary) {
  background-color: #2d8cf0;
}

:deep(.ivu-poptip .ivu-btn-default) {
  border-color: #dcdee2;
  color: #515a6e;
}

// 状态点样式
.status-dot-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
  display: inline-block;

  &.blue {
    background-color: #2d8cf0;
  }

  &.red {
    background-color: #ed4014;
  }

  &.gray {
    background-color: #808695;
  }

  &.default {
    background-color: #c5c8ce;
  }
}

// 双数行背景色
:deep(.even-row td) {
  background-color: #F4F3F6 !important;
}

// Poptip 样式
:deep(.ivu-poptip-body) {
  padding: 0;
}

:deep(.ivu-poptip-body-content) {
  padding: 0;
}

:deep(.ivu-poptip-popper) {
  min-width: 200px;
}

.poptip-title {
  display: flex;
  align-items: left;
  font-size: 14px;
  font-weight: bold;
  padding: 10px 15px;
  background-color: #fff;
  color: #333;
  position: relative;
  padding-left:40px;
  position: relative;

}

.poptip-title:before {
  content: "!";
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #ff9900;
  color: white;
  font-weight: bold;
  margin-right: 10px;
  position: absolute;
  top:13px;
  left:20px;
}

.poptip-title span {
  flex: 1;
  word-break: break-all;
  white-space: normal;
  text-align: left;
}

.poptip-content {
  padding: 10px;
}

.poptip-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.action-btn {
  color: #2d8cf0;
  cursor: pointer;
  text-decoration: none;
  // padding: 0 5px;
  &:hover {
    text-decoration: underline;
  }
}

.end-btn, .forward-btn {
  color: #2d8cf0;
}

.delete-btn {
  color: #ed4014;
  // margin-left: 8px;
}

.action-btn.disposal-btn {
  color: #2d8cf0 !important;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

/deep/.ivu-tooltip-inner{
  max-width: 400px;
  white-space: normal;
}

// 审核类型标签样式
.review-type-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;

  &.first-review {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }

  &.final-review {
    background-color: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffbb96;
  }
}

// 处理结果和备注列样式
.disposal-results-cell {
  .ivu-select {
    width: 100%;
  }

  /deep/.ivu-select-selection {
    border-radius: 4px;
    background: #F4F9FD;
    border: 1px solid #BCD9F5;
    color:#666;
    &:hover {
      border-color: #57a3f3;
    }
  }

  .ivu-select-focused .ivu-select-selection {
    border-color: #57a3f3;
    box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  }
}


.remark-cell {
  .remark-text {
    display: inline-block;
    max-width: 100%;
    cursor: pointer;
    color: #515a6e;

    &:hover {
      color: #2d8cf0;
    }
  }
}
.cont_left{
  width:1370px;
  display: flex;
  flex-direction: column;
  height: 100%;
  height:~"calc(100vh - 40px)"
}
.task-list .rwgl{
  min-height:317px;
  background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    margin-right: 16px;
    margin-bottom:15px;
    padding:15px;
    .tit{
      font-size: 20px;
      color: #5D5D5D;
      line-height: 20px;
      text-align: left;
      font-weight: 500;
      display: flex;
      gap: 5px;
      justify-items: center;
      align-items: center;
      margin-bottom:20px;
      &::before{
        content: "";
        display:block;
        width: 6px;
        height: 15px;
        background: #5585EC;
      }
    }
}
</style>
