<template>
  <div class="task-overview">
    <div class="overview-content">
      <!-- 左侧任务状态统计 -->
      <div class="status-stats">
        <div class="stat-card total">
          <div class="stat-icon">
            <svg-icon icon-class="任务总数-圆圈" />
          </div>
          <div class="stat-content">
            <div class="stat-label">任务总数</div>
            <div class="stat-value">{{ statusStats.total || 0 }}</div>
          </div>
          <!-- 半透明背景图标 -->
          <div class="stat-bg-icon">
            <svg-icon icon-class="任务总数" />
          </div>
        </div>

        <div class="stat-card progress">
          <div class="stat-icon">
            <svg-icon icon-class="进行中任务-圆圈" />
          </div>
          <div class="stat-content">
            <div class="stat-label">进行中任务</div>
            <div class="stat-value">{{ statusStats.inprogress || 0 }}</div>
          </div>
          <!-- 半透明背景图标 -->
          <div class="stat-bg-icon">
            <svg-icon icon-class="进行中的任务" />
          </div>
        </div>

        <div class="stat-card completed">
          <div class="stat-icon">
            <svg-icon icon-class="已结束任务-圆圈" />
          </div>
          <div class="stat-content">
            <div class="stat-label">已结束任务</div>
            <div class="stat-value">{{ statusStats.completed || 0 }}</div>
          </div>
          <!-- 半透明背景图标 -->
          <div class="stat-bg-icon">
            <svg-icon icon-class="已结束任务" />
          </div>
        </div>

        <div class="stat-card rejected">
          <div class="stat-icon">
            <svg-icon icon-class="审核未通过-圆圈" />
          </div>
          <div class="stat-content">
            <div class="stat-label">审核未通过任务</div>
            <div class="stat-value">{{ statusStats.rejected || 0 }}</div>
          </div>
          <!-- 半透明背景图标 -->
          <div class="stat-bg-icon">
            <svg-icon icon-class="审核未通过" />
          </div>
        </div>
      </div>

      <!-- 右侧处置结果统计 -->
      <div class="disposal-stats">
        <div class="disposal-header">
          <div class="disposal-title">任务处置结果</div>
        </div>
        <div class="disposal-content">
          <div class="chart-section">
            <div class="chart-container">
              <div ref="chartContainer" class="echarts-container"></div>
            </div>
          </div>
          <div class="legend-section">
            <div
              v-for="(item, index) in disposalStats"
              :key="index"
              class="legend-item"
            >
              <div
                class="legend-dot"
                :style="{ backgroundColor: getColor(index) }"
              ></div>
              <span class="legend-label">{{ item.groupBy }}</span>
              <span class="legend-value">{{ item.statNum }}个</span>
              <span class="legend-percent">{{ (item.percent || 0).toFixed(1) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'

export default {
  name: 'TaskOverview',
  props: {
    // 接收时间筛选参数
    timeParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      statusStats: {
        total: 0,
        rejected: 0,
        inprogress: 0,
        completed: 0
      },
      disposalStats: [],
      loading: false,
      // 按照设计图配色调整
      colors: ['#F65177', '#5585EC', '#47DDC0', '#96CEB4', '#FFEAA7', '#DDA0DD'],
      chart: null
    }
  },
  computed: {
    disposalTotal() {
      return this.disposalStats.reduce((sum, item) => sum + (item.statNum || 0), 0)
    }
  },
  watch: {
    timeParams: {
      handler() {
        this.fetchData()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.fetchData()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    // 获取统计数据
    async fetchData() {
      this.loading = true
      try {
        await Promise.all([
          this.fetchStatusStats(),
          this.fetchDisposalStats()
        ])
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$Message.error('获取统计数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取任务状态统计
    async fetchStatusStats() {
      try {
        const params = {
          dayNum: this.timeParams.dayNum || -1,
          startTime: this.timeParams.startTime || '2015-07-15',
          endTime: this.timeParams.endTime || '2025-08-13'
        }

        console.log('任务状态统计请求参数:', params)
        const response = await this.$http.get('/linkAge/getStatusStatistics', { params })
        console.log('任务状态统计响应:', response.body)

        if (response.body.status === 0) {
          this.statusStats = response.body.data || {}
        } else {
          console.error('任务状态统计接口错误:', response.body.message)
        }
      } catch (error) {
        console.error('获取任务状态统计失败:', error)
      }
    },
    
    // 获取处置结果统计
    async fetchDisposalStats() {
      try {
        const params = {
          dayNum: this.timeParams.dayNum || -1,
          startTime: this.timeParams.startTime || '2005-02-23 00:00:00',
          endTime: this.timeParams.endTime || '2026-02-23 00:00:00'
        }

        console.log('处置结果统计请求参数:', params)
        const response = await this.$http.get('/linkAge/getDisposalResultStatistics', { params })
        console.log('处置结果统计响应:', response.body)

        if (response.body.status === 0) {
          const rawData = response.body.data || []
          
          // 计算总数
          const total = rawData.reduce((sum, item) => sum + (item.statNum || 0), 0)
          
          // 为每个item添加percent字段
          this.disposalStats = rawData.map(item => ({
            ...item,
            percent: total > 0 ? ((item.statNum || 0) / total * 100) : 0
          }))
          
          console.log('处置结果统计:', this.disposalStats)
          this.$nextTick(() => {
            this.initChart()
          })
        } else {
          console.error('处置结果统计接口错误:', response.body.message)
        }
      } catch (error) {
        console.error('获取处置结果统计失败:', error)
      }
    },
    
    // 初始化ECharts环形图
    initChart() {
      if (!this.$refs.chartContainer || this.disposalStats.length === 0) return

      // 销毁已存在的图表实例
      if (this.chart) {
        this.chart.dispose()
      }

      // 创建新的图表实例
      this.chart = echarts.init(this.$refs.chartContainer)

      // 准备数据
      const chartData = this.disposalStats.map((item, index) => ({
        name: item.groupBy,
        value: item.statNum,
        itemStyle: {
          color: this.getColor(index)
        }
      }))

      // 配置选项
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: `${this.disposalTotal}\n总量`,
            textAlign: 'center',
            fill: '#374151',
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        series: [
          {
            name: '任务处置结果',
            type: 'pie',
            radius: ['50%', '80%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }

      // 设置配置并渲染
      this.chart.setOption(option)
    },
    
    // 获取颜色
    getColor(index) {
      return this.colors[index % this.colors.length]
    }
  }
}
</script>

<style lang="less" scoped>
.task-overview {
  background: #fff;
  border-radius: 12px;

  
  .overview-content {
    display: flex;
    gap: 24px;
    align-items: flex-start;
    
    // 左右各50%布局
    .status-stats {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      max-width: 50%;
      
      .stat-card {
        border-radius: 8px;
        padding: 22px 16px;
        color: white;
        display: flex;
        align-items: center;
        gap: 20px;
        min-height: 80px;
        position: relative;
        overflow: hidden;
       
        
        // 半透明背景图标
        .stat-bg-icon {
          position: absolute;
          right: 0px;
          bottom: 0px;
          height:60px;
          width:51px;
      
          color: white;
      
          svg{
            width:100%;
            height:100%;
          }
        }
        
        &.total {
          background: linear-gradient(135deg,#9BEAFF 0%, #4AB9E9 100%);
        }

        &.progress {
          background: linear-gradient(135deg, #9BAEFF 0%, #4A7FE9 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #FFB79B 0%, #E9624A 100%);
        }

        &.rejected {
          background: linear-gradient(135deg, #9BDBFF 0%, #4AA8E9 100%);
        }
        
        .stat-icon {
         
          
          flex-shrink: 0;

          border-radius: 50%;
          width:64px;
          height:64px;
          display: flex;
          align-items: center;
          justify-content: center;
          svg{
            width:100%;
            height:100%;
          }
        }

        .stat-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;

          .stat-label {
            font-weight: 500;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 22px;
            font-weight: 400;
          }

          .stat-value {
            font-size: 28px;
            color: #FFFFFF;
            line-height: 40px;
          }
        }
      }
    }
    
    .disposal-stats {
      flex: 1;
      background: #F2F6FF;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.16);
      border-radius: 8px;
      padding: 15px 20px 0 20px;
      max-width: 50%;

      .disposal-header {

        .disposal-title {
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;
          color: #333;
          margin: 0;
        }
      }
      
      .disposal-content {
        display: flex;
        align-items: center;
        gap: 40px;
        margin:0 120px 0 70px;

        .chart-section {
          flex-shrink: 0;

          .chart-container {
            position: relative;
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;

            .echarts-container {
              width: 100%;
              height: 100%;
            }
          }
        }
        
        .legend-section {
          flex: 1;
          
          .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            gap: 12px;
            
            .legend-dot {
              width: 16px;
              height: 16px;
              border-radius: 50%;
              flex-shrink: 0;
            }
            
            .legend-label {
              color: #374151;
              font-size: 14px;
              font-weight: 500;
              min-width: 50px;
            }
            
            .legend-value {
              color: #6B7280;
              font-size: 14px;
              margin-left: auto;
            }
            
            .legend-percent {
              color: #9CA3AF;
              font-size: 14px;
              min-width: 50px;
              text-align: right;
            }
          }
        }
      }
    }
  }
}
</style>
