<template>
    <div class="task-detail">
        <div class="task-info-box">
            <div class="task-info">
                <div class="task-row">
                    <div class="task-row-title">
                        <div class="title-left">
                            <svg-icon icon-class="任务信息" style="width:24px;height:24px;margin-right:10px" />
                            <div class="title">任务信息</div>
                        </div>
                        <div class="title-right">
                            <!-- 初审按钮 -->
                            <Button
                                v-if="canFirstReview"
                                type="primary"
                                size="small"
                                @click="goToAuditPage('first')"
                                class="audit-btn">
                                <svg-icon icon-class="初审1"/>
                                初审
                            </Button>
                            <!-- 终审按钮 -->
                            <Button
                                v-if="canSecondReview"
                                type="primary"
                                size="small"
                                @click="goToAuditPage('second')"
                                class="audit-btn">
                                <svg-icon icon-class="终审2"/>
                                终审
                            </Button>
                        </div>
                    </div>
                    <div class="task-row-content task-info-layout">
                        <div class="task-info">
                            <div class="task-row-item">
                                <div class="label">任务名</div>
                                <div class="value">
                                    <span class="bule">{{
                                        taskInfo.name || ""
                                    }}</span>
                                </div>
                            </div>
                            <div class="task-row-item">
                                <div class="label">创建时间</div>
                                <div class="value">
                                    <span>{{
                                        taskInfo.createTime | formatDate
                                    }}</span>
                                </div>
                            </div>
                            <div class="task-row-item">
                                <div class="label">创建人</div>
                                <div class="value">
                                    <span>{{ taskInfo.promoterOrganName ? taskInfo.promoterOrganName + " - " : "" }}{{ taskInfo.promoterName || "" }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="task-info">
                            <div class="task-row-item task-remark">
                                <div class="label">备注</div>
                                <div class="value">
                                    <span class="bule">{{
                                        taskInfo.remark || ""
                                    }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="task-row">
                    <div class="task-row-title" v-if="taskStatus && taskStatus != 0">
                         <svg-icon icon-class="任务审核" style="width:24px;height:24px;margin-right:10px" />
                        <div class="title">任务审核</div>
                    </div>
                    <div class="task-row-content sh">
                        <!-- 新的三卡片布局 -->
                        <div class="audit-cards-container">
                            <!-- 初审卡片 -->
                            <div class="audit-card" v-if="taskInfo.reviewerName">
                                <div class="card-icon initial-review-icon">
                                        <span>初</span>
                                    </div>
                                <div class="card-header">
                                    
                                    <div class="card-title">初审</div>
                                </div>
                                <div class="card-content">
                                    <div class="info-item">
                                        <span class="info-label">初审人:</span>
                                        <span class="info-value">{{ taskInfo.reviewerName || "" }}</span>
                                    </div>
                                    <div class="info-item" v-if="taskInfo.reviewResult!=null">
                                        <span class="info-label">初审结果:</span>
                                        <span class="info-value">
                                            <svg-icon icon-class="通过" style="width:16px;height:16px;margin-right:4px;" v-if="taskInfo.reviewResult == 1" />
                                            <svg-icon icon-class="审核未通过-圆圈" style="width:16px;height:16px;margin-right:4px;" v-else />
                                            {{ taskInfo.reviewResult == 1 ? "通过" : "不通过" }}
                                        </span>
                                    </div>
                                    <div class="info-item" v-if="taskInfo.auditTime">
                                        <span class="info-label">初审时间:</span>
                                        <span class="info-value">{{ taskInfo.auditTime | formatDate }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 终审卡片 -->
                            <div class="audit-card" v-if="taskInfo.secondReviewerName">
                                 <div class="card-icon final-review-icon">
                                        <span>终</span>
                                    </div>
                                <div class="card-header">
                                   
                                    <div class="card-title">终审</div>
                                </div>
                                <div class="card-content">
                                    <div class="info-item">
                                        <span class="info-label">复审人:</span>
                                        <span class="info-value">{{ taskInfo.secondReviewerName || "" }}</span>
                                    </div>
                                    <div class="info-item" v-if="taskInfo.secondReviewResult!=null">
                                        <span class="info-label">复审结果:</span>
                                        <span class="info-value">
                                            <svg-icon icon-class="通过" style="width:16px;height:16px;margin-right:4px;" v-if="taskInfo.secondReviewResult == 1" />
                                            <svg-icon icon-class="审核未通过-圆圈" style="width:16px;height:16px;margin-right:4px;" v-else />
                                            {{ taskInfo.secondReviewResult == 1 ? "通过" : "不通过" }}
                                        </span>
                                    </div>
                                    <div class="info-item" v-if="taskInfo.secondAuditTime">
                                        <span class="info-label">下发时间:</span>
                                        <span class="info-value">{{ taskInfo.secondAuditTime | formatDate }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 任务明细卡片 -->
                            <div class="audit-card" v-if="isTaskApproved">
                                 <div class="card-icon task-detail-icon">
                                        <span>任</span>
                                    </div>
                                <div class="card-header">
                                    <div class="card-title">任务明细</div>
                                </div>
                                <div class="card-content">
                                    <div class="info-item" v-if="taskInfo.taskLevel">
                                        <span class="info-label">任务等级:</span>
                                        <span class="info-value">{{
                                            taskInfo.taskLevel == 1 ? "高" : taskInfo.taskLevel == 2 ? "中" : "低"
                                        }}</span>
                                    </div>
                                    <div class="info-item" v-if="taskInfo.timeType">
                                        <span class="info-label">任务时间:</span>
                                        <span class="info-value">{{
                                            taskInfo.timeType ? timeTypeMapper[taskInfo.timeType] : ""
                                        }}</span>
                                    </div>
                                    <div class="info-item" v-if="taskInfo.endTime">
                                        <span class="info-label">结束时间:</span>
                                        <span class="info-value">{{
                                            taskInfo.endTime | formatDate
                                        }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="task-row">
                    <div class="task-row-title">
                         <svg-icon :icon-class="taskStatus == 1 || taskStatus == 3 ? '任务统计' : '任务信息'" style="width:24px;height:24px;margin-right:10px" />
                        <div class="title">{{ taskStatus == 1 || taskStatus == 3 ? '任务统计' : '任务文章' }}</div>
                    </div>
                    <div class="task-row-content-statistics" v-if="taskStatus == 1 || taskStatus == 3">
                        <div
                            class="task-statistics-item"
                            v-for="(item, index) in articleInfoList"
                            :key="item.id"
                        >
                            <!-- 任务基本信息 -->
                            <div class="task-basic-info" @click="toggleArticleExpand(item)">
                                <div class="task-header">
                                    <div class="task-number">{{ index + 1 }}.</div>
                                    <div class="task-left-content">
                                        <div class="task-title">{{ item.articleTitle }}</div>
                                        <div class="task-meta">
                                            <div class="meta-item">
                                                <span class="meta-label">平台:</span>
                                                <span class="meta-value">{{ item.app }}</span>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">任务状态:</span>
                                                <span class="meta-value">{{ item.status }}</span>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">任务开始时间:</span>
                                                <span class="meta-value">{{ item.startTime || "" }}</span>
                                            </div>
                                        </div>
                                        
                                        <!-- URL链接 -->
                                        <div class="task-url">
                                            <div class="urlbox">
                                                <svg-icon icon-class="链接1" style="width:14px;height:14px;margin-right:5px;"/>
                                                <a :href="item.url" target="_blank" class="url-link">{{ item.url }}</a>
                                                <!-- 二维码图标，hover 展示二维码 -->
                                                <div class="qrcode-container" @mouseenter="generateQRCode(item.url, $event)" @mouseleave="hideQRCode">
                                                    <svg-icon icon-class="二维码" style="width:16px;height:16px;margin-left:8px;cursor:pointer;" class="qrcode-icon"/>
                                                    <div class="qrcode-tooltip" v-show="showQRCode" :style="qrCodePosition">
                                                        <img ref="qrImage" :src="qrCodeImageSrc" alt="二维码" width="120" height="120" v-if="qrCodeImageSrc" />
                                                        <div class="qrcode-loading" v-else>生成中...</div>
                                                        <div class="tooltip-arrow"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="task-right-info">
                                        <div class="account-count">
                                            <span class="count-label">账号数量</span>
                                            <span class="count-value">{{ item.wpyCount || "0" }}</span>
                                        </div>
                                        
                                        <div class="task-actions">
                                            <span class="count-label">操作：</span>
                                            <!-- 取证按钮 -->
                                            <div class="action-btn" @click.stop="GatherEvidence(item)">
                                                <div v-if="item.isEvidence === 0">
                                                    <svg-icon icon-class="重点提示-取证" />
                                                    取证
                                                </div>
                                                <div v-if="item.isEvidence === 1">
                                                    <svg-icon icon-class="重点提示-取证灰色" />
                                                    取证
                                                </div>
                                            </div>
                                            <div class="action-btn" @click.stop="LookEvidence(item)">
                                                <svg-icon icon-class="弹框-查看" />
                                                查看取证
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                

                                
                            </div>
                            <!-- 展开/收起按钮 -->
                            <div class="expand-btn" :class="{'expanded': item.expanded}" @click="toggleArticleExpand(item)">
                                    <span class="btn-text">{{ item.expanded ? '收起' : '展开' }}</span>
                                    <svg-icon v-if="!item.expanded" icon-class="展开" style="width:12px;" />
                                    <svg-icon v-else icon-class="收起" style="width:12px;" />
                                </div>

                            <!-- 展开的队伍执行情况 -->
                            <div class="team-execution-detail" v-if="item.expanded && item.deptList && item.deptList.length > 0">
                                <div class="team-execution-header">
                                    <div class="team-header-item">序号</div>
                                    <div class="team-header-item">队伍</div>
                                    <div class="team-header-item">账号总数：已完成数</div>
                                    <div class="team-header-item">任务完成度</div>
                                    <div class="team-header-item">操作</div>
                                </div>
                                <div
                                    class="team-execution-row"
                                    v-for="(dept, deptIndex) in item.deptList"
                                    :key="dept.deptCode"
                                    :class="{ 'even-row': deptIndex % 2 === 1 }"
                                >
                                    <div class="team-row-item">{{ deptIndex + 1 }}</div>
                                    <div class="team-row-item">{{ dept.deptCodeName }}</div>
                                    <div class="team-row-item">{{ dept.total }} : {{ dept.finished }}</div>
                                    <div class="team-row-item">
                                        <div class="progress-container">
                                            <div class="progress-bar">
                                                <div class="progress-fill" :style="{width: (dept.rate * 100) + '%'}"></div>
                                            </div>
                                            <span class="progress-text">{{ (dept.rate * 100).toFixed(1) }}%</span>
                                        </div>
                                    </div>
                                    <div class="team-row-item">
                                        <a class="detail-link" @click.stop="showTeamDetail(item.articleId, dept.deptCode)">详情</a>
                                    </div>
                                </div>
                            </div>

                            <!-- 加载中状态 -->
                            <div class="team-execution-loading" v-if="item.expanded && item.loading">
                                <Spin>
                                    <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
                                    <div>加载中...</div>
                                </Spin>
                            </div>

                            <!-- 无数据状态 -->
                            <div class="team-execution-empty" v-if="item.expanded && !item.loading && (!item.deptList || item.deptList.length === 0)">
                                <div class="empty-text">暂无队伍执行数据</div>
                            </div>
                        </div>
                    </div>
                    <div class="task-row-content-statistics" v-else>
                        <div
                            class="task-statistics-item"
                            v-for="(item, index) in articleInfoList"
                            :key="item.id"
                        >
                            <!-- 任务基本信息 -->
                            <div class="task-basic-info" @click="toggleArticleExpand(item)">
                                <div class="task-header">
                                    <div class="task-number">{{ index + 1 }}.</div>
                                    <div class="task-left-content">
                                        <div class="task-title">{{ item.title}}</div>
                                        <div class="task-meta">
                                            <div class="meta-item">
                                                <span class="meta-label">平台:</span>
                                                <span class="meta-value">{{  item.platform }}</span>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">任务状态:</span>
                                                <span class="meta-value">{{ item.status  }}</span>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">任务开始时间:</span>
                                                <span class="meta-value">{{ item.startTime || "" }}</span>
                                            </div>
                                        </div>
                                        
                                        <!-- URL链接 -->
                                        <div class="task-url">
                                            <div class="urlbox">
                                                <svg-icon icon-class="链接1" style="width:14px;height:14px;margin-right:5px;"/>
                                                <a :href="item.url" target="_blank" class="url-link">{{ item.url }}</a>
                                                <!-- 二维码图标，hover 展示二维码 -->
                                                <div class="qrcode-container" @mouseenter="generateQRCode(item.url, $event)" @mouseleave="hideQRCode">
                                                    <svg-icon icon-class="二维码" style="width:16px;height:16px;margin-left:8px;cursor:pointer;" class="qrcode-icon"/>
                                                    <div class="qrcode-tooltip" v-show="showQRCode" :style="qrCodePosition">
                                                        <img ref="qrImage" :src="qrCodeImageSrc" alt="二维码" width="120" height="120" v-if="qrCodeImageSrc" />
                                                        <div class="qrcode-loading" v-else>生成中...</div>
                                                        <div class="tooltip-arrow"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="task-right-info">
                                        <div class="account-count">
                                            <span class="count-label">账号数量</span>
                                            <span class="count-value">{{ item.wpyCount || "0" }}</span>
                                        </div>
                                        <!-- <div class="account-count">
                                            <span class="count-label">完成率</span>
                                            <span class="count-value">{{  item.rate || "0" + "%" }}</span>
                                        </div>
                                         -->
                                        <div class="task-actions">
                                            <span class="count-label">操作：</span>
                                            <!-- 取证按钮 -->
                                            <div class="action-btn" @click.stop="GatherEvidence(item)">
                                                <div v-if="item.isEvidence === 0">
                                                    <svg-icon icon-class="重点提示-取证" />
                                                    取证
                                                </div>
                                                <div v-if="item.isEvidence === 1">
                                                    <svg-icon icon-class="重点提示-取证灰色" />
                                                    取证
                                                </div>
                                            </div>
                                            <div class="action-btn" @click.stop="LookEvidence(item)">
                                                <svg-icon icon-class="弹框-查看" />
                                                查看取证
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                

                            </div>
                           

                           


                        </div>
                    </div>
                </div>
                <!-- <div class="task-row">
                    <div class="task-row-title">
                        <div class="line"></div>
                        <div class="title">处置结果</div>
                        <div class="disposal-result-header">
                            <div  v-if="!isEditingDisposalResult"  @click="startEditDisposalResult">
                                <svg-icon
                                    icon-class="编辑 2"
                                    class="edit-icon"
                                    title="编辑"
                                    style="
                                        width: 24px;
                                        height: 24px;
                                        margin-right: 2px;
                                    " />
                            </div>
                            <div v-else @click="saveDisposalResult">
                                <svg-icon 
                                    icon-class="保存"
                                    class="save-icon"
                                    
                                    title="保存"
                                    style="
                                        width: 24px;
                                        height: 24px;
                                        margin-right: 2px;
                                    " />
                            </div>
                                   
                        
                        </div>
                    </div>
                    <div class="disposal-result-section">
                   
                    <div class="disposal-result-content">
                        <Input
                        v-if="isEditingDisposalResult"
                        v-model="disposalResultInput"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入处置结果"
                        />
                        <div v-else class="disposal-result-text">
                        {{ taskInfo.disposalResults || '暂无处置结果' }}
                        </div>
                    </div>
                    </div>
                </div> -->

                <!-- 队伍详情弹窗 -->
                <Modal v-model="showTeamDetailModal" title="队伍执行详情" width="800">
                    <div class="team-detail-modal">
                        <div class="team-detail-header">
                            <div class="team-detail-header-item">序号</div>
                            <div class="team-detail-header-item">姓名</div>
                            <div class="team-detail-header-item">所属队伍</div>
                            <div class="team-detail-header-item">任务状态</div>
                            <div class="team-detail-header-item">完成时间</div>
                            <div class="team-detail-header-item">任务截图</div>
                        </div>
                        <div class="team-detail-content" v-if="!teamDetailLoading && teamDetailList.length > 0">
                            <div
                                class="team-detail-row"
                                v-for="(item, index) in teamDetailList"
                                :key="index"
                                :class="{ 'even-row': index % 2 === 1 }"
                            >
                                <div class="team-detail-item">{{ index + 1 }}</div>
                                <div class="team-detail-item">{{ item.name }}</div>
                                <div class="team-detail-item">{{ item.belongTeam }}</div>
                                <div class="team-detail-item">
                                    <span :class="getStatusClass(item.status)">{{ item.status }}</span>
                                </div>
                                <div class="team-detail-item">{{ item.time || '-' }}</div>
                                <div class="team-detail-item">
                                    <img
                                        v-if="isValidImageUrl(item.thumbUrl)"
                                        :src="item.thumbUrl"
                                        class="thumb-image"
                                        :alt="item.name + '的任务截图'"
                                        @mouseenter="showHoverPreview($event, item.imageUrl || item.thumbUrl)"
                                        @mouseleave="hideHoverPreview"
                                    />
                                    <span v-else>-</span>
                                </div>
                            </div>
                        </div>
                        <div class="team-detail-loading" v-if="teamDetailLoading">
                            <Spin>
                                <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
                                <div>加载中...</div>
                            </Spin>
                        </div>
                        <div class="team-detail-empty" v-if="!teamDetailLoading && teamDetailList.length === 0">
                            <div class="empty-text">暂无队伍执行详情数据</div>
                        </div>
                    </div>
                    <div slot="footer">
                        <Button type="primary" @click="showTeamDetailModal = false">关闭</Button>
                    </div>
                </Modal>

                <!-- 悬停预览 -->
                <div
                    class="hover-preview-container"
                    v-show="hoverPreviewVisible"
                    :style="hoverPreviewStyle"
                >
                    <img
                        :src="hoverPreviewImageUrl"
                        class="hover-preview-image"
                        alt="预览图"
                        @error="hoverPreviewVisible = false"
                    />
                </div>
            </div>
            <div class="status-box">
                <!-- 0-待审核 1-进行中 2-审核未通过 3-已结束 -->
                <svg-icon
                    :icon-class="getStatusIcon(taskStatus)"
                    class="status-icon"
                />
            </div>
        </div>
        <div class="task-log">
            <taskLog />
        </div>
        <ImgPreview
        v-if="ImgPreviewStatus"
        :imgList="imgList"
        @close="ImgPreviewStatus = false"
        />
    </div>
</template>

<script>
import moment from "moment";
import taskLog from "../components/tasklog.vue";
import ImgPreview from "@/components/imgPreview";
import pathList from "@/assets/js/pathList";
import QRCode from "qrcode";

export default {
    name: "TaskDetail",
    data() {
        return {
            pathList,
            taskId: this.$route.query.id,
            taskStatus: this.$route.query.status, // 0-待审核 1-进行中 2-审核未通过 3-已结束
            taskInfo: {},
            articleInfoList: [],
            //   1 1小时内 2 2小时内 3 24小时内 4 48小时内 5 72小时内  6 自定义结束时间
            timeTypeMapper: {
                1: "1小时内",
                2: "2小时内",
                3: "24小时内",
                4: "48小时内",
                5: "72小时内",
                6: "自定义",
            },
            // 队伍详情弹窗相关
            showTeamDetailModal: false,
            teamDetailLoading: false,
            teamDetailList: [],
            currentArticleId: null,
            currentDeptCode: null,

            // 图片预览相关
            showImagePreview: false,
            previewImageUrl: '',

            // 悬停预览相关
            hoverPreviewVisible: false,
            hoverPreviewImageUrl: '',
            hoverPreviewStyle: {
                top: '0px',
                left: '0px'
            },
            isEditingDisposalResult: false,
            disposalResultInput: '',
            ImgPreviewStatus: false,
            imgList: null,

            // 二维码相关
            showQRCode: false,
            qrCodePosition: {
                top: '0px',
                left: '0px'
            },
            qrCodeImageSrc: '', // 二维码图片数据URL

            // 审核权限相关
            currentAccount: localStorage.getItem("userAccount"),
            firstReviewers: ["chensu3", "dingtong", "weishujie1","test"], // 处长（初审）
            secondReviewers: ["qianhongguo1","chensu3","test1"], // 主任（终审）
        };
    },
    components: {
        taskLog,
        ImgPreview
    },
    computed: {
        // 判断任务是否完全通过审核（处长和主任都审核通过）
        isTaskApproved() {
            // 初审通过(reviewResult=1) 且 终审通过(secondReviewResult=1)
            return this.taskInfo.reviewResult === 1 && this.taskInfo.secondReviewResult === 1;
        },

        // 判断是否可以进行初审
        canFirstReview() {
            // 状态为0（待审核）且当前用户有初审权限
            return this.taskStatus == 0 && this.firstReviewers.includes(this.currentAccount);
        },

        // 判断是否可以进行终审
        canSecondReview() {
            // 状态为4（待终审）且当前用户有终审权限
            return this.taskStatus == 4 && this.secondReviewers.includes(this.currentAccount);
        }
    },
    created() {
        this.getTaskInfo();
    },
    methods: {
        // 根据任务状态获取对应的SVG图标
        getStatusIcon(status) {
            // 0-待审核 1-进行中 2-审核未通过 3-已结束
            //转成数字
            status = Number(status);
            switch (status) {
                case 0:
                    return '待初审-圆'; // 待审核
                case 1:
                    return '进行中-圆'; // 进行中
                case 2:
                    return '审核未通过-圆'; // 审核未通过
                case 3:
                    return '已结束-圆'; // 已结束
                case 4:
                    return '待终审-圆'; // 已结束
                default:
                    return '任务总数-圆圈'; // 默认图标
            }
        },
        //取证
        GatherEvidence(item) {
        if (item.isEvidence === 1) {
            this.$Message.warning("信息正在取证中，请稍后查看");
            return false;
        }
       
        let params = {
            mkey: item.mkey ? item.mkey.replace("is_evidence_msg_","") : "", //数据mkey
            type: item.type, //1 涉济，2 涉鲁，3涉政
        };

        this.$http
            .post("/recommend/obtainEvidenceMsg", params, { emulateJSON: true })
            .then((res) => {
            if (res.body.status === 0) {
                this.$Message.warning("信息正在取证中，请稍后查看");
                item.isEvidence = 1;
            } else {
                this.$Message.error(res.body.message);
            }
            });
        },
        // 查看取证
        LookEvidence(item) {
            console.log(item);
            const LookEvidence = () => import("@/components/LookEvidence");
        
            const sys_log_module = this.$route.query.moduleName
            ? decodeURIComponent(this.$route.query.moduleName)
            : this.$route.meta.moduleName;
            console.log("aaa",sys_log_module)
            this.getLog(
            sys_log_module,
            "查看取证/" + item.title,
            item.mkey ? item.mkey.replace("is_evidence_msg_","") : ""
            );
            // 处理下数据
            let data =item;
            data.mkey=item.mkey.replace("is_evidence_msg_","")
        this.$modal.show({
            component: LookEvidence,
            componentProps: {
            data: item,
            that: this,
            pathList: this.pathList,
            },
            componentEvents: {
            // closes: this.tipCancelModel,
            blowUp: this.blowUp,
            },
            title: "查看取证", // 传递标题
            // y: 300,
        });
        },
        //图片放大
        blowUp(imgList) {
        this.imgList = imgList;
        this.ImgPreviewStatus = true;
        },
        // 获取任务信息
        getTaskInfo() {
            let url = "/linkAge/taskInfo";
            if(this.taskStatus == 1 || this.taskStatus == 3){
                url = "/linkAge/taskInfoDetail";
            }
            this.$http
                .get(url, {
                    params: {
                        id: this.taskId,
                    },
                })
                .then((res) => {
                    this.taskInfo = res.body.data.taskInfo;
                    this.articleInfoList = res.body.data.articleInfoList || [];

                    // 初始化每个文章项的展开状态
                    this.articleInfoList.forEach((item, index) => {
                        // this.$set(item, 'expanded', index === 0); // 默认展开第一个任务项
                        this.$set(item, 'loading', false);
                        this.$set(item, 'deptList', []);
                        //设置取证初始化
                        this.$set(item, 'isEvidence', item.isEvidence?item.isEvidence:0);
                        
                        // 如果默认展开，则加载数据
                        if (index === 0 && (this.taskStatus == 1 || this.taskStatus == 3)) {
                            this.loadArticleDeptList(item);
                        }
                    });

                    console.log(this.articleInfoList);
                });
        },

        // 切换文章项的展开/收起状态
        toggleArticleExpand(item) {
            // 只有在进行中或已结束状态下才允许展开和加载数据
            if (this.taskStatus == 1 || this.taskStatus == 3) {
                // 如果当前是收起状态，则展开并加载数据
                if (!item.expanded) {
                    this.$set(item, 'expanded', true);
                    this.loadArticleDeptList(item);
                } else {
                    // 如果当前是展开状态，则收起
                    this.$set(item, 'expanded', false);
                }
            }
            // 在待审核和未通过状态下不执行任何操作
        },

        // 加载文章的部门列表数据
        loadArticleDeptList(item) {
            this.$set(item, 'deptList', []);
            this.$set(item, 'loading', true);

            this.$http.get('/linkAge/getArticleList', {
                params: {
                    articleId: item.articleId
                }
            })
            .then(res => {
                if (res.body.status === 1) {
                    this.$set(item, 'deptList', res.body.body.data || []);
                } else {
                    this.$Message.error(res.body.msg || '获取队伍执行情况失败');
                    this.$set(item, 'deptList', []);
                }
            })
            .catch(err => {
                console.error('获取队伍执行情况失败', err);
                this.$Message.error('获取队伍执行情况失败');
                this.$set(item, 'deptList', []);
            })
            .finally(() => {
                this.$set(item, 'loading', false);
            });
        },

        // 显示队伍详情
        showTeamDetail(articleId, deptCode) {
            this.currentArticleId = articleId;
            this.currentDeptCode = deptCode;
            this.showTeamDetailModal = true;
            this.teamDetailList = [];
            this.loadTeamDetailList();
        },

        // 加载队伍详情列表
        loadTeamDetailList() {
            this.teamDetailLoading = true;

            this.$http.get('/linkAge/getArticleDeptList', {
                params: {
                    articleId: this.currentArticleId,
                    deptCode: this.currentDeptCode
                }
            })
            .then(res => {
                if (res.body.status === 1) {
                    this.teamDetailList = res.body.body.data || [];
                } else {
                    this.$Message.error(res.body.msg || '获取队伍执行详情失败');
                    this.teamDetailList = [];
                }
            })
            .catch(err => {
                console.error('获取队伍执行详情失败', err);
                this.$Message.error('获取队伍执行详情失败');
                this.teamDetailList = [];
            })
            .finally(() => {
                this.teamDetailLoading = false;
            });
        },

        // 获取任务状态的样式类
        getStatusClass(status) {
            if (status === '已完成') return 'status-completed';
            if (status === '未执行') return 'status-pending';
            return '';
        },

        // 显示悬停预览
        showHoverPreview(event, imageUrl) {
            // 如果图片URL无效，不显示预览
            if (!this.isValidImageUrl(imageUrl)) {
                return;
            }

            // 获取缩略图的位置和尺寸（当前未使用）
            // const thumbRect = event.target.getBoundingClientRect();

            // 获取弹窗的位置和尺寸
            const modalRect = document.querySelector('.ivu-modal-content').getBoundingClientRect();

            // 计算预览图片的位置
            // 将预览图片放在弹窗的右侧
            const leftPosition = modalRect.right + 10; // 弹窗右边缘 + 10px间距

            // 计算预览图片的顶部位置，与点击的缩略图垂直对齐
            let topPosition = 130;

            // 确保预览图片不会超出视口顶部
            if (topPosition < 10) {
                topPosition = 10;
            }

            // 确保预览图片不会超出视口底部
            const viewportHeight = window.innerHeight;
            if (topPosition + 400 > viewportHeight - 10) { // 假设预览图片高度最大为400px
                topPosition = viewportHeight - 400 - 10;
            }

            // 设置预览图片的位置和URL
            this.hoverPreviewStyle = {
                top: `${topPosition}px`,
                left: `${leftPosition}px`
            };
            this.hoverPreviewImageUrl = imageUrl;
            this.hoverPreviewVisible = true;
        },

        // 隐藏悬停预览
        hideHoverPreview() {
            this.hoverPreviewVisible = false;
        },

        // 检查图片URL是否有效
        isValidImageUrl(url) {
            // 检查 url 是否为 null 或 undefined
            if (url === null || url === undefined) {
                return false;
            }

            // 检查 url 是否为字符串类型
            if (typeof url !== 'string') {
                return false;
            }

            // 检查 url 是否为空字符串或只包含空格
            if (url.trim() === '') {
                return false;
            }

            // 检查 url 是否包含 'null' 或 'undefined' 字符串
            if (url === 'null' || url === 'undefined' || url.includes('null') || url.includes('undefined')) {
                return false;
            }

            return true;
        },
        startEditDisposalResult() {
            this.isEditingDisposalResult = true;
            this.disposalResultInput = this.taskInfo.disposalResults || '';
        },
        async saveDisposalResult() {
            if (!this.disposalResultInput.trim()) {
                this.$Message.warning('请输入处置结果');
                return;
            }
            const res = await this.$http.get('/linkAge/taskResult', {
                params: {
                    id: this.taskId,
                    disposalResults: this.disposalResultInput
                }
            });
            if (res.data && res.data.status === 0) {
                this.$Message.success('保存成功');
                this.taskInfo.disposalResults = this.disposalResultInput;
                this.isEditingDisposalResult = false;
                if (typeof this.$refs.taskLog?.refresh === 'function') {
                    this.$refs.taskLog.refresh();
                }
            } else {
                this.$Message.error(res.data?.message || '保存失败');
            }
        },

        // 生成二维码
        async generateQRCode(url, event) {
            if (!url) return;

            this.showQRCode = true;
            this.qrCodeImageSrc = ''; // 清空之前的图片

            // 计算二维码显示位置
            const rect = event.target.getBoundingClientRect();
            this.qrCodePosition = {
                top: `${rect.top - 140}px`,
                left: `${rect.left - 50}px`
            };

            try {
                // 使用QRCode库生成二维码图片（Data URL格式）
                const qrCodeDataURL = await QRCode.toDataURL(url, {
                    width: 120,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });

                this.qrCodeImageSrc = qrCodeDataURL;
            } catch (error) {
                console.error('生成二维码失败:', error);
                this.qrCodeImageSrc = ''; // 生成失败时清空图片
            }
        },

        // 隐藏二维码
        hideQRCode() {
            this.showQRCode = false;
            this.qrCodeImageSrc = ''; // 清空图片数据
        },

        // 跳转到审核页面
        goToAuditPage(auditType) {
            // 根据审核类型跳转到对应的审核页面
            this.$router.push({
                path: '/main/jointDisposal/taskAudit',
                query: {
                    id: this.taskId,
                    auditType: auditType // 'first' 或 'second'
                }
            });
        },
    },
    filters: {
        formatDate(date) {
            if (!date) return "";
            return moment(Number(date)).format("YYYY-MM-DD HH:mm:ss");
        },
    },
};
</script>

<style lang="less" scoped>
.task-detail {
    height: 100%;
    overflow: hidden;
    display: flex;
    gap: 20px;
    .task-info-box {
        flex: 1;
        overflow: hidden;
        width: 1350px;
        background-color: #fff;
        border-radius: 4px;
        // padding:0 20px;
        position: relative;
        .status-box {
            position: absolute;
            top: 100px;
            right: 39px;
            width: 80px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;

            .status-icon {
                width: 100%;
                height: 100%;
            }
        }
    }
    .task-info {
        height: 100%;
        overflow-y: auto;
        .task-row {
            
            &:not(:last-child) {
                margin-bottom: 10px;
            }
            .task-row-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 64px;
                margin-bottom: 20px;
                position: relative;
                padding:0 20px;
                &::before{
                    content:"";
                    display:block;
                    height:1px;
                    left:0px;
                    right:0px;
                    bottom:0;
                    position: absolute;
                    background:#F2F2F2;;
                }

                .title-left {
                    display: flex;
                    align-items: center;

                    .title {
                        font-weight: 600;
                        color: #333;
                        font-size: 18px;
                    }
                }

                .title-right {
                    display: flex;
                    gap: 8px;

                    .audit-btn {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        height: 32px;
                        padding: 0 12px;
                        font-size: 14px;
                        border-radius: 4px;
                        background:white;
                        color:#666;
                        .svg-icon {
                            width: 16px;
                            height: 16px;
                        }
                    }
                }
            }
            .task-row-content {
                display: flex;
                flex-wrap: wrap;
                // align-items: center;
                padding:0 20px;
                justify-content: start;
                gap: 20px;
                background:#F6F7FB;
                margin:0 20px;
                padding:20px 16px;
                border-radius:4px;

                // 审核卡片容器的特殊样式
                &.sh {
                    flex-wrap: nowrap; // 审核卡片不换行
                    overflow-x: auto; // 如果内容过多，允许横向滚动
                }

                .task-row-item {
                    width: 30%;
                    flex-shrink: 0;
                    overflow: hidden;
                    display: flex;
                    font-size: 14px;
                    line-height: 20px;
                    color: #333;
                    .blue {
                        color: #6b9ad4;
                    }
                    .value {
                        flex: 1;
                        overflow: hidden;
                    }
                }

                // 任务信息特殊布局
                &.task-info-layout {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;

                    .task-info {
                        padding-bottom:10px;
                        border-bottom:1px dashed #D3D4D5;
                        display: flex;
                        flex-direction: row;
                        gap: 100px;
                        &:last-child{
                            border-bottom:none;
                            padding-bottom:0;
                        }
                        .task-row-item {
                            width:auto;
                            max-width: 100%;

                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;

                            .label {
                                min-width: 80px;
                                font-weight: 500;
                                font-size:14px;
                                line-height:24px;
                                color: #666666;
                            }
                            .value{
                                font-size: 16px;
                                color: rgba(0,0,0,0.85);
                                line-height: 24px;
                                word-break: break-all;

                            }
                        }
                    }

                    
                }
            }
            .task-row-content-statistics {
                display: flex;
                flex-direction: column;
                padding:0 20px;
                gap: 20px;
                margin-bottom:30px;
                // 新的任务统计样式
                .task-statistics-item {
                    width: 100%;
                    background-color: #fff;
                    border-radius: 8px;
                    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.16);
                    overflow: hidden;
                    transition: all 0.3s ease;
                    position: relative;
                    &:hover {
                        box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.16);
                    }

                    .task-basic-info {
                        padding:  12px 12px 12px 0;
                        cursor: pointer;
                        position: relative;
               

                        .task-header {
                            display: flex;
                            align-items: center;
                      
                            padding-left: 40px;
                            position: relative; // 为绝对定位的索引提供参考

                            .task-number {
                                position: absolute; // 绝对定位
                                left: 12px;
                                top: 0;
                                font-size: 16px;
                                font-weight: bold;
                                color: #333;
                                z-index: 1;
                            }

                            .task-title {
                                font-size: 16px;
                                font-weight: bold;
                                color: #333;
                                flex: 1;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                            .task-left-content {
                                flex: 1;
                                display: flex;
                                flex-direction: column;
                                gap: 12px;
                                width:0;

                                .task-meta {
                                    display: flex;
                                    flex-wrap: wrap;
                                    gap: 20px;

                                    .meta-item {
                                        display: flex;
                                        align-items: center;
                                        font-size: 14px;

                                        .meta-label {
                                            color: #666;
                                            margin-right: 8px;
                                            font-weight: 500;
                                        }

                                        .meta-value {
                                            color: #333;
                                            font-weight: 600;
                                        }
                                    }
                                }

                                .task-url {
                                    display: flex;
                                    align-items: center;
                                    .urlbox{
                                        width:~"calc(100% - 20px)";
                                        display: flex;
                                        flex-direction: row;
                                        align-items: center;
                                    }
                                    .url-link {
                                        color: #5482EA;
                                        text-decoration: none;
                                        font-size: 14px;
                                        word-break: break-all;
                                        line-height: 1.4;
                                        display: block;

                                        //一行省略
                                        max-width: 100%;
                                        overflow:hidden;
                                        text-overflow: ellipsis;
                                        white-space: nowrap;
                                        &:hover {
                                            text-decoration: underline;
                                        }
                                    }

                                    // 二维码容器样式
                                    .qrcode-container {
                                        position: relative;
                                        display: inline-block;

                                        .qrcode-icon {
                                            border:1px solid #5482EA;
                                            border-radius: 2px;
                                            color:#5482EA;
                                            padding:1px;
                                            transition: all 0.3s ease;
                                            &:hover {
                                                transform: scale(1.1);
                                            }
                                        }

                                        .qrcode-tooltip {
                                            position: fixed;
                                            z-index: 9999;
                                            background: white;
                                            border: 1px solid #e8e8e8;
                                            border-radius: 8px;
                                            padding: 10px;
                                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                            pointer-events: none;

                                            img {
                                                display: block;
                                                border-radius: 4px;
                                            }

                                            .qrcode-loading {
                                                width: 120px;
                                                height: 120px;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                background-color: #f5f5f5;
                                                border-radius: 4px;
                                                color: #666;
                                                font-size: 12px;
                                            }

                                          
                                        }
                                    }
                                }
                            }
                            .task-right-info {
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                                gap: 34px;
                                min-width: 350px;
                                .count-label {
                                        font-weight: 400;
                                        font-size: 14px;
                                        color: #666666;
                                        line-height: 20px;
                                        margin-bottom:5px;
                                       
                                    }
                                .account-count {
                                    text-align: right;
                                    display: flex;
                                    flex-direction: column;
                                    
                           
                                   
                                    .count-value {
                                        font-weight: 500;
                                        font-size: 18px;
                                        color: #333333;
                                        text-align: center;
                                        line-height: 20px;
                                    }
                                }

                                .task-actions {
                                    display: flex;
                                    flex-direction: row;
                                    gap: 8px;
                                    align-items: center;
                                    padding: 19px 0;
                                    padding-left: 34px;
                                    border-left: 1px dashed #E0E0E0;
                                    .action-btn {
                                        background: #FFFFFF;
                                        border: 1px solid #BCD9F5;
                                        color: #666;
                                        font-size: 14px;
                                        padding:2px 6px;
                                        border-radius: 4px;
                                        &:hover {
                                            background-color: #BCD9F5;
                                           
                                        }

                                        .svg-icon {
                                            width: 16px;
                                            height: 16px;
                                        }
                                    }
                                }
                            }
                        }

                        
                    }
                    .expand-btn {
                            position: absolute;
                            right: 0px;
                            bottom: 0px;
                            background: #5482EA;
                            border-radius: 8px 0px 8px 0px;
                            gap: 4px;
                            width: 64px;
                            height: 24px;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;

                            .btn-text {
                                font-size: 12px;
                                font-weight: 500;
                                color: #FFFFFF;
                            }

                            .svg-icon {
                                width: 16px;
                                height: 16px;
                            }

                            &:hover {
                                background-color: #5378e5;
                                color: #fff;
                            }
                        }
                    
                   

                    .team-execution-loading, .team-execution-empty {
                        padding: 30px 20px;
                        text-align: center;
                        background-color: #f8f9fa;

                        .empty-text {
                            color: #999;
                            font-size: 14px;
                        }
                    }
                }

                // 保留原有的样式作为备用
                .task-row-content-statistics-item {
                    width: 100%;
                    padding: 20px;
                    border-radius: 6px;
                    background-color: #dfe9fc;
                    font-size: 14px;
                    line-height: 20px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        background-color: #d0e0fa;
                    }

                    .statistics-info {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        position: relative;
                        padding-right:30px;
                        .statistics-info-title {
                            font-weight: bold;
                            max-width: 65%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        .statistics-info-value {
                          
                            color: #6b8aea;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            flex:1;
                        }
                        .arrow-icon {
                            position: absolute;
                            right: 0;
                            top: 50%;
                            transform: translateY(-50%);
                            transition: all 0.3s;

                            .ivu-icon {
                                font-size: 24px;
                                color: #5378e5;
                            }

                            &.arrow-down {
                                transform: translateY(-50%) rotate(180deg);
                            }
                        }
                        .bbtn{
                            border:1px solid #5378e5;
                            border-radius:4px;
                            padding:0 5px;
                        }
                    }
                    .statistics-info-remark {
                        margin-top: 10px;
                        display: flex;
                        gap: 20px;
                    }

                    // 队伍执行情况样式
                    .team-execution {
                        margin-top: 20px;

                        padding-top: 15px;
                        background:white;
                        padding:20px;

                        .team-execution-header {
                            display: flex;
                            background-color: #f0f5ff;
                            padding: 10px 0;
                            font-weight: bold;
                            border-radius: 4px;
                            

                            .team-header-item {
                                flex: 1;
                                text-align: center;

                                &:first-child {
                                    flex: 0.5;
                                }

                                &:last-child {
                                    flex: 0.8;
                                }
                            }
                        }

                        .team-execution-row {
                            display: flex;
                            padding: 12px 0;
                            border-bottom: 1px solid #eee;

                            &.even-row {
                                background-color: #F4F3F6;
                            }

                            .team-row-item {
                                flex: 1;
                                text-align: center;

                                &:first-child {
                                    flex: 0.5;
                                }

                                &:last-child {
                                    flex: 0.8;
                                }

                                .detail-link {
                                    color: #2d8cf0;
                                    cursor: pointer;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }
                            }
                        }
                    }

                    .team-execution-loading, .team-execution-empty {
                        margin-top: 20px;
                        padding: 30px 0;
                        text-align: center;
                        border-top: 1px solid #c5d3f3;
                    }

                    .empty-text {
                        color: #999;
                        font-size: 14px;
                    }
                }
            }
        }
    }
    .task-log {
        width: 370px;
        flex-shrink: 0;
        overflow-y: auto;
    }
}

// 队伍详情弹窗样式
.team-detail-modal {
    .team-detail-header {
        display: flex;
        background-color: #f0f5ff;
        padding: 10px 0;
        font-weight: bold;

        .team-detail-header-item {
            flex: 1;
            text-align: center;

            &:first-child {
                flex: 0.5;
            }

            &:last-child {
                flex: 1.5;
            }
        }
    }

    .team-detail-content {
        max-height: 400px;
        overflow-y: auto;

        .team-detail-row {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
            align-items: center;

            &.even-row {
                background-color: #F4F3F6;
            }

            .team-detail-item {
                flex: 1;
                text-align: center;

                &:first-child {
                    flex: 0.5;
                }

                &:last-child {
                    flex: 1.5;
                }

                .status-completed {
                    color: #19be6b;
                }

                .status-pending {
                    color: #ff9900;
                }

                .thumb-image {
                    width: 80px;
                    height: 60px;
                    object-fit: cover;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        transform: scale(1.05);
                    }
                }
            }
        }
    }

    .team-detail-loading, .team-detail-empty {
        padding: 40px 0;
        text-align: center;
    }
}

// 悬停预览样式
.hover-preview-container {
    position: fixed;
    z-index: 1000;
    padding: 10px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    pointer-events: none; // 防止预览图片阻挡鼠标事件
    border: 1px solid #e8e8e8;

    .hover-preview-image {
        max-width: 300px; // 设置最大宽度为300px
        max-height: 400px;
        object-fit: contain;
        display: block; // 确保图片正确显示
        border-radius: 4px;
    }

    // 移除小三角形指示器
}

// 修复demo-spin-icon-load样式
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
    to { transform: rotate(360deg); }
}

.disposal-result-section {

//   border: 1px solid #e8e8e8;
//   border-radius: 6px;
//   padding: 16px;
//   background: #f8faff;
}
.disposal-result-header {
    flex:1;
    
  display: flex;
  align-items: end;
  font-weight: bold;
  font-size: 16px;

  justify-content: end;
  .edit-icon, .save-icon {
    margin-left: 8px;
    color: #2d8cf0;
    cursor: pointer;
    font-size: 20px;
  }
}
.disposal-result-content {
  .disposal-result-text {
    min-height: 80px;
    color: #333;
    font-size: 15px;
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    border: 1px solid #e8e8e8;
    background: #ededed;
  }
}

// 新的审核卡片样式
.audit-cards-container {
  display: flex;
  gap: 150px;
  flex-wrap: nowrap; // 改为不换行，确保水平并排
  width: 100%;

  .audit-card {
    
    min-width: 0; // 移除最小宽度限制

    
    display: flex;
    flex-direction: column;
    align-items: flex-start; // 改为左对齐
    text-align: left; // 改为左对齐
   
    position: relative;
    padding-left:32px;
    .card-icon {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-weight: bold;
        position: absolute;
        top:0;
        left:0;
        
        span {
          font-size: 12px; // 稍微调小字体
          font-weight: bold;
        }
      }

      .initial-review-icon {
        background-color: #FFA522; // 橙色
        border: 3px solid #FCD088; // 红色边框
        span {
          color: #fff;
        }
      }

      .final-review-icon {
        background-color: #FF765C; // 橙色
        border: 3px solid #FCB7AC; // 红色边框
        span {
          color: #fff;
        }
      }

      .task-detail-icon {
        background-color: #4881D2; // 蓝色
        border: 3px solid #A6BFE7; // 蓝色边框
        span {
          color: #fff;
        }
      }
    .card-header {
      display: flex;
      align-items: center;

      width: 100%; // 确保宽度100%

      

      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .card-content {
      width: 100%;

      .info-item {
        display: flex;
        justify-content: start;
        align-items: center;
        font-size: 14px;
        color: #333;
        padding: 4px 0; // 减小内边距
  

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .info-label {
          font-weight: 500;
          color: #666;
          margin-right: 10px;
      
          font-size: 14px;
        }

        .info-value {
          font-weight: 600; // 调整字体粗细
          color: #333;
          text-align: right;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font-size: 14px;
        }
      }
    }
  }
}



.team-execution-detail {
    margin:0 20px 40px 40px;
    background: white;
    font-size:16px;
    .team-execution-header {
        display: flex;
        background: #EAEBF2;
        padding: 9px 0;
        font-weight: bold;
        border-radius: 8px 8px  0 0px;

        .team-header-item {
            flex: 1;
            text-align: center;

            &:first-child {
                flex: 0.5;
            }

            &:last-child {
                flex: 0.8;
            }
        }
    }

    .team-execution-row {
        display: flex;
        padding: 11px 0;
        border: 1px solid #eee;
        border-top:0;
        &:last-child{
            border-radius:0 0 8px 8px  ;
        }
        &.even-row {
            // background-color: #F4F3F6;
        }

        .team-row-item {
            flex: 1;
            text-align: center;

            &:first-child {
                flex: 0.5;
            }

            &:last-child {
                flex: 0.8;
            }

            .progress-container {
                display: flex;
                align-items: center;
                gap: 5px;
                width: 100%;
                justify-content: center;
                .progress-bar {
                    width: 100px;
                    height: 8px;
                    background-color: #e0e0e0;
                    border-radius: 4px;
                    overflow: hidden;
                    .progress-fill {
                        height: 100%;
                        background-color: #9CDC51;
                        border-radius: 4px;
                    }
                }
                .progress-text {
                    font-size: 12px;
                    color: #333;
                }
            }

            .detail-link {
                color: #2d8cf0;
                cursor: pointer;
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}
</style>
