<template>
  <div class="recommended flex sb">
    <div class="main">
      <div class="header flex sb">
        <div class="Toggle flex">
          <!-- @click="typeChange(i.key)" -->
          <div
            v-for="i in typeList"
            :key="i.key"
            :class="['cp', 'flex', 'item', type === i.key ? 'active' : '']"
            @click="typeChange(i.key)"
          >
            {{ i.name + (i.key === "2" ? "(" + markingNum + ")" : "") }}

            <div class="triangle"></div>
          </div>
        </div>
        <div class="Tips flex">
          <!-- <div class="unreadNum">
            全部未读预警信息共 <span style="color: red">5</span> 条
          </div> -->
          <div class="screeningSwitch">
            <Checkbox v-model="single">自动刷新(每15秒自动刷新一次)</Checkbox>
          </div>
        </div>
      </div>
      <div v-if="type == '1'" class="screenType">
        <div
          v-for="item in screenList"
          :key="item.id"
          :class="['item cp', screenId === item.id ? 'active' : '']"
          @click="
            defaultScreen = item.screenJson;
            screenId = item.id;
          "
        >
          {{ item.screenName }}<Icon type="ios-close" @click.stop="delScreen(item.id)"/>
        </div>
      </div>
      <div class="content">
        <div v-if="isRefreshPrompt" class="RefreshPrompt cp" @click="getCount">
          有{{ balanceNum }}条新信息，点击获取
          <Icon type="md-sync" />
        </div>
        <Spin v-show="loading" fix>
          <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
          <div>Loading</div>
        </Spin>
        <no-data v-show="total === 0 && !loading" />
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
          <MonitoringInfoList
            v-for="(item, index) in tableData"
            :key="item.mkey"
            :data="item"
            :index="index"
            :keywordArr="filterData.keyword ? [filterData.keyword] : []"
            :pageNo="pageNo"
            :pageSize="pageSize"
            checkKey="id"
            @drawerChange="openDrawer"
            :editInfoShow="type === '2'"
          >
            <template v-slot:controls>
              <ListControls
                :btnStatus="btnStatus"
                :data="item"
                :delMsg="delMsg"
                @editAbstract="editAbstract"
              />
            </template>
          </MonitoringInfoList>
        </CheckboxGroup>
        <Page
          v-show="total > 0 && !loading"
          :current="pageNo"
          :page-size="pageSize"
          :pageSizeOpts="pageSizeOpts"
          :total="total"
          show-elevator
          show-sizer
          show-total
          style="position: relative; margin: 30px 0"
          @on-change="pageNoChange"
          @on-page-size-change="pageSizeChange"
        />
      </div>
      <div class="footer flex sb">
        <div class="controls flex">
          <Checkbox
            :indeterminate="indeterminate"
            :value="checkAll"
            @click.prevent.native="handleCheckAll"
            >全选
          </Checkbox>
          <div class="btn" @click="territorialPush">
            <svg-icon icon-class="信息库-导出" />
            按归属地推送
          </div>
          <div class="btn" @click="derive">
            <svg-icon icon-class="信息库-导出" />
            批量导出
          </div>
          <div class="btn" @click="batchDetection">
            <svg-icon icon-class="探测-白" />
            批量探测
          </div>
          <Poptip confirm title="确认删除所选中的信息吗?" @on-ok="delMsg">
            <div class="btn">
              <svg-icon icon-class="榜单-批量删除" />
              批量删除
            </div>
          </Poptip>
        </div>
        <div class="screen">
          <Select
            v-model="orderByType"
            style="width: 200px"
            @on-change="orderChange"
          >
            <Option v-for="(v, k) in orderByTypeList" :key="k" :value="k"
              >{{ v }}
            </Option>
          </Select>
          <span>
            共<span style="color: #537be6">{{ total }}</span>条
          </span>
         <span style="cursor: pointer;"  @click="getHasTelData">
            ( <svg-icon icon-class="电话"/><span style="color: #537be6">{{ telTotal }}</span>条 {{ telPercentage }}
          </span>
          <!-- 未建档条数 -->
          <span  @click="getNotArchivedData">
            未建档<span style="color: #537be6">{{ notArchivedTotal }}</span>条 {{ notArchivedPercentage }} )
          </span>
        </div>
      </div>
    </div>
    <Filters
      :defaultScreen="defaultScreen"
      :screenList="screenList"
      :screenType="screenType"
      :type="type"
      @query="query"
       ref="filters"
    />
    <!--    抽屉-->
    <Drawer v-model="drawer" :closable="false" width="750">
      <component :is="componentId" :data="drawerData" />
    </Drawer>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import Filters from "./components/filters.vue";
import ListControls from "@/components/listControls/index.vue";
import MonitoringInfoList from "@/components/monitoringInfoList/index.vue";
import { listData } from "@/views/main/monitor/recommended/components/mock";
import SimilarArticle from "@/components/similarArticle"; //相似文章
import SimilarArticleNew from "./components/similarArticle/index.vue"; //相似文章
import OriginalText from "@/components/originalText"; //预览原文

const orderByTypeList = {
  1: "按发布时间倒序",
  2: "按发布时间正序",
  3: "按报送时间倒序",
  4: "按报送时间正序",
};
const typeList = [
  {
    key: "1",
    name: "推荐关注",
  },
  {
    key: "2",
    name: "待审核",
  },
];
export default {
  data() {
    // 这里存放数据
    return {
      btnStatus: {},
      listData,
      drawer: false, //抽屉开关
      drawerData: null,

      type: "1",
      single: false, //自动刷新开关
      refreshTimeout: null,
      typeList,
      orderByTypeList,

      // 复选框用到的
      indeterminate: false,
      checkAll: false,
      checkAllGroup: [],

      filterData: {}, //筛选条件

      tableData: [],
      loading: false,
      total: 0,
      telTotal:0,
      notArchivedTotal: 0, // 未建档总数
      pageNo: 1,
      pageSize: 50,

      urlSuffix: 1, //url后缀

      componentId: null,

      orderByType: "3",

      screenList: [],
      screenType: 1,
      screenId: null,
      defaultScreen: "",

      isRefreshPrompt: false, //刷新提示

      balanceNum: 0, //新旧总数差额

      markingNum: 0, //打标数量
      pageSizeOpts: [10, 50, 100],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    MonitoringInfoList,
    ListControls,
    Filters,
    SimilarArticle,
    OriginalText,
    SimilarArticleNew,
  },
  computed: {
    // 计算有电话数量的百分比
    telPercentage() {
      if (this.total === 0 || this.telTotal === 0) {
        return '';
      }
      const percentage = (this.telTotal / this.total * 100);
      return percentage.toFixed(1)+"%";
    },
    // 计算未建档数量的百分比
    notArchivedPercentage() {
      if (this.total === 0 || this.notArchivedTotal === 0) {
        return '';
      }
      const percentage = (this.notArchivedTotal / this.total * 100);
      return percentage.toFixed(1)+"%";
    }
  },
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
  delScreen(d) {
    let params = {
      id:d
    }
    this.$http.post("/recommend/screen/delete",  params, { emulateJSON: true } ).then((res) => {
      if (res.body.status === 0) {
        this.$Message.success("删除成功");
        this.getScreenList();
      }
    });

  },
    editAbstract(key, data) {
      console.log(key, data);
    },
    //按属地推送
    territorialPush() {
      if (this.checkAllGroup.length === 0) {
        return this.$Message.warning("请选择信息后重试！");
      }
      let params = {
        ids: this.checkAllGroup.toString(),
      };
      this.$http.get("/recommend/pushArea", { params }).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success("推送成功");
          this.getCount();
        }
      });
    },
    // 批量探测
    batchDetection() {
      if (this.checkAllGroup.length === 0) {
        return this.$Message.warning("请选择信息后重试！");
      }
      let params = {
        "mkeys": this.tableData.reduce((pre, cur) => {
        return this.checkAllGroup.includes(cur.id) ? pre.push(cur.mkey) && pre : pre
      }, []),
        "detectionFlag": 1,
        "cycle": 1,
        "times": 24,
        "isImportant": 1
      };
      this.$http.post("/message/detection/list", params).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success("探测已启动，稍后刷新页面查看结果");
          this.getCount();
        }else{
          this.$Message.error(res.body.message);
        }
      }).catch((err) => {
        this.$Message.error(err.body.message);
      });
    },
    getScreenList() {
      let params = {
        screenType: this.screenType,
      };
      this.$http
        .get("/recommend/screen/getScreenList", { params })
        .then((res) => {
          console.log(res);
          this.screenList = res.body.data;
        });
    },
    orderChange() {
      console.log("asda");
      this.getListData();
    },

    typeChange(d) {
      this.type = d;
      // 涉及推荐
      if(d == '1'){
        if (this.$route.path === "/main/monitor/recommended") {
          this.$route.meta.moduleName = '涉济监测/涉济推荐-推荐关注'
          this.getLog("涉济监测/涉济推荐-推荐关注");
        }
      }else if(d == '2'){
        if (this.$route.path === "/main/monitor/recommended") {
          this.$route.meta.moduleName = '涉济监测/涉济推荐-推荐关注（待审核）'
          this.getLog("涉济监测/涉济推荐-推荐关注（待审核）");
        }
      }
    },
    //导出
    derive() {
      if (this.checkAllGroup.length === 0) {
        return this.$Message.warning("请选择信息后重试！");
      }
      let params = {
        ids: this.checkAllGroup.toString(),
        orderByType: this.orderByType,
      };
      this.$http
        .post("/recommend/exportMsg", params, {
          emulateJSON: true,
          responseType: "blob",
        })
        .then((res) => {
          const disposition = res.headers.get("Content-Disposition");
          let filename = "downloaded_file";
          if (disposition && disposition.indexOf("attachment") !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
              filename = matches[1].replace(/['"]/g, "");
              filename = decodeURIComponent(escape(filename)); // 解码文件名
            }
          }
          const blob = new Blob([res.body]);
          const a = document.createElement("a");
          a.href = URL.createObjectURL(blob);
          a.download = filename;
          document.body.appendChild(a);
          a.click();

          setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(a.href);
          }, 0);
        });
    }, //打开右侧抽屉  d：当前信息数据，type：类型1相似文章2信息原文预览
    openDrawer(d, type) {
      this.drawer = true;
      this.drawerData = d;
      this.componentId = type === 1 ? "SimilarArticleNew" : "OriginalText";
    },
    // 删除
    delMsg(d) {
      let params;
      if (d) {
        params = {
          ids: d.id,
        };
      } else {
        if (this.checkAllGroup.length === 0) {
          return this.$Message.warning("请选择信息后重试！");
        } else {
          params = {
            ids: this.checkAllGroup.toString(),
          };
        }
      }
      this.$http
        .post("/recommend/delMsg", params, { emulateJSON: true })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
            this.getCount();
          }
        });
    },
    async setBtnStatus(data) {
      let obj;
      // 涉济推荐
      if (this.$route.path === "/main/monitor/recommended") {
        this.urlSuffix = 1;
        this.screenType = 1;

        obj = {
          //提示单按钮
          handelTips: false,
          //生成摘要
          createAbstract: false,
          //创建事件按钮
          createEvent: true,
          //加入素材库按钮
          addMaterial: false,
          // 复制链接
          copyURL: true,
          //删除事件
          delMsg: true,
          //取证
          GatherEvidence: true,
          // 查看取证
          LookEvidence: true,
        };
      }
      // 涉鲁推荐
      if (this.$route.path === "/main/shandongMonitor/recommended") {
        this.urlSuffix = 2;
        this.screenType = 2;
        obj = {
          // 取证
          GatherEvidence: true,
          //加入涉鲁信息库
          JoinSLDatabase: true,
        };
        this.getLog("涉鲁涉政监测/涉鲁推荐");
      }
      // 涉政推荐
      if (this.$route.path === "/main/shandongMonitor/politicalRecommended") {
        this.urlSuffix = 3;
        this.screenType = 3;
        obj = {
          // 取证
          GatherEvidence: true,
          //加入涉政信息库
          JoinSZDatabase: true,
        };
        this.getLog("涉鲁涉政监测/涉政推荐");
      }
      this.btnStatus = obj;
      this.getScreenList();
    },
    pageSizeChange(d) {
      this.pageNo = 1;
      this.pageSize = d;
      this.getCount();
    },
    pageNoChange(d) {
      this.pageNo = d;
      this.getListData();
    },
    query(d) {
      this.filterData = d;
      this.getCount();
    },
    getMarkingNum() {
      // this.markingNum = 0;
      let params = {
        ...this.filterData,
        isCounty: 1,
      };
      this.$http
        .get("/recommend/msgCount/" + this.urlSuffix, { params })
        .then((res) => {
          if (res.body.status === 0) {
            this.markingNum = res.body.data;
          }
        });
    },
    getHasTelData(){
      this.filterData["hasYinsi"] = 1;
      // 同步更新筛选组件的状态
      if (this.$refs.filters) {
        this.$refs.filters.params.hasYinsi = "1";
      }
      this.getCount();

    },
    getNotArchivedData(){
      //还没要求做
      // this.filterData["isArchived"] = 0; // 0表示未建档
      // // 同步更新筛选组件的状态
      // if (this.$refs.filters) {
      //   this.$refs.filters.params.isArchived = "0";
      // }
      // this.getCount();
    },
    getCount() {
      this.loading = true;
      this.total = 0;
      this.pageNo = 1;
      this.tableData = [];
      this.isRefreshPrompt = false;
      let params = {
        ...this.filterData,
        orderByType: this.orderByType,
      };
      this.$http
        .get("/recommend/msgCount/" + this.urlSuffix, { params })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0 && res.body.data > 0) {
            this.total = res.body.data;
          }
          if (this.total > 0) {
            this.getListData();
          } else {
            this.loading = false;
          }
          console.log("开始自动刷新");
          this.autoRefresh();
          this.getMarkingNum();
        });
        //获取有电话的总数
        this.$http
        .get("/recommend/msgCountWithPhone/" + this.urlSuffix, { params })
        .then((res) => {
          // console.log(res);
          if (res.body.status === 0 ) {
            this.telTotal = res.body.data;
          }
        });

        //获取未建档的总数
        this.$http
        .get("/recommend/msgCountNotArchived/" + this.urlSuffix, { params })
        .then((res) => {
          // console.log(res);
          if (res.body.status === 0 ) {
            this.notArchivedTotal = res.body.data;
          }
        });
    },
    getListData() {
      this.loading = true;
      this.checkAllGroup = [];
      this.checkAll = false;
      this.tableData = [];
      this.indeterminate = false;
      let params = {
        ...this.filterData,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        orderByType: this.orderByType,
      };
      this.$http
        .get("/recommend/msgList/" + this.urlSuffix, { params })
        .then((res) => {
          if (res.body.status === 0) {
            res.body.data.forEach( item => {
              item.sameMsgTotal = '-'
            });
            this.tableData = res.body.data
            //this.getSameMsgCount(res.body.data)
          }
          this.loading = false;
        });
    },
    getSameMsgCount(data){
      const ids = data.map((item) => item.id).join(',')
      this.$http
        .post("/recommend/sameMsgCount", { ids }, { emulateJSON: true,})
        .then((res) => {
          if(res.body.status === 0){
            data.forEach((item) => {
              item.sameMsgTotal = res.body.data[item.id] || 0
            })
          }
        });
    },
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.tableData.map((i) => i.id);
      } else {
        this.checkAllGroup = [];
      }
    },
    checkAllGroupChange(data) {
      console.log(data)
      if (data.length === this.tableData.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    //定时刷新
    autoRefresh() {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
      this.refreshTimeout = setTimeout(() => {
        let params = {
          ...this.filterData,
          orderByType: this.orderByType,
        };
        this.$http
          .get("/recommend/msgCount/" + this.urlSuffix, { params })
          .then((res) => {
            console.log("进行自动刷新判断");
            if (res.body.data > this.total) {
              if (this.single) {
                //自动刷新打开时
                this.loading = true;
                this.tableData = [];
                this.pageNo = 1;
                this.getListData();
                this.total = res.body.data;
              } else {
                // 自动刷新关闭时
                this.isRefreshPrompt = true;
                this.balanceNum = res.body.data - this.total;
              }
            }
            this.autoRefresh();
          });
      }, this.single ? 1000 * 15 : 1000 * 60 * 5);
    },
  },
  beforeDestroy() {
    clearTimeout(this.refreshTimeout);
  },
  // 计算属性 类似于 data 概念
  watch: {
    $route(to, from) {
      this.setBtnStatus();
    },
    drawer(d) {
      if (!d) {
        this.componentId = null;
      }
    },
    single(d) {
      this.autoRefresh();
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.setBtnStatus();
    if (this.$route.path === "/main/monitor/recommended") {
      this.getLog("涉济监测/涉济推荐-推荐关注");
    }
  },
};
</script>

<style lang="less" scoped>
.RefreshPrompt {
  text-align: center;
  color: #5585ec;
  background-color: #edf3fd;
  line-height: 30px;
}

.recommended {
  margin-top: 20px;
  height: calc(~"100vh - 80px");

  .main {
    height: 100%;
    background-color: #fff;
    width: 1370px;
    border-radius: 8px;
    padding: 20px 20px 0;

    .header {
      border-bottom: 2px solid #537be6;
      height: 50px;
      color: #666666;
      font-size: 14px;

      .Toggle {
        .active {
          color: #537be6;

          .triangle {
            display: block !important;
          }
        }

        .item {
          position: relative;
          line-height: 25px;
          align-items: center;
          margin-right: 40px;

          .svg-icon {
            width: 25px;
            height: 25px;
          }

          .triangle {
            display: none;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 11px solid #537be6;
          }
        }
      }

      .Tips {
        align-items: center;

        & > div {
          margin-left: 20px;
        }
      }
    }

    .screenType {
      padding-top: 8px;

      .item {
        height: 24px;
        line-height: 24px;
        padding: 0 10px;
        border: 1px solid #999999;
        display: inline-block;
        color: #999;
        font-size: 14px;
        margin-right: 20px;
      }

      .active {
        border-color: #537be6;
        color: #537be6;
      }
    }

    .content {
      height: calc(~"100% - 134px");
      overflow-y: auto;
      position: relative;
    }

    .footer {
      background-color: #fff;
      height: 50px;
      border: 1px solid #eee;
      margin: 0 -20px;
      padding: 0 20px;
      align-items: center;

      .controls {
        align-items: center;
      }

      .btn {
        background: #5585ec;
        border-radius: 4px;
        line-height: 30px;
        height: 30px;
        padding: 0 18px;
        color: #fff;
        margin-right: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
