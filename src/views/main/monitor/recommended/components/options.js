import situations from "@/assets/json/platform.json";
import regionals from "@/assets/json/regionals.json";
import fields from "@/assets/json/fields.json";

const dayNum = {
  0: "全部",
  1: "近一日",
  2: "今日",
  4: "近三日",
  8: "近一周",
  31: "近一月",
  99: "自定义",
};
const emotions = {
  0: "全部",
  1: "正面",
  2: "负面",
  3: "中性",
};
const statuses = {
  0: "全部",
  1: "重点提示",
  2: "已提示",
  3: "未提示",
};
// const orderByType = {
//   1: "按时间倒序",
//   2: "按时间正序",
// };
const topic = {
  1: "精准排重",
  3: "不排重",
};
const rule = [
  {
    key: "dayNum",
    name: "时间",
    svg: "榜单-时间",
  },
  {
    key: "situations",
    name: "平台",
    svg: "榜单-来源",
  },
  {
    key: "regionals",
    name: "地域",
    svg: "榜单-地域",
  },
  {
    key: "fields",
    name: "领域",
    svg: "榜单-领域",
  },
  {
    key: "fields",
    name: "情感倾向",
    svg: "榜单-领域",
  },
  {
    key: "statuses",
    name: "提示状态",
    svg: "榜单-处置状态",
  },
  // {
  //   key: "orderByType",
  //   name: "排序方式",
  //   svg: "榜单-排序方式",
  // },
  {
    key: "topic",
    name: "信息排重",
    svg: "榜单-信息排重",
  },
  {
    key: "keyword",
    name: "搜索词",
    svg: "榜单-搜索词",
  },
];

const allRule = [
  {
    name: "时间",
    key: "dayNum",

    options: dayNum,
  },
  {
    name: "报送单位",
    key: "reportOrganIds",
    check: true,
    options: {
      0: "全部",
      257: "大众网",
      226: "舜网",
      259: "智慧星光",
      494: "美扮",
      221: "市委网信办",
      9999: "区县网信办",
    },
  },
  {
    name: "单条信息报送单位数量",
    key: "reportNum",
    check: true,
    options: {
      0: "全部",
      1: "1",
      2: "2",
      3: "3",
      99: ">3",
    },
  },
  {
    name: "平台",
    key: "situations",
    check: true,
    options: situations,
  },
  {
    name: "地域",
    key: "regionals",
    check: true,
    options: regionals,
  },
  {
    name: "领域",
    key: "fields",
    check: true,
    options: fields,
  },
  {
    name: "情感倾向",
    key: "emotions",
    check: true,
    options: emotions,
  },
  {
    name: "提示状态",
    key: "statuses",
    options: statuses,
  },
  // {
  //   name: "排序方式",
  //   key: "orderByType",
  //   options: orderByType,
  // },
  {
    name: "信息排重",
    key: "topic",
    options: topic,
  },
  {
    name: "是否有联系方式",
    key: "hasYinsi",
    options: {
      0: "全部",
      1: "有联系方式"
    },
  },
  {
    name: "区县打标",
    key: "isCounty",
    options: {
      0: "全部",
      1: "区县打标",
    },
  },{
    name: "推送状态",
    key: "sendMsgs",
    options: {
      0: "全部",
      1: "已推送",
      2: "未推送",
    },
  },
  {
    name: "探测状态",
    key: "isMsgDel",
    options: {
      '1': "全部",
      '2': "已删除",
      '3': "未删除",
      // '4': "未探测",
      // '5': "未成功",
    },
    
  },
  {
    name: "转发情况",
    key: "isForward",
    options: {
      '1': "全部",
      '2': "已转发至山东通",
      '3': "未转发",
    },
    
  },
];

export { dayNum, emotions, statuses, topic, allRule, rule };
