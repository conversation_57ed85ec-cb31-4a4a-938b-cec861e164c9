<template>
  <div class="filter">
    <div class="header flex sb">
      <div class="title">筛选</div>
      <div class="controls flex">
        <Input
          v-model="keyword"
          placeholder="请输入关键词"
          search
          style="width: 120px;"
          @on-search="search"
        />
        <div class="item cp" @click="screen">筛选</div>
        <div v-if="type === '1'" class="item save cp" @click="openModel">
          筛选并保存
        </div>
      </div>
    </div>
    <div class="conditions">
      <div v-for="item in rule" :key="item.key" class="item">
        <div class="label flex">
          <div class="line"></div>
          <div class="title">
            {{ item.name }}
          </div>
          <div v-if="item.name === '时间'" class="yuanBtn">
            <RadioGroup v-model="timeGrounp">
              <Radio label="发布时间"></Radio>
              <Radio label="报送时间"></Radio>
            </RadioGroup>
          </div>
        </div>
        <div
          v-if="customComponents.indexOf(item.name) === -1"
          class="options flex"
        >
          <div
            v-for="(v, k) in item.options"
            :key="k"
            :class="[
              'item',
              'cp',
              item.check
                ? params[item.key].includes(k)
                  ? 'active'
                  : ''
                : params[item.key] === k
                ? 'active'
                : '',
            ]"
            @click="selectData(item, k)"
          >
            {{ v }}
          </div>
          <DatePicker
            v-if="item.key === 'dayNum' && params.dayNum === '99'"
            placeholder="Select date and time"
            style="width: 240px;"
            transfer
            split-panels
            type="datetimerange"
            placement="bottom-end"
            @on-change="dateChange"
          ></DatePicker>
        </div>
        <CustomSelectnew
          v-if="
            item.name === '领域' &&
            $route.path.indexOf('/monitor/recommended') > -1
          "
          v-model="fields"
          :options="domainList"
          placeholder="请选择领域类型"
        />
        <CustomSelect
          v-if="
            item.name === '领域' &&
            $route.path.indexOf('/monitor/recommended') == -1
          "
          v-model="fields"
          :options="domainList"
          placeholder="请选择领域类型"
        />
      </div>
    </div>
    <Modal v-model="saveModal" title="保存筛选条件" @on-ok="ok">
      <div class="frame flex">
        <div class="label flex">
          <div
            v-for="(v, k) in typeList"
            :key="k"
            :class="['item', 'cp', saveType === k ? 'active' : '']"
            @click="cutType(k)"
          >
            {{ v }}
          </div>
        </div>
        <div class="content">
          <div>
            <Input v-model="saveName" placeholder="请输入筛选条件名称" />
          </div>
          <div v-if="saveType === '2'" class="list flex">
            <div
              v-for="item in screenList"
              :key="item.id"
              :class="['item', saveId === item.id ? 'active' : '']"
              @click="
                saveId = item.id;
                saveName = item.screenName;
              "
            >
              {{ item.screenName }}
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="box">
        <div class="modalBtn" @click="ok">确定</div>
        <div class="modalBtn" @click="saveModal = false">关闭</div>
      </div>
    </Modal>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import { allRule, dayNum, emotions, statuses, topic } from "./options";
import situations from "@/assets/json/platform.json";
import regionals from "@/assets/json/regionals.json"; //济南地区
import shandong from "@/assets/json/shandong.json"; //山东地区
import moment from "moment/moment";
import { mapState } from "pinia";
import { useDomainStore } from "@/stores/domainList";
import CustomSelect from "@/components/CustomSelect/index.vue";
import CustomSelectnew from "@/components/CustomSelect/index2.vue";

const typeList = {
  1: "新增",
  2: "替换",
};
var isMsgDelMapper = {
  "1": null,
  "2": "1",
  "3": "0",
  "4": "-2",
  "5": "-1",
};

export default {
  data() {
    // 这里存放数据
    return {
      moment,
      rule: {},
      typeList,
      allRule,
      dayNum,
      emotions,
      statuses,
      // orderByType,
      topic,
      situations,
      regionals,
      shandong,
      fields: [],
      saveModal: false,
      saveName: "",
      saveType: 1,
      saveId: null,
      keyword: "",
      params: {
        dayNum: "1",
        situations: ["0"],
        regionals: ["0"],
        emotions: ["0"],
        statuses: "0",
        orderByType: "1",
        topic: "1",
        sendMsgs: "0",
        isCounty: "0",
        reportOrganIds: ["0"],
        hasYinsi:"0",
        reportNum: ["0"],
        isMsgDel: "1", // 探测状态
        isForward: "1", // 转发情况
      },
      timeGrounp: "报送时间",

      customComponents: ["领域"],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { CustomSelect, CustomSelectnew },
  props: {
    screenList: {
      default: () => [],
    },
    screenType: {
      default: 1,
    },
    defaultScreen: {
      default: "",
    },
    //推荐关注1和区县打标2
    type: {
      default: 1,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.unfoldScreen();
    this.rule = this.allRule;
  },
  // 方法集合
  methods: {
    search() {
      this.unfoldScreen();
    },
    getUnitList() {
      console.log(this.params.dayNum);
      let params = {
        type: 1,
        dayNum:
          this.params.dayNum === "99" || this.params.dayNum === "0"
            ? null
            : this.params.dayNum === "1"
            ? "98"
            : this.params.dayNum - 1,
        timeType: this.timeGrounp == "发布时间" ? 1 : 2, //查询时间类型:1按发布时间查询2按推送时间查询
        startTime:
          this.params.dayNum === "99" ? this.params["startTime"] : null,
        endTime: this.params.dayNum === "99" ? this.params["endTime"] : null,
      };
      console.log(params.dayNum);
      this.$http.get("/recommend/getReportOrgan", { params }).then((res) => {
        let unitList = {
          0: "全部",
          ...res.body.data,
        };
        for (let item of this.allRule) {
          if (item.key == "reportOrganIds") {
            item.options = unitList;
            break;
          }
        }
      });
    },
    screen() {
      this.$parent.screenId = null;
      this.unfoldScreen();
    },
    cutType(d) {
      if (this.screenList.length === 5 && d === "1") {
        return this.$Message.warning("最多只能保存5个筛选条件！");
      } else {
        this.saveType = d;
      }
    },
    openModel(d) {
      this.saveModal = true;
      this.saveId = null;
      this.saveName = "";
      console.log(this.screenList.length);
      if (this.screenList.length === 5) {
        this.saveType = "2";
      } else {
        this.saveType = "1";
      }
    },
    ok() {
      if (!this.saveName) {
        this.$Message.warning("请输入筛选条件名称后重试！");
        return;
      }
      if (this.saveType === "2" && !this.saveId) {
        this.$Message.warning("请选择要替换的筛选条件后重试！");
        return;
      }
      let json = {
        ...this.params,
        situations: this.params.situations.toString(),
        regionals: this.params.regionals.toString(),
        reportOrganIds: this.params.reportOrganIds.toString(),
        reportNum: this.params.reportNum.toString(),
        emotions: this.params.emotions.toString(),
        fields:
          this.$route.path.indexOf("/monitor/recommended") > -1
            ? this.addName()
            : this.fields.toString(),
        dayNum: this.params.dayNum === "99" ? null : this.params.dayNum,
        isMsgDel: this.realisMsgDel,
        isForward:
          this.params.isForward == 1
            ? null
            : this.params.isForward == 2
            ? 1
            : 0,
      };
      json.emotions = this.replaceArrayValues([...this.params.emotions]);
      let params = {
        screenName: this.saveName,
        screenType: this.screenType,
        id: this.saveId,
        screenJson: JSON.stringify(json),
      };
      this.$http
        .post("/recommend/screen/saveOrUpdateScreen", params, {
          emulateJSON: true,
        })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
            this.$parent.getScreenList();
            this.unfoldScreen();
          } else {
            this.$Message.warning(res.body.message);
          }
          this.saveModal = false;
        });
    },
    // 领域分类名称
    addName() {
      let list = [];
      if (this.domainList) {
        this.domainList.forEach((v) => {
          if (this.fields.includes(v.id)) {
            list.push(v.name);
          }
          if (v.children && v.children.length > 0) {
            v.children.forEach((c) => {
              if (this.fields.includes(c.id)) {
                list.push(c.name);
              }
            });
          }
        });
      }
      if (this.fields.filter((v) => v == "未分类").length > 0) {
        list.push("未分类");
      }
      return list.toString();
    },
    replaceArrayValues(arr) {
      // 遍历数组
      if (arr.length === 0 || arr[0] === "0") {
        return null;
      }
      console.log(arr);
      for (let i = 0; i < arr.length; i++) {
        // 如果值为2，将其替换为-1
        if (arr[i] === "2") {
          arr[i] = "-1";
        }
        // 如果值为3，将其替换为0
        if (arr[i] === "3") {
          arr[i] = "0";
        }
      }
      console.log(arr);
      return arr.toString();
    },
    unfoldScreen() {
      this.allScreen = false;
      console.log(this.params.emotions);
      let params = {
        ...this.params,
        keyword: this.keyword,
        situations: this.params.situations.toString(),
        regionals: this.params.regionals.toString(),
        fields:
          this.$route.path.indexOf("/monitor/recommended") > -1
            ? this.addName()
            : this.fields.toString(),
        dayNum:
          this.params.dayNum === "99" || this.params.dayNum === "0"
            ? null
            : this.params.dayNum === "1"
            ? "98"
            : this.params.dayNum - 1,
        timeType: this.timeGrounp == "发布时间" ? 1 : 2,
        reportOrganIds: this.params.reportOrganIds.toString(),
        reportNum: this.params.reportNum.toString(),
        isMsgDel: this.realisMsgDel,
        isForward:
          this.params.isForward == 1
            ? null
            : this.params.isForward == 2
            ? 1
            : 0,
      };

      Object.keys(params).forEach((i) => {
        if (params[i] === "0" && (i != "isMsgDel" && i != "isForward")) {
          params[i] = null;
        }
      });
      params.emotions = this.replaceArrayValues([...this.params.emotions]);

      this.$emit("query", {
        ...params,
      });
    },
    dateChange(d) {
      this.params["startTime"] = d[0];
      this.params["endTime"] = d[1];
      // this.getUnitList();
      // this.params["reportOrganIds"] = ["0"];
      this.params["reportNum"] = ["0"];
    },
    reset() {
      this.fields = [0];
      this.params = {
        // dayNum: "8",
        dayNum: "1",
        situations: ["0"],
        regionals: ["0"],
        emotions: ["0"],
        statuses: "0",
        orderByType: "1",
        topic: "1",
        isCounty: "0",
        sendMsgs: "0",
        reportNum: ["0"],
        reportOrganIds: ["0"],
        hasYinsi:0,
        isMsgDel: "1",
        isForward: "1",
      };
      this.timeGrounp = "报送时间";
    },
    selectData(data, k) {
      console.log(data, k);
      if (data.check) {
        if (data.key == "reportOrganIds") {
          this.params["reportNum"] = ["0"];
        } else if (data.key == "reportNum") {
          this.params["reportOrganIds"] = ["0"];
        }
        //全选
        // 如果当前选项是0，那直接重置为['0']
        if (k === "0") {
          this.params[data.key] = ["0"];
          return;
        }
        if (this.params[data.key].includes(k)) {
          // 有值就删掉
          this.params[data.key].splice(this.params[data.key].indexOf(k), 1);
          if (this.params[data.key].length === 0) {
            this.params[data.key] = ["0"];
          }
        } else {
          // 添加前先判断有没有0，有的话去掉
          if (this.params[data.key].includes("0")) {
            // 有值就删掉
            this.params[data.key].splice(this.params[data.key].indexOf("0"), 1);
          }
          // 没值就添加
          this.params[data.key].push(k);
        }
      } else {
        //单选
        this.params[data.key] = k;
      }
      console.log(this.params[data.key]);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    ...mapState(useDomainStore, ["domainList"]), //监控 data 中的数据变化
    realisMsgDel() {
      return isMsgDelMapper[this.params["isMsgDel"]];
    },
  },

  watch: {
    type: {
      handler(d) {
        if (d == 1) {
          this.rule = this.allRule;
          this.reset();
          console.log(this.params);
          this.unfoldScreen();
        } else {
          this.rule = [this.allRule[0]];
          this.fields = [0];
          console.log(this.params);
          this.params = {
            ...this.params,
            situations: ["0"],
            regionals: ["0"],
            emotions: ["0"],
            statuses: "0",
            orderByType: "1",
            topic: "1",
            isCounty: "1",
            sendMsgs: "0",
            reportNum: ["0"],
            reportOrganIds: ["0"],
            isMsgDel: "1",
            isForward: "1",
          };

          this.unfoldScreen();
        }
        console.log(this.rule);
      },
    },
    "params.dayNum": {
      handler(d) {
        if (d !== "99") {
          this.params["startTime"] = null;
          this.params["endTime"] = null;
          // this.getUnitList();
          // this.params["reportOrganIds"] = ["0"];
        }
      },
      deep: true,
    },
    defaultScreen(d) {
      console.log(d);
      if (d) {
        let json = JSON.parse(d);
        this.params = {
          ...json,
          situations: json.situations ? json.situations.split(",") : ["0"],
          regionals: json.regionals ? json.regionals.split(",") : ["0"],
          emotions: json.emotions
            ? json.emotions.replace(/-1/g, "2").replace(/0/g, "3").split(",")
            : ["0"],
          reportOrganIds: json.reportOrganIds
            ? json.reportOrganIds.split(",")
            : ["0"],
          reportNum: json.reportNum ? json.reportNum.split(",") : ["0"],
          dayNum: json.dayNum ? json.dayNum : "7",
          isMsgDel:
            json.isMsgDel || json.isMsgDel === 0
              ? json.isMsgDel == "1"
                ? "2"
                : json.isMsgDel === "0"
                ? "3"
                : json.isMsgDel == "-2"
                ? "4"
                : json.isMsgDel == "-1"
                ? "5"
                : ""
              : "1",
          isForward:
            json.isForward === 0 ? "3" : json.isForward === 1 ? "2" : "1",
        };
        if (
          this.$route.path.indexOf("/monitor/recommended") > -1 &&
          json.fields
        ) {
          let list = [];
          this.domainList.forEach((v) => {
            if (json.fields.split(",").includes(v.name)) {
              list.push(v.id);
            }
            if (v.children && v.children.length > 0) {
              v.children.forEach((c) => {
                if (json.fields.split(",").includes(c.name)) {
                  list.push(c.id);
                }
              });
            }
          });
          this.fields = list ? list.map((i) => Number(i)) : [0];
        } else {
          this.fields = json.fields
            ? json.fields.split(",").map((i) => Number(i))
            : [0];
        }

        // this.$parent.defaultScreen = "";
        console.log(this.params, "1------");
        this.unfoldScreen();
      }
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.domainList = useDomainStore();
    this.addName();
  },
};
</script>

<style lang="less" scoped>
.filter {
  width: 370px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
  padding: 10px 8px;

  .header {
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;

    .title {
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
      font-size: 16px;
      line-height: 16px;
    }

    .controls {
      .item {
        text-align: center;
        line-height: 30px;
        font-weight: 600;
        color: #ffffff;
        font-size: 14px;
        width: 80px;
        height: 30px;
        border-radius: 4px;
        background: #5585ec;
        margin-left: 10px;
      }

      .save {
        background: #f4a000;
      }
    }
  }

  .conditions {
    height: calc(~"100% - 50px");
    overflow-y: auto;

    /deep/ .ivu-date-picker {
      margin-top: 8px;
      .ivu-input {
        height: 22px;
      }
    }

    & > .item {
      margin-top: 20px;
      position: relative;

      .yuanBtn {
        position: absolute;
        right: 30px;

        /deep/ .ivu-radio-wrapper {
          font-size: 14px;
        }
      }

      .label {
        align-items: center;

        .line {
          width: 3px;
          height: 12px;
          background: #537be6;
          margin-right: 5px;
        }

        .title {
          color: #333333;
          font-size: 14px;
          line-height: 30px;
        }
      }

      .options {
        flex-wrap: wrap;

        .item {
          min-width: 40px;
          margin-top: 10px;
          margin-right: 10px;
          padding: 0 5px;
          height: 22px;
          border: 1px solid #c4c3c3;
          border-radius: 2px;
          line-height: 20px;
          text-align: center;
          font-size: 14px;
        }

        .active {
          border-color: #537be6;
          color: #537be6;
        }
      }
    }
  }
}

.frame {
  & > .label {
    background-color: #eee;
    height: 30px;

    .item {
      line-height: 30px;
      padding: 0 10px;
    }

    .active {
      background-color: #537be6;
      color: #fff;
    }
  }

  & > .content {
    padding: 0 20px;
    width: 350px;

    .list {
      //padding: 10px;
      flex-wrap: wrap;

      .item {
        line-height: 30px;
        padding: 0 10px;
        background-color: #eee;
        margin: 2px 5px;
        cursor: pointer;
      }

      .active {
        background-color: #537be6;
        color: #fff;
      }
    }
  }
}

.box {
  display: flex;
  justify-content: space-between;
  padding: 0 30%;

  .modalBtn {
    cursor: pointer;
    line-height: 40px;
    text-align: center;
    width: 80px;
    height: 40px;
    background: #5585ec;
    border-radius: 4px;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;

    &:nth-child(2) {
      background: #999;
    }
  }
}
</style>
