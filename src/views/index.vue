<!-- 入口页 -->
<template>
  <!-- <div class="frame" :style="{ height: windowHeight, width: windowWidth }"> -->
  <div class="frame">
    <RouterView />
  </div>
</template>

<script>
import { useDomainStore } from "@/stores/domainList";

export default {
  data() {
    return {
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
    };
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {
    this.setViewportScale();
    //更新领域
    this.$http.get("/classifyConfig/classifyList?type=1").then((res) => {
      const domain = useDomainStore();
      domain.setDomainList(res.body.data);
    });
  },
  //方法所在
  methods: {
    setViewportScale() {
      const originalWidth = 1920; // 原始宽度
      const originalHeight = 1080; // 原始高度

      const currentWidth = window.innerWidth;
      // const currentHeight = window.innerHeight;
      console.log(currentWidth);

      // 计算宽度和高度的缩放比例
      const scaleWidth = currentWidth / originalWidth;
      // const scaleHeight = currentHeight / originalHeight;

      // 选择较小的比例作为缩放值
      // const scale = Math.min(scaleWidth, scaleHeight);

      // 动态设置 meta viewport 缩放比例
      // this.updateMetaViewport(scale);
      this.updateMetaViewport(scaleWidth);
    },
    updateMetaViewport(scale) {
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        // 如果没有 viewport meta 标签，创建一个
        viewportMeta = document.createElement("meta");
        viewportMeta.name = "viewport";
        document.head.appendChild(viewportMeta);
      }

      // 设置 viewport meta 标签的 content 属性
      viewportMeta.setAttribute(
        "content",
        `width=device-width, initial-scale=${scale}, maximum-scale=${scale}, user-scalable=no`
      );
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    // window.addEventListener("resize", this.setViewportScale);
  },
  // beforeDestroy() {
  //   window.removeEventListener("resize", this.setViewportScale);
  // },
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.frame {
  width: 1920px;
  height: 100%;
  flex: 1;
}
//媒，商，自 标签样式
/deep/.ui_tab_mei{
  width:20px;
  height:20px;
  display:flex;
  justify-content: center;
  align-items: center;
  border-radius:2px;
  border:2px solid #EF8E9B;
  color:#EF8E9B;
  &::before{
    content:"媒";
    font-size:14px;
    font-weight: 600;
    
  }
}
/deep/.ui_tab_shang{
  width:20px;
  height:20px;
  display:flex;
  justify-content: center;
  align-items: center;
  border-radius:2px;
  border:2px solid #5D92EB;
  color:#5D92EB;
  &::before{
    content:"商";
    font-size:12px;
    font-weight: 600;
    
  }
}
/deep/.ui_tab_zi{
  width:20px;
  height:20px;
  display:flex;
  justify-content: center;
  align-items: center;
  border-radius:2px;
  border:2px solid #F7A03E;
  color:#F7A03E;
  &::before{
    content:"自";
    font-size:12px;
    font-weight: 600;
    
  }
}
</style>
