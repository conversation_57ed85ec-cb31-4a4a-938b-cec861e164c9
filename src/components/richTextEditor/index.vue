<template>
  <div class="rich-text-editor">
    <quill-editor
      ref="myQuillEditor"
      v-model="content"
      :options="editorOption"
      @blur="onEditorBlur($event)"
      @focus="onEditorFocus($event)"
      @ready="onEditorReady($event)"
      @change="onEditorChange($event)"
    />
  </div>
</template>

<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

export default {
  name: 'RichTextEditor',
  components: {
    quillEditor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    height: {
      type: String,
      default: '200px'
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      content: this.value,
      editorOption: {
        placeholder: this.placeholder,
        readOnly: this.readonly,
        theme: 'snow',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],        // 加粗 斜体 下划线 删除线
            ['blockquote', 'code-block'],                     // 引用  代码块
            [{ 'header': 1 }, { 'header': 2 }],               // 1、2 级标题
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],     // 有序、无序列表
            [{ 'script': 'sub'}, { 'script': 'super' }],      // 上标/下标
            [{ 'indent': '-1'}, { 'indent': '+1' }],          // 缩进
            [{ 'direction': 'rtl' }],                         // 文本方向
            [{ 'size': ['small', false, 'large', 'huge'] }],  // 字体大小
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],        // 标题
            [{ 'color': [] }, { 'background': [] }],          // 字体颜色、字体背景颜色
            [{ 'font': [] }],                                 // 字体种类
            [{ 'align': [] }],                                // 对齐方式
            ['clean'],                                        // 清除文本格式
            ['link']                                          // 链接
          ]
        }
      }
    }
  },
  watch: {
    value(newVal) {
      if (newVal !== this.content) {
        this.content = newVal
      }
    },
    content(newVal) {
      this.$emit('input', newVal)
    }
  },
  mounted() {
    // 设置编辑器高度
    this.$nextTick(() => {
      const editor = this.$refs.myQuillEditor.$el.querySelector('.ql-editor')
      if (editor) {
        editor.style.minHeight = this.height
      }
    })
  },
  methods: {
    onEditorBlur(quill) {
      this.$emit('blur', quill)
    },
    onEditorFocus(quill) {
      this.$emit('focus', quill)
    },
    onEditorReady(quill) {
      this.$emit('ready', quill)
    },
    onEditorChange({ quill, html, text }) {
      this.$emit('change', { quill, html, text })
    }
  }
}
</script>

<style lang="less" scoped>
.rich-text-editor {
  /deep/ .ql-toolbar {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: none;
  }
  
  /deep/ .ql-container {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-top: none;
  }
  
  /deep/ .ql-editor {
    font-size: 14px;
    line-height: 1.5;
    
    &.ql-blank::before {
      font-style: normal;
      color: #999;
    }
  }
  
  /deep/ .ql-snow .ql-tooltip {
    z-index: 9999;
  }
}
</style>
