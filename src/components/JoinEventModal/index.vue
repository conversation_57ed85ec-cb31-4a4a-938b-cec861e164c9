<template>
  <Modal
    v-model="visible"
    title="加入事件"
    width="1000"
    :loading="confirmLoading"
    @on-ok="handleConfirm"
    @on-cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="search-area">
      <div class="search-row">
        <div class="search-item">
          <span class="label">时间：</span>
          <DatePicker
            v-model="searchForm.timeRange"
            type="daterange"
            format="yyyy-MM-dd"
            placeholder="选择时间范围"
            style="width: 200px"
            @on-change="handleTimeChange"
          />
        </div>
        <div class="search-item">
          <span class="label">状态：</span>
          <Select v-model="searchForm.status" style="width: 120px" @on-change="handleSearch">
            <Option value="0">全部</Option>
            <Option value="1">进行中</Option>
            <Option value="2">已暂停</Option>
          </Select>
        </div>
        <div class="search-item">
          <span class="label">搜索词查找：</span>
          <Input
            v-model="searchForm.keyword"
            placeholder="请输入关键词"
            style="width: 200px"
            @on-enter="handleSearch"
          />
          <Button size="large" type="primary" @click="handleSearch" style="margin-left: 8px">搜索</Button>
        </div>
      </div>
    </div>
    <div class="selected-info">
        已选择 {{ selectedEvents.length }} 个事件
      </div>
    <!-- 事件列表 -->
    <div class="event-list">
      <div class="list-header">
        <Checkbox
          :indeterminate="indeterminate"
          :value="checkAll"
          @click.prevent.native="handleCheckAll"
        >
          全选
        </Checkbox>
        <span class="list-title">标题</span>
        <span class="list-time">创建时间</span>
      </div>
      
      <CheckboxGroup v-model="selectedEvents" @on-change="handleSelectionChange">
        <div class="event-table" v-if="eventList.length > 0">
          <div
            v-for="(item, index) in eventList"
            :key="item.eventId"
            :class="['event-row']"
          >
            <div class="event-checkbox">
              <Checkbox
                :label="String(item.eventId)"
              ></Checkbox>
              <span class="row-number">{{ (pagination.pageNo - 1) * pagination.pageSize + index + 1 }}</span>
            </div>
            <div class="event-title">
              {{ item.eventName }}

            </div>
            <div class="event-time">{{ formatTime(item.createTime) }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <Icon type="ios-document-outline" size="48" color="#c5c8ce" />
          <p>暂无事件数据</p>
        </div>
      </CheckboxGroup>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
       
        <Page
          :current="pagination.pageNo"
          :total="total"
          :page-size="pagination.pageSize"
          show-total
          show-sizer
          @on-change="handlePageChange"
          @on-page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 底部信息 -->
    <div slot="footer" class="modal-footer">
      <div class="footer-buttons">
        <Button size="large" @click="handleCancel">取消</Button>
        <Button size="large" type="primary" :loading="confirmLoading" @click="handleConfirm">
          确定
        </Button>
      </div>
    </div>
  </Modal>
</template>

<script>
import moment from 'moment'

export default {
  name: 'JoinEventModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
     data: {
      default: () => {},
    },
    // 当前素材信息，用于加入事件时传递
    materialInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: this.value,
      confirmLoading: false,

      // 搜索表单
      searchForm: {
        keyword: '',
        status: '0',
        timeRange: [],
        startTime: '',
        endTime: ''
      },

      // 分页信息
      pagination: {
        pageNo: 1,
        pageSize: 10
      },

      // 列表数据
      eventList: [],
      total: 0,
      loading: false,

      // 选择相关
      selectedEvents: [],
      checkAll: false,
      indeterminate: false,

      // 已存在的事件ID数组
      existingEventIds: []
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val) {
        this.initModal()
      }
    },
    visible(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    // 初始化弹窗
    initModal() {
      console.log("传入的数据:", this.data)
      this.parseExistingEvents()
      this.resetForm()
      this.loadEventList()
    },

    // 解析已存在的事件ID
    parseExistingEvents() {
      this.existingEventIds = []
      if (this.data && this.data.jcczexistStatus) {
        try {
          // 将字符串格式的数组转换为真正的数组
          const parsedIds = JSON.parse(this.data.jcczexistStatus)
          if (Array.isArray(parsedIds)) {
            this.existingEventIds = parsedIds.map(id => String(id)) // 确保ID为字符串格式
          }
        } catch (error) {
          console.error('解析已存在事件ID失败:', error)
          this.existingEventIds = []
        }
      }
      console.log('已存在的事件ID:', this.existingEventIds)
    },
    
    // 重置表单
    resetForm() {
      this.searchForm = {
        keyword: '',
        status: '0',
        timeRange: [],
        startTime: '',
        endTime: ''
      }
      this.pagination = {
        pageNo: 1,
        pageSize: 10
      }
      // 初始化选择的事件为已存在的事件ID
      this.selectedEvents = [...this.existingEventIds]
      this.checkAll = false
      this.indeterminate = false
    },
    
    // 加载事件列表
    async loadEventList() {
      this.loading = true
      try {
        const params = {
          keyword: this.searchForm.keyword,
          status: this.searchForm.status== '0' ? '' : this.searchForm.status,
          type: 0, // 根据需要调整
          pageNo: this.pagination.pageNo,
          pageSize: this.pagination.pageSize,
          startTime: this.searchForm.startTime,
          endTime: this.searchForm.endTime
        }
        
        // 获取列表数据
        const listResponse = await this.$http.get('/monitor/event/list', { params })
        if (listResponse.body.status === 0) {
          this.eventList = listResponse.body.data || []
        }
        
        // 获取总数
        const countResponse = await this.$http.get('/monitor/event/count', { params })
        if (countResponse.body.status === 0) {
          this.total = countResponse.body.data || 0
        }

        // 加载完成后更新选择状态
        this.updateSelectionState()

      } catch (error) {
        console.error('加载事件列表失败:', error)
        this.$Message.error('加载事件列表失败')
      } finally {
        this.loading = false
      }
    },

    // 更新选择状态
    updateSelectionState() {
      // 初始化时，将已存在的事件ID添加到选中列表（仅在初始化时）
      if (this.selectedEvents.length === 0) {
        this.selectedEvents = [...this.existingEventIds]
      }

      // 更新全选状态
      this.updateCheckAllState()
    },

    // 更新全选状态
    updateCheckAllState() {
      const currentPageEventIds = this.eventList.map(item => String(item.eventId))
      const selectedInCurrentPage = currentPageEventIds.filter(id => this.selectedEvents.includes(id))

      if (selectedInCurrentPage.length === 0) {
        this.indeterminate = false
        this.checkAll = false
      } else if (selectedInCurrentPage.length === currentPageEventIds.length) {
        this.indeterminate = false
        this.checkAll = true
      } else {
        this.indeterminate = true
        this.checkAll = false
      }
    },
    
    // 时间范围变化
    handleTimeChange(dates) {
      if (dates && dates.length === 2) {
        this.searchForm.startTime = moment(dates[0]).format('YYYY-MM-DD 00:00:00')
        this.searchForm.endTime = moment(dates[1]).format('YYYY-MM-DD 23:59:59')
      } else {
        this.searchForm.startTime = ''
        this.searchForm.endTime = ''
      }
      this.handleSearch()
    },
    
    // 搜索
    handleSearch() {
      this.pagination.pageNo = 1
      this.loadEventList()
    },
    
    // 分页变化
    handlePageChange(page) {
      this.pagination.pageNo = page
      this.loadEventList()
    },
    
    // 页面大小变化
    handlePageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.pageNo = 1
      this.loadEventList()
    },
    
    // 全选/取消全选
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false
      } else {
        this.checkAll = !this.checkAll
      }
      this.indeterminate = false

      const currentPageEventIds = this.eventList.map(item => String(item.eventId))

      if (this.checkAll) {
        // 全选：将当前页面的所有事件ID添加到选择列表中
        const newSelectedEvents = [...new Set([...this.selectedEvents, ...currentPageEventIds])]
        this.selectedEvents = newSelectedEvents
      } else {
        // 取消全选：从选择列表中移除当前页面的事件ID
        this.selectedEvents = this.selectedEvents.filter(id => !currentPageEventIds.includes(id))
      }
    },
    
    // 选择变化
    handleSelectionChange() {
      // 允许用户自由选择和取消选择，包括已加入的事件
      this.updateCheckAllState()
    },
    
    // 确认加入
    async handleConfirm() {
      if (this.selectedEvents.length === 0) {
        this.$Message.warning('请选择要加入的事件')
        return
      }

      this.confirmLoading = true
      try {
        // 调用加入事件接口
        const params = {
          msgKey: this.materialInfo.id || this.materialInfo.mkey,
          situation: this.materialInfo.situation || '30',
          eventIds: this.selectedEvents.join(',')
        }

        const response = await this.$http.get('/monitor/event/joinEvent', { params })

        if (response.body.status === 0) {
          this.$Message.success('加入事件成功')
          this.$emit('on-success', this.selectedEvents)
          // 触发重新获取详情页数据
          setTimeout(()=>this.$emit('refresh-detail'),1000)

          this.handleCancel()
        } else {
          this.$Message.error(response.body.message || '加入事件失败')
        }

      } catch (error) {
        console.error('加入事件失败:', error)
        this.$Message.error('加入事件失败')
      } finally {
        this.confirmLoading = false
      }
    },
    
    // 取消
    handleCancel() {
      this.visible = false
    },
    
    // 格式化时间
    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    
    
    
   
    
    
  }
}
</script>

<style lang="less" scoped>
/deep/ .ivu-modal-body{
  padding-bottom:0;
  font-size:14px;
}
.search-area {

  margin-bottom:15px;
  border-radius: 4px;
}

.search-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.search-item {
  display: flex;
  align-items: center;
}

.search-item .ivu-btn {
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
}

.label {
  margin-right: 8px;
  white-space: nowrap;
}

.event-list {
  max-height: 500px;
}

.list-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f8f9;
  border: 1px solid #e8eaec;
  border-bottom: none;
  font-weight: 500;
  color: #333;
}

.list-header .ivu-checkbox-wrapper {
  width: 80px;
}

.list-title {
  flex: 1;
  text-align: left;
}

.list-time {
  width: 180px;
  text-align: center;
}

.event-table {
  max-height: 350px;
  overflow-y: auto;
  border: 1px solid #e8eaec;
}

.event-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8eaec;
  transition: all 0.2s;
  background: #fff;
}

.event-row:last-child {
  border-bottom: none;
}

.event-row:hover {
  background: #f8f9fa;
}

.event-row.existing-event {
  background: #f6ffed;
  border-left: 3px solid #52c41a;
}

.event-row.existing-event:hover {
  background: #f0f9ff;
}

.event-checkbox {
  width: 80px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.row-number {
  font-size: 12px;
  color: #999;
  min-width: 20px;
}

.event-title {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  padding-right: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.existing-tag {
  background: #f0f9ff;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #d1ecf1;
  white-space: nowrap;
  flex-shrink: 0;
}

.event-time {
  width: 180px;
  text-align: center;
  font-size: 12px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content:center;
}

.total-info {
  color: #666;

}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-info {
  color: #666;

  margin-bottom:10px;
}

.footer-buttons {
  display: flex;
  gap: 12px;
}

.footer-buttons .ivu-btn {
  min-width: 100px;
  height: 40px;
  font-size: 16px;
  font-weight: 500;
}

/* 已加入事件的 checkbox 样式 */
.existing-event .ivu-checkbox-disabled .ivu-checkbox-inner {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

.existing-event .ivu-checkbox-disabled .ivu-checkbox-inner:after {
  border-color: #fff !important;
}
</style>
