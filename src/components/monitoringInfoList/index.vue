<template>
  <div class="MonitoringInfoList" ref="MonitoringInfoList">
    <template v-if="isRender">
      <div class="SerialNum">
        <Checkbox :label="data[checkKey]" v-if="!hideCheckBox">
          {{ index + 1 + (pageNo - 1) * pageSize }}
        </Checkbox>
        <span v-else>{{ index + 1 + (pageNo - 1) * pageSize }}</span>
      </div>
      <div class="content cp">
        <template v-if="$route.path.indexOf('monitor/recommended') > -1">
          <div class="tc_status" v-if="data.isMsgDel == 1">
            <svg-icon icon-class="已删除" class="icon_tcstatus"></svg-icon>
          </div>
        </template>
        <template v-if="data.situation !== 10">
          <div class="title ellipsis" @click="toDetails">
            <!--          <svg-icon :icon-class="situationIcon[data.situation]" />-->
            <span
              v-if="
                $route.path.indexOf('monitor/recommended') > -1 &&
                data.sendMessage
              "
              style="
                color: #fff;
                background: #00ff7f;
                border-radius: 4px;
                font-size: 14px;
              "
              >已报送至"群"</span
            >
            <span
              v-if="
                ($route.path.indexOf('monitor/message') > -1 ||
                  $route.path.indexOf('monitor/recommended') > -1) &&
                data.status == 1
              "
              style="
                color: #fff;
                background: #f09fa9;
                border-radius: 4px;
                font-size: 14px;
              "
              >已下发提示单</span
            >
            <span
              v-if="
                $route.path.indexOf('monitor/recommended') > -1 &&
                data.sendMsg == 1
              "
              style="
                color: #fff;
                background: #4caf50;
                border-radius: 4px;
                font-size: 14px;
              "
              >已推送</span
            >

            <div
              v-if="
                ($route.path.indexOf('monitor/recommended') > -1 || $route.path.indexOf('monitor/SDplatform') > -1) &&
                data.isRead !== null
              "
              class="readStatus"
              :style="{ background: data.isRead === 1 ? '#8FC9FF' : '' }"
            >
              {{ data.isRead === 1 ? "已读" : "未读" }}
            </div>
            <span
              v-if="data.isForward == 1"
              style="
                color: #fff;
                background: #ff8000;
                border-radius: 4px;
                font-size: 14px;
              "
              >已转发</span
            >

            <PlatformIcon :id="data.situation" :text="data.mwebsiteName" />
            <!-- <div class="readStatus" > -->

            <span
              v-html="
                highlightTool.highlightByHitWords(
                  data.mtitle
                    ? removeROrN(data.mtitle)
                    : removeROrN(data.mabstract),
                  keyword,
                  'highlight0'
                )
              "
            ></span>
          </div>
          <div class="abstract ellipsis cp" @click="toDetails">
            <div
              class="ellipsis"
              v-html="
                highlightTool.highlightByHitWords(
                  data.mabstract
                    ? removeROrN(data.mabstract)
                    : data.mcontent
                    ? removeROrN(data.mcontent)
                    : removeROrN(data.mtitle),
                  keyword,
                  'highlight0'
                )
              "
            ></div>
            <!-- ocr内容展示 -->
            <span
              v-if="
                (data.mabstract && data.mabstract.indexOf('\\nocr:') > -1) ||
                (data.mcontent && data.mcontent.indexOf('\\nocr:') > -1)
              "
              style="margin-left: 5px;"
            >
              <svg-icon icon-class="ocr-img" />
              <span
                v-html="
                  highlightTool.highlightByHitWords(
                    data.mabstract
                      ? removeOcr(data.mabstract)
                      : data.mcontent
                      ? removeOcr(data.mcontent)
                      : removeOcr(data.mtitle),
                    keyword,
                    'highlight0'
                  )
                "
              ></span>
            </span>

            <!-- <span
              v-if="data.topicNum"
              class="suffix cp"
              @click.stop="openDrawer(1)"
              >({{ data.topicNum }})</span
            > -->
          </div>
        </template>
        <template v-else>
          <v-clamp
            :keywords="
              keyword.length > 0
                ? [
                    {
                      key: keyword,
                      class: 'highlight0',
                    },
                  ]
                : []
            "
            :max-lines="2"
            autoresize
            class="cp"
            style="height: 48px; padding-right: 60px;"
            @click.native="toDetails"
          >
            <template v-slot:before>
              <span
                v-if="
                  $route.path.indexOf('monitor/recommended') > -1 &&
                  data.sendMessage
                "
                style="
                  color: #fff;
                  background: #00ff7f;
                  border-radius: 4px;
                  font-size: 14px;
                "
                >已报送至"群"</span
              >
              <span
                v-if="
                  ($route.path.indexOf('monitor/message') > -1 ||
                    $route.path.indexOf('monitor/recommended') > -1) &&
                  data.status == 1
                "
                style="
                  color: #fff;
                  background: #f09fa9;
                  border-radius: 4px;
                  font-size: 14px;
                "
                >已下发提示单</span
              >
              <span
                v-if="data.isForward == 1"
                style="
                  color: #fff;
                  background: #ff8000;
                  border-radius: 4px;
                  font-size: 14px;
                "
                >已转发</span
              >

              <div
                v-if="
                  ($route.path.indexOf('monitor/recommended') > -1 || $route.path.indexOf('monitor/SDplatform') > -1) &&
                  data.isRead !== null
                "
                class="readStatus"
                :style="{ background: data.isRead === 1 ? '#8FC9FF' : '' }"
              >
                {{ data.isRead === 1 ? "已读" : "未读" }}
              </div>
              <PlatformIcon :id="data.situation" />
            </template>
            {{
              data.mtitle
                ? removeROrN(data.mtitle)
                : data.mabstract
                ? removeROrN(data.mabstract)
                : removeROrN(data.mcontent)
            }}
            <template v-slot:after>
              <!-- ocr内容展示 -->
              <span
                v-if="
                  (data.mtitle && data.mtitle.indexOf('\\nocr:') > -1) ||
                  (data.mabstract && data.mabstract.indexOf('\\nocr:') > -1) ||
                  (data.mcontent && data.mcontent.indexOf('\\nocr:') > -1)
                "
                style="margin-left: 5px;"
              >
                <svg-icon icon-class="ocr-img" />
                <span
                  v-html="
                    highlightTool.highlightByHitWords(
                      data.mtitle
                        ? removeOcr(data.mtitle)
                        : data.mabstract
                        ? removeOcr(data.mabstract)
                        : removeOcr(data.mcontent),
                      keyword,
                      'highlight0'
                    )
                  "
                ></span>
              </span>
              <!--          相似文章-->
              <!-- <span
                v-if="data.topicNum"
                class="suffix cp"
                @click.stop="openDrawer(1)"
              >
                ({{ data.topicNum }})
              </span> -->
              <!--          操作-->
              <span class="controlsBtn cp" @click.stop="openDrawer(2)"
                >预览</span
              >
              <span class="controlsBtn cp" @click.stop="copyUrl(data.murl)" 
                >复制</span
              >
            </template>
          </v-clamp>
        </template>
        <div class="flex middle">
          <Tag :data="data" />
          <div
            class="editInfo"
            v-if="
              editInfoShow &&
              (data.modifyValues && data.modifyValues.length > 0)
            "
          >
            <span v-html="modifyValues()"></span>
          </div>
        </div>

        <div class="remarksOperations">
          <div class="remarks">
            <div
              v-if="data.mwebsiteName || data.uname"
              :title="handleSource"
              class="ellipsis"
              
            >
              来源：{{ handleSource }}
            </div>
            <div class="ellipsis">
              {{ moment(data.mpublishTime).format("YYYY-MM-DD HH:mm:ss") }}
            </div>
            <div
              class="ellipsis"
              v-if="
                $route.path.indexOf('/main/archiveDetail') > -1 && data.mlocIp
              "
            >
              <svg-icon icon-class="媒体库-ip" style="margin-right: 5px;" />{{
                data.mlocIp
              }}
            </div>

            <!-- 报送单位 -->
            <div
              v-if="
                $route.path.indexOf('/monitor/recommended') > -1 &&
                getReportName(data.reportName)
              "
              :title="data.reportOrgUserName ? data.reportOrgUserName : ''"
              class="ellipsis"
           
            >
              <span>报送单位：</span>
              <span>{{ getReportName(data.reportName) }}</span>
            </div>

            <!-- 预警信息 -->
            <div
              v-if="$route.path.indexOf('/monitor/recommended') > -1"
              :title="
                data.reportOrgAndTime
                  ? '报送时间：' + data.reportOrgAndTime
                  : ''
              "
              class="ellipsis"
            >
              <span>报送时间：</span>
              <span>{{
                moment(data.insertTime).format("MM-DD HH:mm:ss")
              }}</span>
            </div>
            <!-- 是否有联系方式标识 -->
             
            <div v-if="$route.path.indexOf('/monitor/recommended') > -1" style="display: flex; align-items: center;">
              <svg-icon
                v-if="data.hasYinsi == 1" class="ellipsis"  title="有联系方式"
                icon-class="电话"
                style="width: 16px; height: 16px; margin-right: 3px;"
              />
            </div>
            <div v-if="$route.path.indexOf('/monitor/recommended') > -1">
              <svg-icon
                v-if="data.isVideo == '1'"
                :icon-class="data.videoStatus == '1' ? '蓝1' : '灰1'"
                style="width: 14px; height: 14px;"
              />
              <svg-icon
                :icon-class="data.evidenceIds ? '蓝2' : '灰2'"
                style="width: 14px; height: 14px;"
              />
            </div>
            <!-- 弹窗预警 -->
            <div
              v-if="
                $route.path.indexOf('/monitor/recommended') > -1 &&
                data.isEarlyWarning == 1
              "
              class="ellipsis"
              style="display: flex; align-items: center;"
              title="已预警弹窗"
            >
              <svg-icon
                icon-class="预警弹窗"
                style="width: 16px; height: 16px; margin-right: 3px;"
              />
              <span>弹窗预警</span>
            </div>
            <div
              v-if="$route.path.indexOf('monitor/recommended') > -1"
              @click="openDrawer(1)"
            >
               <span
                >相似文章
                <!-- <svg-icon
                  icon-class="loading-xiangsi"
                  v-if="data.sameMsgTotal == '-'"
                ></svg-icon>
                {{ data.sameMsgTotal == "-" ? "" : data.sameMsgTotal }} -->
                </span
              >
              
            </div>
            <div v-if="$route.path.indexOf('monitor/recommended') > -1" @click="openUserDetil(data)">
              <span
                >发布账号：
                {{ data.uname == "" ? "-" : data.uname }}</span
              >
              <div style="display: inline-block;">
                <span class="ui_tab_mei" v-if="data.accountTypeMark=='媒'"></span>
                <span class="ui_tab_shang" v-if="data.accountTypeMark=='商'"></span>
                <span class="ui_tab_zi" v-if="data.accountTypeMark=='自'"></span>
              </div>
            </div>

            <!--          命中关键词-->
            <template v-if="obj">
              <div
                v-if="obj.expKeywords && obj.classifyId == 1"
                class="line"
              ></div>
              <div
                v-if="obj.expKeywords && obj.classifyId == 1"
                :title="getKeywords()"
                class="ellipsis"
                style="width: 400px;"
              >
                命中关键词：{{ getKeywords() }}
              </div>
            </template>
                      </div>
            <slot name="controls" v-if="$route.path.indexOf('monitor/SDplatform') == -1"></slot>
        </div>
      </div>
    </template>
    <template v-else>
      <Spin fix size="small"></Spin>
    </template>
    <div
      class="sb_tips"
      v-if="
        $route.path.indexOf('main/publicOpinionTips') > -1 &&
        openSourceName == '省办提示'
      "
      :class="{ follow: data.tipStatus == 1 }"
    >
      <span>{{
        data.tipStatus == 1 ? "关注" : data.tipStatus == 2 ? "核实" : ""
      }}</span>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import ListControls from "@/components/listControls";
import highlightTool from "trs-tool-highlight";
import Tag from "@/components/tag";
import TextContainer from "./component/textContainer.vue";
import VClamp from "trs-clamp";
import platform from "../../assets/json/situations";
import moment from "moment";

import PlatformIcon from "@/components/platformIcon";

export default {
  data() {
    // 这里存放数据
    return {
      keyword: [],
      platform,
      observer: null, // Intersection Observer 实例
      isRender: false,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    ListControls,
    Tag,
    TextContainer,
    VClamp,
    PlatformIcon,
  },
  props: {
    data: {
      default: null,
    },
    //监听事件的事件信息
    obj: {
      default: null,
    },
    pageNo: {
      default: 1,
    },
    pageSize: {
      default: 10,
    },
    index: {
      default: 1,
    },
    checkKey: {
      default: "mkey",
    },
    keywordArr: {
      default: () => [],
    },
    editInfoShow: {
      default: false,
    },
    hideCheckBox: {
      default: false,
    },
    openSourceName: {
      default: "",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    String.prototype.replaceAll = function (oldSubStr, newSubStr) {
      return this.replace(new RegExp(oldSubStr, "gm"), newSubStr);
    };
  },
  // 方法集合
  methods: {
    openUserDetil(item){
     //新页面打开自媒体详情页
     //校验非空
     if(item.ukey && item.ukey!=""){
      window.open(`/main/archiveDetail?ukey=${item.ukey}&situation=${item.situation}`, '_blank');
     }else{
      window.open(`/main/archiveDetail?mkey=${item.mkey}&situation=${item.situation}&uname=${item.uname}&mwebsiteName=${item.mwebsiteName}`, '_blank');
     }
     
    },
    moment,
    getKeywords() {
      if (!this.data.matchWords || !this.obj.expKeywords) {
        return "";
      }
      const arr = this.obj.expKeywords.replace(/&|\(|\)|\||-/g, ",").split(",");
      const matchWords = this.data.matchWords.replace(/,/g, "");
      let result = [];
      console.log(arr, matchWords);
      arr.forEach((i) => {
        if (matchWords.indexOf(i) != -1 && i) {
          result.push(i);
        }
      });
      return result.toString();
    },
    modifyValues() {
      if (!this.data.modifyValues || this.data.modifyValues.length === 0) {
        return false;
      }
      let html = "";
      this.data.modifyValues.forEach((i) => {
        html += this.highlightChanges(i) + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
      });
      return html;
    },
    highlightChanges(text) {
      // 匹配需要高亮的部分，正则表达式中的 \[ 和 \] 用于匹配中括号
      return text.replace(
        /\["(.*?)"\]/g,
        "<span style='color: #005CC8;'>[$1]</span>"
      );
    },

    // 去除报送单位#号
    getReportName(data) {
      let str = null;
      if (data) {
        str = data.replace(/#/g, ",");
      }
      if (str && str[0] == ",") {
        return str.substring(1, str.length - 1);
      } else {
        return str;
      }
    },
    removeOcr(str) {
      if (str) {
        if (str.indexOf("\\nocr:") > -1) {
          str = str.substring(str.indexOf("\\nocr:"), str.length);
        }
        return str
          .replace(/(\\nocr:)/g, "")
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
    removeROrN(str) {
      if (str) {
        return str
          .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
          .replace(/<[^>]+>/g, "")
          .trim();
      } else {
        return "";
      }
    },
    toDetails() {
      console.log(this.data);
      console.log(this.$route);
      const query = {
        msgKey: this.data.mkey,
        keyword: this.data.matchWords,
        situation: this.data.situation,
        sourcePath: this.$route.path,
        sourceName: this.openSourceName
          ? this.openSourceName
          : this.$route.name,
        moduleOriginName:
          this.$route.meta && this.$route.meta.moduleName
            ? encodeURIComponent(this.$route.meta.moduleName)
            : "",
        moduleName:
          this.$route.meta && this.$route.meta.moduleName
            ? encodeURIComponent(
                this.$route.path.indexOf("/main/monitor/") > -1
                  ? this.$route.meta.moduleName.split("/")[0] + "/正文页"
                  : this.$route.meta.moduleName
              )
            : "",
      };
      this.hideCheckBox ? (query.isQx = 1) : void 0;
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: query,
      });

      window.open(href, "_blank");
    },
    //打开弹窗
    openDrawer(type) {
      this.$emit("drawerChange", this.data, type);
    },
    // 复制链接
    copyUrl(d) {
      // console.log(d);
      // this.copyUrl(d);
      try {
        navigator.clipboard.writeText(d);
        this.$Message.success("信息链接已复制");
      } catch (err) {
        this.$Message.error("无法复制信息链接");
      }
    },
    getKeyword() {
      let hitWords;
      if (this.obj?.expKeywords) {
        let text = this.data.mcontent + this.data.mtitle;
        hitWords = this.highlightTool.getHitWords(text, this.obj.expKeywords);
      }
      this.keyword = [...this.keywordArr];
      if (hitWords) this.keyword.push(...hitWords);

      if (this.data.matchWords) {
        this.keyword.push(this.data.matchWords);
      }
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    highlightTool() {
      return highlightTool;
    },
    handleSource() {
      // let arr = [10, 20, 80, 170];
      // if (arr.includes(this.data.situation)) {
      //   return this.data.uname;
      // } else {
      return this.data.mwebsiteName || this.platform[this.data.situation];
      // }
    },
  },
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    if (
      this.keywordArr.length > 0 ||
      this.obj?.expKeywords ||
      this.obj?.matchWords
    ) {
      this.getKeyword();
    }
    const MonitoringInfoList = this.$refs.MonitoringInfoList;
    if (!MonitoringInfoList) return;
    this.observer = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          this.isRender = true;
          this.observer.disconnect();
        }
      });
    });
    // 选择要观察的元素
    const target = MonitoringInfoList;
    this.observer.observe(target);
  },
  beforeDestroy() {
    // 在组件销毁前移除 Intersection Observer
    if (this.observer) {
      this.observer.disconnect();
    }
  },
};
</script>

<style lang="less" scoped>
.MonitoringInfoList {
  display: flex;
  justify-content: space-between;
  // height: 130px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.16);
  margin-top: 12px;
  padding: 10px;
  position: relative;

  .SerialNum {
    width: 60px;
  }

  .content {
    font-size: 16px;
    width: calc(~"100% - 60px");
    .tc_status {
      position: absolute;
      top: -2px;
      right: 4px;
      text-align: center;
      pointer-events: none;
      .icon_tcstatus {
        font-size: 65px;
      }
    }
    .ellipsis-2 {
      height: 48px;
      position: relative;

      &::after {
        content: "...";
        position: absolute;
        right: 0;
        bottom: 0;
        width: 100px;
        height: 24px;
        background: #fff;
        /* 根据容器背景色调整 */
        pointer-events: none;
        /* 确保伪元素不影响文本选择和点击 */
      }
    }

    .title {
      font-weight: 600;
      line-height: 22px;
      padding-right: 60px;
    }

    .suffix {
      color: #5585ec;
    }

    .controlsBtn {
      height: 18px;
      border: 1px solid #5585ec;
      border-radius: 2px;
      line-height: 18px;
      color: #5585ec;
      font-size: 12px;
      margin-right: 10px;
      text-align: center;
      padding: 0 2px;
    }

    .tag {
      display: flex;
      color: #333333;
      font-size: 12px;
      align-items: center;
      margin: 11px 0 10px;

      & > div {
        line-height: 20px;
        text-align: center;
      }

      .line {
        width: 1px;
        height: 20px;
        background: #999999;
        margin: 0 4px;
      }

      .area {
        width: 64px;
        height: 20px;
        border: 0.5px solid #5585ec;
      }

      .type {
        background-color: #dee7fc;
        padding: 0 7px;
      }

      .emotion {
        padding: 0 4px;
        color: #fff;
      }

      .negative {
        background: #ffbc00;
      }

      .front {
        background: #e93d61;
      }

      .neutral {
        background: #5585ec;
      }

      .edit {
        background-color: #dee7fc;
        padding: 0 4px;
      }
    }

    .remarksOperations {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #666666;
      font-size: 14px;

      .remarks {
        display: flex;

        & > div {
          margin-right: 20px;
        }

        .line {
          width: 0;
          height: 22px;
          border: 1px solid #707070;
        }
      }
      .bbox{
        display: flex;
        flex-direction: row;
      }
    }
  }

  /deep/ .ivu-checkbox-wrapper {
    font-size: 16px;
  }

  /deep/ .ivu-checkbox-inner {
    width: 16px;
    height: 16px;
  }
}

.abstract {
  display: flex;
  align-items: center;
  padding-right: 60px;
  .ellipsis {
    max-width: calc(~"100% - 100px");
  }
}

.middle {
  align-items: center;

  .editInfo {
    margin-left: 50px;
    padding: 0 10px;
    background-color: #fff3f5;
  }
}
.readStatus {
  display: inline-block;
  width: 50px;
  font-size: 14px;
  text-align: center;
  background-color: #ff4d4f;
  color: #fff;
  border-radius: 5px;
  line-height: 20px;
}
.sb_tips {
  width: 34px;
  height: 50px;
  top: 5px;
  right: 0;
  position: absolute;
  color: #fff;
  line-height: 24px;
  text-align: center;
  &::before {
    content: "";
    width: 0;
    height: 0;
    border-top: 25px solid #ec808d;
    border-right: 30px solid #ec808d;
    border-bottom: 24px solid transparent;
    border-left: 30px solid transparent;
    position: absolute;
    top: 0;
    right: 0;
  }
  &.follow {
    &::before {
      border-top: 25px solid #facd91;
      border-right: 30px solid #facd91;
      border-bottom: 24px solid transparent;
      border-left: 30px solid transparent;
    }
  }
  span {
    position: relative;
    z-index: 1;
  }
}
</style>
