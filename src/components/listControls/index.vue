<template>
  <div
    :class="['ListControls', itemMargin ? 'margin' : '']"
    :style="itemMargin ? { '--margin': itemMargin } : {}"
  >
    <div
      v-if="
        $route.path.indexOf('monitor/recommended') > -1 ||
        $route.path.indexOf('monitor/message') > -1 ||
        $route.path.indexOf('main/publicOpinionTips') > -1 ||
        $route.path.indexOf('main/tipsDeatil') > -1 ||
        $route.path.indexOf('monitor/chinaImportanceNews') > -1 ||
        $route.path.indexOf('main/archiveDetail') > -1
      "
      style="display: flex;"
    >
      <template>
        <!--  $route.path.indexOf('monitor/recommended') > -1 || -->
        <template
          v-if="
            ($route.path.indexOf('monitor/message') > -1 && btnStatus.daBiao) ||
            $route.path.indexOf('/monitor/chinaImportanceNews') > -1 ||
            ($route.path.indexOf('main/publicOpinionTips') > -1 &&
              btnStatus.daBiao) ||
            ($route.path.indexOf('main/archiveDetail') > -1 && btnStatus.daBiao)
          "
        >
          <div
            v-if="
              (data.isRecommend !== 1 &&
                $route.path.indexOf('monitor/recommended') == -1) ||
              ($route.path.indexOf('monitor/recommended') > -1 &&
                data.reportId.indexOf(organId) == -1)
            "
            class="item"
            @click="addOrDelRecommendMsg(0)"
          >
            <svg-icon icon-class="打标推送" />
            打标推送
          </div>
          <div
            v-if="
              (data.isRecommend == 1 &&
                $route.path.indexOf('monitor/recommended') == -1) ||
              ($route.path.indexOf('monitor/recommended') > -1 &&
                data.reportId.indexOf(organId) > -1)
            "
            class="item"
            style="border-color: #3ad776; background-color: #d0f7e1;"
          >
            <Icon
              style="color: #18d46b; font-size: 20px;"
              type="md-checkmark"
            />
            已推送
          </div>
        </template>
        <div v-if="btnStatus.handelTips" class="item" @click="creatTips">
          <svg-icon icon-class="榜单-生成提示单" />
          生成提示单
        </div>
        <!-- <div
          class="item"
          @click="showTipsNew"
          v-if="datas.status == 1"
          style="border: 1px solid #f65177; background: #ffdce4"
        >
          <svg-icon icon-class="查看提示单" />
          查看提示单
        </div> -->
      </template>

      <div v-if="btnStatus.createAbstract" class="item" @click="createAbstract">
        <svg-icon icon-class="榜单-生成摘要" />
        编辑摘要
      </div>

      <div
        v-if="
          btnStatus.addMaterial && (datas.isMaterial == 0 || !datas.isMaterial)
        "
        class="item"
        @click="creatMaterial(2)"
      >
        <svg-icon icon-class="榜单-加入素材库" />
        加入要报素材
      </div>

      <div
        v-if="btnStatus.addMaterial && datas.isMaterial == 1"
        class="item"
        style="border: 1px solid #2ae094; background: #c4f8e3;"
      >
        <svg-icon icon-class="素材库-已加入" />
        已加入
      </div>
      <div
        v-if="
          btnStatus.addMaterial &&
          (datas.isPerMaterial == 0 || !datas.isPerMaterial)
        "
        class="item"
        @click="creatMaterial(1)"
      >
        <svg-icon icon-class="榜单-加入素材库" />
        加入个人素材
      </div>

      <div
        v-if="btnStatus.addMaterial && datas.isPerMaterial == 1"
        class="item"
        style="border: 1px solid #2ae094; background: #c4f8e3;"
      >
        <svg-icon icon-class="素材库-已加入" />
        已加入
      </div>

      <!-- 加入事件按钮 -->
      <div
        v-if="btnStatus.joinEvent"
        class="item"
        @click="openJoinEventModal"
      >
        <svg-icon icon-class="事件" />
        加入事件
      </div>

      <div
        v-if="handleOver && this.datas.isFinish != 1"
        class="item"
        @click="creatOver"
      >
        <svg-icon icon-class="弹框-完结" />
        完结提示单
      </div>

      <div v-if="handleOver" class="item" @click="handleDownLoad">
        <svg-icon icon-class="弹框-下载" />
        下载提示单
      </div>
      <div
        v-if="
          btnStatus.warningPush &&
          $route.path.indexOf('/main/publicOpinionTips') == -1
        "
        class="item"
        @click="warningPush"
      >
        <svg-icon icon-class="蓝色三角预警" />
        弹窗提醒
      </div>
      <!-- handleOver暂时判断是否为舆情提示已提示模块 -->
      <div
        v-if="$route.path.indexOf('main/tipsDeatil') > -1"
        style="display: flex;"
      >
        <div
          v-if="this.datas.promptStatus != 5 && this.datas.isFinish != 1"
          class="item"
          @click="creatBack"
        >
          <svg-icon icon-class="弹框-退回" />
          退回提示单
        </div>
        <div class="item" @click="creatInclident">
          <svg-icon icon-class="关联事件" />
          关联事件
        </div>
        <div class="item" @click="creatSms">
          <svg-icon icon-class="短信发送" />
          短信通知
        </div>
      </div>
      <div v-else class="item-point">
        <Icon type="ios-more" v-if="!btnStatus.hidepoint" />
        <div class="point-content" v-if="!btnStatus.hidepoint">
          <div
            v-if="hasPermission('/recommend/sendMessage') && !handleOver"
            class="points"
            @click="sendMessage"
          >
            转发到"群"
          </div>
          <div
            v-if="
              $route.path.indexOf('monitor/recommended') > -1 ||
              ($route.path.indexOf('/main/publicOpinionTips') > -1 &&
                btnStatus.warningPush)
            "
            class="points"
            @click="warningPush"
          >
            弹窗提醒
          </div>
          <div v-if="datas.status == 1" class="points" @click="showTipsNew">
            查看提示单
          </div>
          <div
            v-if="datas.isImportant == 0 && !handleOver"
            class="points"
            @click="importTips(data)"
          >
            重点提示
          </div>
          <div
            v-if="datas.isImportant == 1 && !handleOver"
            class="points"
            @click="importTips(data)"
          >
            取消重点提示
          </div>
          <router-link
            v-if="btnStatus.createEvent"
            :to="
              '/main/eventAnalysis/eventDetails?type=1&msgKey=' +
              datas.mkey +
              '&situation=' +
              datas.situation +
              '&moduleName=' +
              httpHeader()['sys_log_module']
            "
            target="_blank"
          >
            <div class="points" style="color: #333;">创建事件</div>
          </router-link>
          <div
            v-if="btnStatus.GatherEvidence"
            class="points"
            @click="GatherEvidence"
          >
            取证
          </div>
          <div
            v-if="btnStatus.LookEvidence"
            class="points"
            @click="LookEvidence"
          >
            查看取证
          </div>
          <div v-if="btnStatus.delMsg" class="points" @click="delMsg(data)">
            删除
          </div>
          <div
            v-if="
              handleBack &&
              this.datas.promptStatus != 5 &&
              this.datas.isFinish != 1
            "
            class="points"
            @click="creatBack"
          >
            退回提示单
          </div>
          <div
            v-if="btnStatus.tipsTurn && this.datas.isFinish != 1"
            class="points"
            @click="turnTips"
          >
            提示单转办
          </div>
          <div v-if="handleIncident" class="points" @click="creatInclident">
            关联事件
          </div>
          <div v-if="handleBack" class="points" @click="creatSms">短信通知</div>
        </div>
      </div>
    </div>

    <div v-else style="display: flex; flex-wrap: wrap;">
      <template v-if="btnStatus.recommendMsg || btnStatus.warningPush">
        <div
          v-if="data.isRecommend !== 1"
          class="item"
          @click="addOrDelRecommendMsg(0)"
        >
          <svg-icon icon-class="打标推送" />
          打标推送
        </div>
        <div
          v-else
          class="item"
          style="border-color: #3ad776; background-color: #d0f7e1;"
        >
          <Icon style="color: #18d46b; font-size: 20px;" type="md-checkmark" />
          已推送
        </div>
      </template>

      <template v-if="btnStatus.handelTips">
        <div
          v-if="
            $route.query.sourceName == '涉济推荐' ||
            $route.query.sourceName == '搜索结果' ||
            (!datas.promptNum && !$route.query.sourceName) ||
            ($route.path.indexOf('/comprehensiveSearch/searchResults') > -1 &&
              datas.status == 0)
          "
          class="item"
          @click="creatTips"
        >
          <svg-icon icon-class="榜单-生成提示单" />
          生成提示单
        </div>
        <div
          v-if="
            (($route.query.sourceName == '涉济推荐' ||
              $route.query.sourceName == '搜索结果') &&
              datas.status == 1) ||
            (datas.promptNum &&
              !$route.query.sourceName &&
              $route.path.indexOf('/comprehensiveSearch/searchResults') ==
                -1) ||
            ($route.path.indexOf('/comprehensiveSearch/searchResults') > -1 &&
              datas.status == 1)
          "
          class="item"
          style="border: 1px solid #f65177; background: #ffdce4;"
          @click="showTipsNew"
        >
          <svg-icon icon-class="查看提示单" />
          查看提示单
        </div>
      </template>

      <!-- 退回按钮 -->
      <template v-if="handleBack">
        <div v-if="datas.promptStatus == 6" class="item no-item">
          <svg-icon icon-class="弹框-退回灰色" />
          退回
        </div>
        <div v-else class="item" @click="creatBack">
          <svg-icon icon-class="弹框-退回" />
          退回
        </div>
      </template>

      <!-- 完结按钮 -->
      <template v-if="handleOver">
        <div v-if="datas.promptStatus != 6" class="item" @click="creatOver">
          <svg-icon icon-class="弹框-完结" />
          完结
        </div>
        <div v-if="datas.promptStatus == 6" class="item no-item">
          <svg-icon icon-class="弹框-完结灰色" />
          完结
        </div>
      </template>

      <div v-if="btnStatus.createAbstract" class="item" @click="createAbstract">
        <svg-icon icon-class="榜单-生成摘要" />
        编辑摘要
      </div>
      <div
        v-if="
          btnStatus.addMaterial && (datas.isMaterial == 0 || !datas.isMaterial)
        "
        class="item"
        @click="creatMaterial(2)"
      >
        <svg-icon icon-class="榜单-加入素材库" />
        加入要报素材
      </div>
      <div
        v-if="btnStatus.addMaterial && datas.isMaterial == 1"
        class="item"
        style="border: 1px solid #2ae094; background: #c4f8e3;"
      >
        <svg-icon icon-class="素材库-已加入" />
        已加入
      </div>
      <div
        v-if="
          btnStatus.addMaterial &&
          (datas.isPerMaterial == 0 || !datas.isPerMaterial)
        "
        class="item"
        @click="creatMaterial(1)"
      >
        <svg-icon icon-class="榜单-加入素材库" />
        加入个人素材
      </div>
      <div
        v-if="btnStatus.addMaterial && datas.isPerMaterial == 1"
        class="item"
        style="border: 1px solid #2ae094; background: #c4f8e3;"
      >
        <svg-icon icon-class="素材库-已加入" />
        已加入
      </div>
      <!-- 加入事件按钮 -->
      <div
        v-if="btnStatus.joinEvent"
        class="item"
        @click="openJoinEventModal"
      >
        <svg-icon icon-class="事件" />
        加入事件
      </div>

      <div
        v-if="btnStatus.warningPush && $route.query.sourceName == '搜索结果'"
        class="item"
        @click="warningPush"
      >
        <svg-icon icon-class="蓝色三角预警" />
        弹窗提醒
      </div>

      <template v-if="btnStatus.point && $route.query.sourceName == '搜索结果'">
        <div v-if="!datas.promptNum" class="item" @click="importTips(data)">
          <svg-icon icon-class="重点提示" />
          重点提示
        </div>
        <!-- <div
        class="item"
        @click="showTips"
        v-else
        style="border: 1px solid #f65177; background: #ffdce4"
      >
        <svg-icon icon-class="已提示" />
        已提示
      </div> -->
        <div
          v-else-if="datas.promptNum && datas.promptStatus != 6"
          class="item"
          @click="creatTip"
        >
          <svg-icon icon-class="查看提示单-蓝色" />
          查看
        </div>

        <!-- 置灰 -->
        <div
          v-else-if="datas.promptNum && datas.promptStatus == 6"
          class="item no-item"
        >
          <svg-icon icon-class="查看提示单-灰色" />
          查看
        </div>
      </template>

      <router-link
        v-if="btnStatus.createEvent"
        :to="
          datas.mkey
            ? '/main/eventAnalysis/eventDetails?type=1&msgKey=' +
              datas.mkey +
              '&situation=' +
              datas.situation +
              '&moduleName=' +
              httpHeader()['sys_log_module']
            : '/main/eventAnalysis/eventDetails?id=' +
              datas.id +
              '&moduleName=' +
              httpHeader()['sys_log_module']
        "
        target="_blank"
      >
        <div class="item" style="color: #333;">
          <svg-icon icon-class="榜单-创建事件" />
          创建事件
        </div>
      </router-link>
      <div v-if="btnStatus.copyURL" class="item">
        <svg-icon icon-class="榜单-创建事件" />
        复制链接
      </div>
      <div
        v-if="btnStatus.JoinSLDatabase"
        :class="[
          'item',
          data.jcczStatus &&
          JSON.parse(data.jcczStatus).includes('JoinSLDatabase')
            ? 'disabled'
            : '',
        ]"
        @click="openModalStatus('JoinSLDatabase')"
      >
        <svg-icon icon-class="列表-文件夹" />
        加入到涉鲁信息库
      </div>
      <div
        v-if="btnStatus.JoinSZDatabase"
        :class="[
          'item',
          data.jcczStatus &&
          JSON.parse(data.jcczStatus).includes('JoinSZDatabase')
            ? 'disabled'
            : '',
        ]"
        @click="openModalStatus('JoinSZDatabase')"
      >
        <svg-icon icon-class="列表-文件夹" />
        加入到涉政信息库
      </div>
      <div v-if="btnStatus.GatherEvidence" class="item" @click="GatherEvidence">
        <div v-if="datas.isEvidence === 0">
          <svg-icon icon-class="重点提示-取证" />
          取证
        </div>
        <div v-if="datas.isEvidence === 1">
          <svg-icon icon-class="重点提示-取证灰色" />
          取证
        </div>
      </div>

      <div v-if="btnStatus.LookEvidence" class="item" @click="LookEvidence">
        <svg-icon icon-class="弹框-查看" />
        查看取证
      </div>
      <div
        v-if="btnStatus.relevanceEvent"
        class="item"
        @click="openRelevanceEvent"
      >
        <svg-icon icon-class="弹框-查看" />
        关联事件
      </div>
      <div
        class="item"
        v-if="btnStatus.detectionStatus"
        @click="batchDetection"
      >
        <svg-icon icon-class="探测 -蓝" />
        探测
      </div>
      <div
        class="item"
        v-if="btnStatus.collect"
        @click="collect"
      >
        <svg-icon icon-class="收藏蓝" />
        {{ datas.isCollect == 0 ? "收藏" : "取消收藏" }}
      </div>
      <Poptip
        v-if="btnStatus.delMsg && $route.query.sourceName == '搜索结果'"
        confirm
        title="确认删除该信息吗?"
        transfer
        @on-ok="delMsg(data)"
      >
        <div class="item">
          <svg-icon icon-class="榜单-删除" />
          删除
        </div>
      </Poptip>

      <!-- 设为精选按钮 设为一般按钮 -->
      <Poptip
        v-if="btnStatus.handpick"
        :title="datas.isSelection == 0 ? '是否设为精选？' : '是否设为一般？'"
        confirm
        transfer
        @on-ok="handlePick(data)"
      >
        <div v-if="datas.isSelection == 0" class="item">
          <svg-icon icon-class="信息库-设为精选" />
          设为精选
        </div>

        <div v-if="datas.isSelection == 1" class="item">
          <svg-icon icon-class="信息库-取消精选" />
          设为一般
        </div>
      </Poptip>

      <!-- 关联事件 -->
      <template v-if="handleIncident">
        <div class="item" @click="creatInclident">
          <svg-icon icon-class="重点提示-创建事件" />
          关联事件
        </div>
      </template>

      <!-- 下载按钮 -->
      <template v-if="btnStatus.downLoad">
        <div class="item" @click="handleDownLoad">
          <svg-icon icon-class="弹框-下载" />
          下载
        </div>
      </template>

      <div class="item-point">
        <Icon type="ios-more" v-if="!btnStatus.hidepoint" />
        <div class="point-content" v-if="!btnStatus.hidepoint">
          <div
            class="points"
            @click="senSDT"
            v-if="account.includes(currentAccount)"
          >
            <!-- <svg-icon icon-class="山东通" /> -->
            转发到山东通
          </div>
          <Poptip
            v-if="
              $route.query.sourceName == '涉济推荐' ||
              $route.query.sourceName == '专题监测' ||
              $route.query.sourceName == '搜索结果'
            "
            confirm
            title="确认提示该信息吗?"
            transfer
            @on-ok="setProvincOfficeTips(1)"
            :disabled="data.tipStatus == 1 || data.tipStatus == 3"
          >
            <div
              class="points"
              :class="{ disabled: data.tipStatus == 1 || data.tipStatus == 3 }"
            >
              {{
                data.tipStatus == 1 || data.tipStatus == 3
                  ? "已提示关注"
                  : "省办提示关注"
              }}
            </div>
          </Poptip>
          <Poptip
            v-if="
              $route.query.sourceName == '涉济推荐' ||
              $route.query.sourceName == '专题监测' ||
              $route.query.sourceName == '搜索结果'
            "
            confirm
            title="确认提示该信息吗?"
            transfer
            @on-ok="setProvincOfficeTips(2)"
            :disabled="data.tipStatus == 2 || data.tipStatus == 3"
          >
            <div
              class="points"
              :class="{ disabled: data.tipStatus == 2 || data.tipStatus == 3 }"
            >
              {{
                data.tipStatus == 2 || data.tipStatus == 3
                  ? "已提示核实"
                  : "省办提示核实"
              }}
            </div>
          </Poptip>

          <div
            v-if="
              btnStatus.warningPush && $route.query.sourceName != '搜索结果'
            "
            class="points"
            @click="warningPush"
          >
            弹窗提醒
          </div>
          <template
            v-if="btnStatus.point && $route.query.sourceName != '搜索结果'"
          >
            <div
              v-if="!datas.promptNum"
              class="points"
              @click="importTips(data)"
            >
              重点提示
            </div>
            <div
              v-else-if="datas.promptNum && datas.promptStatus != 6"
              class="points"
              @click="creatTip"
            >
              查看
            </div>
            <!-- 置灰 -->
            <div
              v-else-if="datas.promptNum && datas.promptStatus == 6"
              class="points"
            >
              查看
            </div>
          </template>
          <div class="points" @click="createJointDisposal">
            联动处置
          </div>
          
          <div
            v-if="btnStatus.delMsg && $route.query.sourceName != '搜索结果'"
            class="points"
            @click="delMsg(data)"
          >
            删除
          </div>
        </div>
      </div>
    </div>

    <Drawer v-model="modalStatus" :closable="false" width="730">
      <component
        :is="componentId"
        :data="data"
        :updateStatus="updateStatus"
        @close="this.modalStatus = false"
      />
      <div slot="footer"></div>
    </Drawer>
    <ImgPreview
      v-if="ImgPreviewStatus"
      :imgList="imgList"
      @close="ImgPreviewStatus = false"
    />

    <!-- 加入事件弹窗 -->
    <JoinEventModal
      v-model="joinEventModalVisible"
      :material-info="datas"
      :data="data"
      @on-success="handleJoinEventSuccess"
      @refresh-detail="handleRefreshDetail"
    />
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import materials from "../../views/main/publicOpinionTips/components/materials.vue";
import tips from "./components/tips.vue"; //查看舆情提示单界面
import moreTipsnew from "./components/moreTips.vue"; //修改查看舆情提示单界面
import yqtips from "./components/yqtips.vue"; //生成舆情提示单界面
import inclidents from "./components/inclident.vue"; //关联事件界面
import backs from "./components/back.vue"; //退回事件界面
import sends from "./components/messages.vue"; //信息发送界面
import overs from "./components/over.vue"; //完结界面
import senSDTModal from "./components/senSdt.vue"; //转发到山东通
import JoinEventModal from "../JoinEventModal/index.vue"; //加入事件弹窗
import platformList from "@/assets/json/situations.json";
import fileDownload from "js-file-download"; // import JoinSLDatabase from "./components/joinSLDatabase.vue" ; //涉鲁信息库
// import JoinSZDatabase from "./components/joinSZDatabase.vue"; //涉政信息库
import JoinSLDatabase from "@/views/main/monitor/luInformation/components/modals.vue"; //涉鲁信息库
import JoinSZDatabase from "@/views/main/monitor/zhengInformation/components/modals.vue"; //涉政信息库
import { mapState } from "pinia";
import { useDomainStore } from "@/stores/domainList";
import unitList from "@/assets/json/jiDependency.json";
import moment from "moment";
import ImgPreview from "@/components/imgPreview/index.vue";

const pathList = {
  "/main/comprehensiveSearch/searchResults": {
    moduleType: "1",
    moduleSecondType: "2",
  }, //搜索结果
  "/main/shandongMonitor/recommended": { type: 2 }, //涉鲁推荐
  "/main/shandongMonitor/politicalRecommended": { type: 3 }, //涉政推荐
  "/main/monitor/recommended": {
    moduleType: "1",
    moduleSecondType: "1",
    type: 1,
  }, //涉济信息 涉济推荐模块
  "/main/monitor/message": { moduleType: "1", moduleSecondType: "2" }, //涉济信息 信息监测模块
  "/main/monitor/shortVideo": { moduleType: "1", moduleSecondType: "3" }, //涉济信息 短视频监测模块
  "/main/monitor/jiNanHotList": { moduleType: "1", moduleSecondType: "4" }, //涉济信息 涉济热榜模块
  "/main/monitor/chinaHotList": { moduleType: "1", moduleSecondType: "5" }, //涉济信息 国内热榜模块
  "/main/monitor/jiNansubmission": { moduleType: "1", moduleSecondType: "6" }, //涉济信息 涉济报送模块
  // "": { moduleType: "", moduleSecondType: "" },
  // "": { moduleType: "", moduleSecondType: "" },
};

export default {
  data() {
    // 这里存放数据
    return {
      organId: "#" + localStorage.getItem("organId") + "#",
      pathList,
      yqFlagNew: false,
      yqFlag: false, //查看舆情提示单
      datas: {}, //接收列表传的值
      tipsData: {}, //查看提示单
      tipsList: [],
      tipsIndex: 0,
      modalTitle: "", //弹框抬头
      platformList,
      params: {},

      modalStatus: false, // 弹窗开关,
      modalList: {
        JoinSLDatabase: "加入到涉鲁信息库",
        JoinSZDatabase: "加入到涉政信息库",
      },
      componentId: "",
      unitList,
      selectFolderId: null,
      dataId: 0,
      ImgPreviewStatus: false,
      imgList: null,
      account: [
        "qianhongguo1",
        "chensu3",
        "chenzhe",
        "caoyang",
        "Yaoxuqian",
        "test",
        "xupengxiang",
      ],
      currentAccount: localStorage.getItem("userAccount"),
      joinEventModalVisible: false, // 加入事件弹窗显示状态
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    ImgPreview,
    materials,
    tips,
    yqtips,
    JoinSLDatabase,
    JoinSZDatabase,
    inclidents,
    backs,
    sends,
    overs,
    moreTipsnew,
    JoinEventModal,
    tipsParams: false,
  },
  props: {
    data: {
      default: () => {},
    },
    btnStatus: {
      default: () => {
        return {
          //重点提示
          point: false,
          // 重点提示打标推送
          daBiao: false,
          //提示单按钮
          handelTips: false,
          tipsTurn: false,
          //推荐按钮
          recommendMsg: false,
          //生成摘要
          createAbstract: false,
          //创建事件按钮
          createEvent: false,
          //加入素材库按钮
          addMaterial: false,
          // 复制链接
          copyURL: false,
          //加入涉鲁信息库
          JoinSLDatabase: false,
          //加入涉政信息库
          JoinSZDatabase: false,
          // 删除
          delMsg: null,
          // 下载
          downLoad: false,
          //精选
          handpick: false,
          //预警推送
          warningPush: false,
          //取证
          GatherEvidence: false,
          // 查看取证
          LookEvidence: false,
          //加入事件
          joinEvent: false,
        };
      },
    },
    //删除事件
    delMsg: {
      default: () => {},
    },
    updateStatus: {
      default: () => {},
    },
    handlePick: {
      default: null,
    },
    handleIncident: {
      default: null,
    },
    handleBack: {
      default: null,
    },
    handleOver: {
      default: null,
    },
    itemMargin: {
      default: false,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    collect() {
      if (this.datas.isCollect == 1) {
        this.$http
          .get("/msgDeal/cancelCollect", { params: { mkey: this.datas.mkey } })
          .then((res) => {
            this.$Message.success("取消收藏成功");
            this.datas.isCollect = 0;
          });
      } else {
        this.$http
          .post(
            "/msgDeal/addCollectMsg",
            // "/prompt/collectPrompt",
            {
              mkey: this.datas.mkey,
              mpublishTime: this.datas.mpublishTime,
              msentiment: this.datas.msentiment, //情感倾向
              mtitle: this.datas.mtitle,
              murl: this.datas.murl,
              mwebsiteName: this.datas.mwebsiteName, //网站名
              situation: this.datas.situation,
              uname: this.datas.uname, //用户名
            },
            // { emulateJSON: true }
          )
          .then((res) => {
            console.log(res);
            this.datas.isCollect = 1;
            this.$Message.success("收藏成功");
          });
      }
    },
    // 打开加入事件弹窗
    openJoinEventModal() {
      this.joinEventModalVisible = true;
    },
    // 加入事件成功回调
    handleJoinEventSuccess(eventIds) {
      // this.$Message.success(`成功加入 ${eventIds.length} 个事件`);
      // 可以在这里添加其他成功后的处理逻辑
    },
    // 刷新详情页数据
    handleRefreshDetail() {
      // 通知父组件刷新详情数据
      if (this.updateStatus && typeof this.updateStatus === 'function') {
        this.updateStatus();
      }
      // 如果父组件有其他刷新方法，也可以通过 emit 触发
      this.$emit('refresh-detail');
    },
    handleRelevance(d) {
      let params = {
        hotRankId: this.datas.id,
        eventIds: d.toString(),
      };
      this.$http
        .get("/hotrank/updateHotRankRelation", { params })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
          } else {
            this.$Message.warning(res.body.message);
          }
        });
    },
    creatMoreTip() {
      this.$modal.show({
        component: moreTipsnew,
        componentProps: {
          list: this.tipsList,
        },
        componentEvents: {},
      });
    },
    creatTip() {
      this.showTips();
      this.$modal.show({
        component: tips,
        componentProps: {
          dataItem: this.tipsData,
        },
        componentEvents: {},
      });
    },
    // 加入素材库弹框
    creatMaterial(type) {
      this.$modal.show({
        component: materials,
        componentProps: {
          data: this.datas,
          that: this,
          type,
        },
        componentEvents: {
          close: this.closeSms,
          handleMaterial: this.addMaterial,
        },
        title: type == 1 ? "个人素材添加" : "要报素材添加", // 传递标题
      });
    },
    senSDT() {
      this.$modal.show({
        component: senSDTModal,
        componentProps: {
          data: this.datas,
          that: this,
        },
        componentEvents: {
          confirm: this.handleSendSDT,
          close: this.closeSms,
        },
        title: "转发到山东通", // 传递标题
      });
    },
    handleSendSDT(params) {
      if (params.length == 0) {
        this.$Message.warning("至少选择一个用户！");
        return;
      }
      this.$http
        .post("/forward/addForwardLog", {
          mkey: this.datas.mkey,
          forwardLogList: [...params],
        })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success("信息已转发至山东通");
          } else {
            this.$Message.warning(res.body.message);
          }
          this.closeSms();
        });
    },
    // 关联提示单弹框
    openRelevanceEvent() {
      this.$modal.show({
        component: inclidents,
        componentProps: {
          data: this.datas,
          that: this,
        },
        componentEvents: {
          close: this.closeSms,
          handleRelevance: this.handleRelevance,
        },
        title: "关联事件", // 传递标题
      });
    },
    // 关联提示单弹框
    creatInclident() {
      this.$modal.show({
        component: inclidents,
        componentProps: {
          data: this.datas,
          that: this,
        },
        componentEvents: {
          close: this.closeSms,
          handleInclidents: this.associationPrompt,
        },
        title: "关联事件", // 传递标题
      });
    },
    // 退回弹框
    creatBack() {
      if (this.datas.promptStatus == 5) {
        return this.$Message.error("该提示单已退回，请勿重新操作");
      } else if ([2, 3, 6].includes(this.datas.promptStatus)) {
        return this.$Message.error("当前状态不允许退回提示单");
      }
      this.$modal.show({
        component: backs,
        componentProps: {
          data: this.datas,
        },
        componentEvents: {
          close: this.closeSms,
          handleBack: this.handleBack,
        },
        title: "提示单退回", // 传递标题
      });
    },

    // 完结弹框
    creatOver() {
      this.$modal.show({
        component: overs,
        componentProps: {
          datas: this.datas,
        },
        componentEvents: {
          close: this.closeSms,
          handleOver: this.handleOver,
        },
        title: "完结确认", // 传递标题
      });
    },
    // 短信提示单弹框
    creatSms() {
      this.$modal.show({
        component: sends,
        componentProps: {
          datas: this.datas,
          that: this,
        },
        componentEvents: {
          close: this.closeSms,
          handleSms: this.handleSends,
        },
        title: "短信通知", // 传递标题
      });
    },
    closeSms() {
      this.$modal.hide();
    },
    //取证
    GatherEvidence() {
      if (this.datas.isEvidence === 1) {
        this.$Message.warning("信息正在取证中，请稍后查看");
        return false;
      }
      let type = 1;
      if (
        this.pathList[this.$route.query.sourcePath] &&
        this.pathList[this.$route.query.sourcePath].type
      ) {
        type = this.pathList[this.$route.query.sourcePath].type;
      } else if (
        this.pathList[this.$route.query.path] &&
        this.pathList[this.$route.query.path].type
      ) {
        type = this.pathList[this.$route.query.sourcePath].type;
      }
      if (!type) {
        type = 1;
      }
      let params = {
        mkey: this.data.mkey||this.data.msgId, //数据mkey
        type: type, //1 涉济，2 涉鲁，3涉政
      };

      this.$http
        .post("/recommend/obtainEvidenceMsg", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.warning("信息正在取证中，请稍后查看");
            this.datas.isEvidence = 1;
          } else {
            this.$Message.error(res.body.message);
          }
        });
    },
    // 查看取证
    LookEvidence() {
      const LookEvidence = () => import("@/components/LookEvidence");
      if (
        (this.$route.meta && this.$route.meta.moduleName) ||
        this.$route.query.moduleName
      ) {
        const sys_log_module = this.$route.query.moduleName
          ? decodeURIComponent(this.$route.query.moduleName)
          : this.$route.meta.moduleName;
        this.getLog(
          sys_log_module,
          "查看取证/" + this.datas.mtitle || this.datas.msgTitle,//提示单 msgTitle
          this.$route.query.msgKey ? this.$route.query.msgKey : ""
        );
      }
      this.$modal.show({
        component: LookEvidence,
        componentProps: {
          data: this.datas,
          that: this,
          pathList: this.pathList,
        },
        componentEvents: {
          // closes: this.tipCancelModel,
          blowUp: this.blowUp,
        },
        title: "查看取证", // 传递标题
        // y: 300,
      });
    },
    //图片放大
    blowUp(imgList) {
      this.imgList = imgList;
      this.ImgPreviewStatus = true;
    },
    turnTips() {
      this.$modal.show({
        component: yqtips,
        componentProps: {
          data: this.datas,
          that: this,
          pathList: this.pathList,
          title: "提示单转办",
        },
        componentEvents: {
          closes: this.tipCancelModel,
          handleYq: this.handleYq,
        },
        title: "提示单转办", // 传递标题
        // y: 300,
      });
    },
    setTipsParams(d) {
      this.datas.promptNum = d.promptNum;
      this.datas.mabstract = d.msgAbstract;
      this.datas.murl = d.msgContentUrl;
      this.datas.suggest = d.suggest;
      this.datas.sendOrgId = d.sendOrgId;
      this.datas.yqReceiver = d.sendUserId;
      this.datas.sendPhone = d.sendPhone;
      this.datas.promptUserId = d.promptUserId;
      this.datas.promptUserPhone = d.promptUserPhone;
      this.datas.receiverList = d.receiverList;
    },
    async creatTips() {
      if (!this.tipsParams) {
        const promptRes = await this.$http.get("/prompt/getPrompt", {
          params: {
            msgId: this.datas.mkey,
          },
        });
        if (promptRes.body.status == 0 && promptRes.body.data) {
          this.setTipsParams(promptRes.body.data);
        }

        this.tipsParams = true;
      }
      if (this.datas.status && this.datas.status == 1) {
        this.getNewtips().then((res) => {
          let result = res.body;
          // this.datas = result.data.newPrompt;
          this.datas.msgAbstract =
            result.data.newPrompt && result.data.newPrompt.msgAbstract
              ? result.data.newPrompt.msgAbstract
              : "";
          this.datas.msgContentUrl =
            result.data.newPrompt && result.data.newPrompt.msgContentUrl
              ? result.data.newPrompt.msgContentUrl
              : "";
        });
      }
      this.$modal.show({
        component: yqtips,
        componentProps: {
          data: this.datas,
          that: this,
          pathList: this.pathList,
        },
        isOverlay: true,
        componentEvents: {
          closes: this.tipCancelModel,
          handleYq: this.handleYq,
        },
        title: "生成提示单", // 传递标题
        // y: 300,
      });
    },
    tipCancelModel(d) {
      this.datas.promptNum = d.promptNum;
      this.datas.mabstract = d.msgAbstract;
      this.datas.murl = d.msgContentUrl;
      this.datas.suggest = d.suggest;
      this.datas.sendOrgId = d.sendOrgId;
      this.datas.yqReceiver = d.yqReceiver;
      this.datas.sendPhone = d.sendPhone;
      this.datas.promptUserId = d.promptUserId;
      this.datas.promptUserPhone = d.promptUserPhone;
      this.datas.receiverList = d.receiverList;
      this.handleStorePrompt(d);
      this.$modal.hide();
    },
    createAbstract() {
      const editAbstract = () => import("@/components/model/editAbstract");
      this.$modal.show({
        component: editAbstract,
        componentProps: { data: this.datas, that: this },
        componentEvents: { close: this.cancelModel },
        title: "信息摘要", // 传递标题
        // y: 300,
      });
    },
    cancelModel(d) {
      this.$emit("editAbstract", d, this.data);
      this.datas.abstract = d;
      this.$modal.hide();
    },
    moment,
    getStatus(type) {
      let params = {
        promptMsgId: this.datas.promptMsgId, //提示单主键
        type: type, //5:退回,6:完结,8:加入素材库,10:短信通知
      };
      this.$http
        .post("/prompt/updatePromptStatus", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.status === 0) {
          }
        });
    },
    //发送短信
    handleSends(data, type) {
      console.log("发送",data)
      let newData = {};
      if (data) {
        newData = data;
      }
      let params = {
        smsContent: newData.smsContent, //短信内容
        isSendSms: newData.sendFlags, //  是否短信同步下达提示单 0:否,1:是
        promptNum: newData.promptNum, // 提示单编号
        isSendUserSms: newData.sendFlagPeoples, // 是否发送至本人 0:否,1:是
        sendUserPhone:
          newData.sendFlagPeoples == "1" ? newData.promptUserPhone : null, // 本人手机号
      };
      this.$http
        .post("/smsSend/promptSms", params, { emulateJSON: true })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
            this.closeSms();
            if (type == 10) {
              this.getStatus(10);
            }
          }
        });
    },
    cancelItem(d, type) {
      let params = {
        mkey: d, //数据mkey 可以多个
        type: type, //修改类别 1重点提示/取消重点提示 2生成提示单
      };
      this.$http
        .post("/recommend/updateMsgStatus", params, { emulateJSON: true })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
          }
        });
    },
    // id拼接
    getId(name, data) {
      let list = [];
      if (!name || !data) {
        return "";
      }
      let nameList = name.replace(/[\[\]"]/g, "").split(",");
      data.forEach((v) => {
        if (nameList.includes(v.name)) {
          list.push(v.id);
        }
        if (v.children && v.children.length > 0) {
          v.children.forEach((c) => {
            if (nameList.includes(c.name)) {
              list.push(c.id);
            }
          });
        }
      });
      return list.length > 0 ? list.toString() : "";
    },
    importTips(d) {
      let url = "/important/prompt/insertOrDeleteMsg";
      console.log(
        this.pathList[this.$route.path],
        "this.pathList[this.$route.path]"
      );
      let params = {
        moduleType: this.$route.query.sourcePath
          ? this.pathList[this.$route.query.sourcePath].moduleType
          : this.pathList[this.$route.path].moduleType
          ? this.pathList[this.$route.path].moduleType
          : this.datas.moduleType, //来源模块类型（1:涉济监测,2:涉鲁涉政监测）
        moduleSecondType: this.$route.query.sourcePath
          ? this.pathList[this.$route.query.sourcePath].moduleSecondType
          : this.pathList[this.$route.path].moduleSecondType
          ? this.pathList[this.$route.path].moduleSecondType
          : this.datas.moduleSecondType, // 来源二级模块类型（1:涉济推荐,2:涉济信息,3:涉济短视频,4:涉济热榜,5:国内热榜,6:涉济报送）
        msgId: d.mkey || d.msgId,
        msgType: d.type || d.mtype, //信息类型（1:文本,2:视频）
        publishTime:
          moment(d.mpublishTime).format("YYYY-MM-DD HH:mm:ss") ||
          moment(d.publishTime).format("YYYY-MM-DD HH:mm:ss"), // 数据发布时间
        classifyId: this.getId(d.scopeArea, this.domainList), // 信息对应领域id','拼接
        emotionType: d.msentiment, // 情感倾向(-1:负.0:中.1:正)
        locationId: this.getId(d.marea, this.unitList), // 信息对应的地域id拼接','拼接
        msgSiution: d.situation || d.msgSiution, // 平台号
        msgUname: d.uname || d.msgUname, // 作者名
        msgTitle: d.mtitle || d.msgTitle, // 标题
        msgContent: d.mcontent ? d.mcontent : d.msgContent ? d.msgContent : "", // 正文内容
        msgAbstract: d.mabstract || d.msgAbstract, //  摘要
        mwebsiteName: d.mwebsiteName || d.mwebsiteName, // 网站名
      };
      this.$http.post(url, params, { emulateJSON: true }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          if (params.moduleType == 1 && params.moduleSecondType == 1) {
            this.$Message.success(result.message);
            this.cancelItem(params.msgId, 1);
            this.datas.isImportant = !this.datas.isImportant;
          }
        }
      });
    },
    // 预警推送
    warningPush() {
      let params = {
        mkey: this.data.mkey,
        mtitle: this.data.mtitle,
        mabstract: this.data.mabstract,
        msentiment: this.data.msentiment,
        situation: this.data.situation,
        mpublishTime: moment(this.data.mpublishTime),
        type: 1,
      };
      this.$http.post("/recommend/pushMsg", params, {}).then((res) => {
        this.$Message.success(res.body.message);
      });
    },
    // 下载
    handleDownLoad() {
      let formData = new FormData();
      formData.append("msgNums", this.data.promptNum);
      this.$http
        .post("/prompt/exportByNums", formData, {
          responseType: "blob",
        })
        .then((res) => {
          const disposition = res.headers.get("Content-Disposition");
          let filename = "downloaded_file";
          if (disposition && disposition.indexOf("attachment") !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
              filename = matches[1].replace(/['"]/g, "");
              filename = decodeURIComponent(escape(filename)); // 解码文件名
            }
          }
          fileDownload(res.body, filename);
        });
    },
    openModalStatus(key) {
      if (
        key == "JoinSLDatabase" &&
        this.data.jcczStatus &&
        JSON.parse(this.data.jcczStatus).includes(key)
      ) {
        return;
      }
      if (
        key == "JoinSZDatabase" &&
        this.data.jcczStatus &&
        JSON.parse(this.data.jcczStatus).includes(key)
      ) {
        return;
      }
      this.componentId = key;
      this.modalStatus = true;
    },
    getNewtips() {
      let params = {
        msgKey: this.datas.mkey,
      };
      return this.$http.get("/prompt/findPromptByMsgKey", { params });
    },
    //新增多个提示单接口
    showTipsNew() {
      this.getNewtips().then((res) => {
        let result = res.body;
        this.tipsList = result.data.data;
        this.creatMoreTip();
      });
    },

    // 查看舆情提示单
    showTips() {
      let params = {
        msgNum:
          this.params.promptNum && this.datas.status == 1
            ? this.params.promptNum
            : this.datas.promptNum,
      };
      this.$http
        .post("/prompt/onlinePreviewByNum", params, { emulateJSON: true })
        .then((res) => {
          let result = res.body;
          this.tipsData = result.data;
          this.yqFlag = true;
        });
    },
    // 推荐
    addOrDelRecommendMsg(type) {
      let params = {};
      if (
        this.$route.path.indexOf("main/publicOpinionTips") > -1 &&
        this.btnStatus.warningPush
      ) {
        this.datas = this.getContent(this.data);
        console.log(this.datas, "this.datas 11111");
      }
      console.log(this.datas, "this.datas 111112222");
      if (type == 0) {
        //推荐
        params = {
          mkey: this.datas.mkey,
          mtitle: this.datas.mtitle,
          mabstract: this.datas.mabstract
            ? this.datas.mabstract
            : this.datas.mcontent
            ? this.datas.mcontent.substring(0, 1000)
            : null,
          murl: this.datas.murl,
          msentiment: this.datas.msentiment,
          mwebsiteName: this.datas.mwebsiteName,
          uname: this.datas.uname,
          ukey: this.datas.ukey,
          situation: this.datas.situation,
          readCnt: this.datas.readCnt ? this.datas.readCnt : 0,
          replyCnt: this.datas.replyCnt,
          replyForwardCnt: this.datas.replyForwardCnt,
          likeCnt: this.datas.likeCnt,
          marea: this.datas.marea,
          scopeArea: this.datas.scopeArea,
          type: 1, //1 涉济，2 涉鲁，3涉政
          mpublishTime: moment(this.datas.mpublishTime).format(
            "YYYY-MM-DD HH:mm:ss"
          ),
          jcczStatus: this.datas.blacklist,
          promptNum: this.$refs.refTips ? this.$refs.refTips.promptNum : "",
        };
      } else {
        //取消推荐
        params = {
          mkey: this.datas.mkey,
        };
      }

      this.$http
        .post("/recommend/addOrDelMsg", params, { emulateJSON: true })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
            if (type == 0) {
              if (this.$route.path.indexOf("monitor/recommended") > -1) {
                this.data.reportName += localStorage.getItem("organName") + "#";
                this.data.reportId += localStorage.getItem("organId") + "#";
                if (this.data.reportOrgUserName.indexOf("推送") > -1) {
                  this.data.reportOrgUserName = this.data.reportOrgUserName.substring(
                    0,
                    this.data.reportOrgUserName.indexOf("推送")
                  );
                }
                this.data.reportOrgUserName +=
                  (this.data.reportOrgUserName ? "、" : "") +
                  localStorage.getItem("organName") +
                  "（" +
                  localStorage.getItem("departmentName") +
                  "）推送";
                this.data.reportOrgAndTime +=
                  (this.data.reportOrgAndTime ? "、" : "") +
                  localStorage.getItem("organName") +
                  "（" +
                  moment(new Date()).format("YYYY-MM-DD HH:mm:ss") +
                  "）";
              } else {
                this.data.isRecommend = 1;
              }

              this.$Message.success("推荐成功");
            } else {
              this.$Message.success("取消推荐成功");
            }
          } else {
            this.$Message.error(result.message);
          }
        });
    },

    associationPrompt(data) {
      // let data = this.$refs.inclidents.checkAllGroup;
      let params = {
        promptId: this.data.promptMsgId,
        eventIds: data.toString(),
      };
      this.$http
        .post("/prompt/updatePromptRelationEvent", params, {
          emulateJSON: true,
        })
        .then((res) => {
          console.log(res);
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
          } else {
            this.$Message.error(res.body.message);
          }
        });
    },
    // 地域及领域传值
    getNumber(data, type) {
      let newList = [];
      let list = JSON.parse(data);
      // type == 1 为领域传值  type == 2 为地域传值
      if (type == 1) {
        this.domainList.forEach((v) => {
          if (list.indexOf(v.name) > -1) {
            newList.push(v.id);
          }
          if (v.children && v.children.length > 0) {
            v.children.forEach((k) => {
              if (list.includes(k.name)) {
                newList.push(k.id);
              }
            });
          }
        });
      } else if (type == 2) {
        unitList.forEach((u) => {
          if (list.indexOf(u.name) > -1) {
            newList.push(u.id);
          }
        });
      }
      return newList.toString();
    },
    // 添加入素材库
    addMaterial(val, type, folderId) {
      let params = {
        year: val.year ? val.year : 2024,
        // msgPeriods: val.msgPeriods,
        msgTitle: val.msgTitle || val.mtitle,
        msgAbstract: val.msgAbstract || val.mabstract,
        msgLocation:
          val.locationId ||
          val.msgLocation ||
          (val.marea ? this.getNumber(val.marea, 2) : ""),
        msgLevel: val.msgLevel ? val.msgLevel : 0,
        // msgStatus: val.msgStatus,
        msgEmotion:
          val.msgEmotion || val.msentiment
            ? val.msgEmotion || val.msentiment
            : null,
        msgClassify:
          val.classifyId ||
          val.msgClassify ||
          (val.scopeArea ? this.getNumber(val.scopeArea, 1) : ""),
        materialType: type,
        msgUrl: val.murl || val.msgContentUrl,
        msgSiution: val.situation || val.msgSiution,
        msgUname: val.uname ? val.uname : val.mwebsiteName,
        msgKey: val.mkey || val.msgId,
        msgTime: moment(val.mpublishTime || val.publishTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        classifyId: type === 1 ? folderId : null,
      };
      console.log(params, "params");
      if (this.$route.path.indexOf("/main/publicOpinionTips") > -1) {
        params.feedbackContent = this.datas.backContent;
        this.getAddMaterial(params, type);
        console.log("提示单");
      } else {
        if (this.data.status && this.data.status == 1) {
          let list = [];
          this.getNewtips().then((res) => {
            let result = res.body;
            list = result.data.data ? result.data.data : []; //list[list.length -1].backContent
            params.feedbackContent = list[list.length - 1].backContent
              ? list[list.length - 1].backContent
              : "";
            this.getAddMaterial(params, type);
          });
          console.log("信息已下发");
        } else {
          if (this.datas.moduleType && this.datas.moduleSecondType) {
            params.feedbackContent = this.datas.backContent;
          }
          this.getAddMaterial(params, type);
        }
      }
    },
    getAddMaterial(params, type) {
      this.$http
        .post("/personal/addMaterial", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success(res.body.message);
            type == 2
              ? (this.datas.isMaterial = 1)
              : (this.datas.isPerMaterial = 1);
          } else {
            this.$Message.error(res.body.message);
          }
        });
    },
    handleYqContent(val) {
      this.params = val;
    },
    validatePhoneNumber(phoneNumber) {
      const regex = /^1[3-9]\d{9}$/;
      return regex.test(phoneNumber);
    },
    // 舆情提示单生成
    handleYq(val) {
      let url = "/prompt/insertPrompt";
      this.params = val;
      if (!this.params.promptNum) {
        return this.$Message.error("提示单编号不能为空");
      }
      if (!this.params.sendOrgId) {
        return this.$Message.error("发送单位不能为空");
      }
      if (!this.params.sendUserName) {
        return this.$Message.error("接收人不能为空");
      }
      if (!this.params.sendPhone) {
        return this.$Message.error("接收人电话不能为空");
      }
      if (!this.params.msgAbstract) {
        return this.$Message.error("舆情线索不能为空");
      }
      if (!this.params.msgContentUrl) {
        return this.$Message.error("链接不能为空");
      }
      if (!this.params.suggest) {
        return this.$Message.error("处置建议不能为空");
      }
      if (!this.params.promptUserId) {
        return this.$Message.error("单位不能为空");
      }
      if (!this.params.promptOrganId) {
        return this.$Message.error("联系人不能为空");
      }
      if (!this.params.promptUserPhone) {
        return this.$Message.error("联系人电话不能为空");
      }
      let data = {};
      if (
        this.$route.path.indexOf("main/publicOpinionTips") > -1 &&
        this.btnStatus.tipsTurn
      ) {
        data = this.data;
      }
      console.log("ppp",this.params)
      let params = {
        ...data,
        ...this.params,
        classifyId:
          this.datas.scopeArea || this.datas.classifyId
            ? this.getId(this.datas.scopeArea, this.domainList) ||
              this.getId(this.datas.classifyId, this.domainList)
            : null, // 信息对应领域id','拼接
        emotionType: this.datas.msentiment
          ? this.datas.msentiment
          : this.datas.emotionType
          ? this.datas.emotionType
          : 0, // 情感倾向(-1:负.0:中.1:正)
        locationId:
          this.datas.marea || this.datas.locationId
            ? this.getId(this.datas.marea, this.unitList) ||
              this.getId(this.datas.locationId, this.unitList)
            : null, // 信息对应的地域id拼接','拼接
      };
      if (params.isSendUserSms == "1") {
        if (!this.validatePhoneNumber(params.sendUserPhone)) {
          return this.$Message.error("请输入正确的手机号");
        }
      }
      if (params.smsContent.length > 1000 || params.smsContent.length == 0) {
        return this.$Message.error("请输入1000字以内的短信内容");
      }
      if (
        this.$route.path.indexOf("main/publicOpinionTips") > -1 &&
        this.btnStatus.tipsTurn
      ) {
        params.promptMsgId = this.data.promptMsgId;
      }
      this.$http.post(url, params, { emulateJSON: true }).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          if (
            this.$route.path.indexOf("main/publicOpinionTips") > -1 &&
            this.btnStatus.tipsTurn
          ) {
            this.$Message.success("舆情提示单已转办");
          } else {
            this.$Message.success("舆情提示单已下发");
          }

          this.$modal.hide();
          this.datas.status = 1;
          this.cancelItem(this.data.mkey, 2);
          let data = {
            promptNum: params.promptNum,
            smsContent: val.smsContent,
            // "提示单编号：" +
            // params.promptNum +
            // "#舆情线索：" +
            // params.msgAbstract +
            // "#" +
            // params.msgContentUrl +
            // "#处置建议：" +
            // params.suggest +
            // "#【请及时在系统内或本短信回复反馈情况，反馈内容控制在70字以内】",
            sendFlags: params.isSendSms,
            sendFlagPeoples: params.isSendUserSms,
            promptUserPhone: params.sendUserPhone,
          };
          if (params.isSendSms == "1" || params.isSendUserSms == "1") {
            this.handleSends(data);
          }
        } else {
          this.$Message.error(result.message);
        }
      });
    },
    // 舆情提示单生成
    handleStorePrompt(val) {
      let url = "/prompt/storePrompt";
      this.params = val;
      let data = {};
      if (
        this.$route.path.indexOf("main/publicOpinionTips") > -1 &&
        this.btnStatus.tipsTurn
      ) {
        data = this.data;
      }
      let params = {
        ...data,
        ...this.params,
        classifyId:
          this.datas.scopeArea || this.datas.classifyId
            ? this.getId(this.datas.scopeArea, this.domainList) ||
              this.getId(this.datas.classifyId, this.domainList)
            : null, // 信息对应领域id','拼接
        emotionType: this.datas.msentiment
          ? this.datas.msentiment
          : this.datas.emotionType
          ? this.datas.emotionType
          : 0, // 情感倾向(-1:负.0:中.1:正)
        locationId:
          this.datas.marea || this.datas.locationId
            ? this.getId(this.datas.marea, this.unitList) ||
              this.getId(this.datas.locationId, this.unitList)
            : null, // 信息对应的地域id拼接','拼接
      };
      if (
        this.$route.path.indexOf("main/publicOpinionTips") > -1 &&
        this.btnStatus.tipsTurn
      ) {
        params.promptMsgId = this.data.promptMsgId;
      }
      this.$http.post(url, params, { emulateJSON: true }).then((res) => {
        this.$modal.hide();
      });
    },
    showModal(id) {
      if (id == 4) {
        this.$router.push("/main/incident");
      }
    },
    // 转发到群
    sendMessage() {
      const { href } = this.$router.resolve({
        path: "/main/details",
        query: {
          msgKey: this.data.mkey,
          situation: this.data.situation,
          sourcePath: this.$route.path,
          sourceName: this.$route.name,
        },
      });
      let params = {
        id: this.data.id,
      };
      this.$http
        .post("/recommend/sendMessage", params, { emulateJSON: true })
        .then((res) => {
          if (res.body.data == 1) {
            this.$set(this.data, "sendMessage", 1);
            this.$Message.success("转发到群成功");
          } else {
            this.$Message.success("转发到群失败了");
          }
        });
    },
    // 获取详情内容
    getContent(data) {
      let itm = {};
      let params = {
        msgKey: data.msgId,
        situation: data.msgSiution,
      };
      this.$http.get("/search/msgDetail", { params }).then((res) => {
        if (res.body.status === 0) {
          itm = res.body.data ? res.body.data : {};
        }
      });
      return itm;
    },
    httpHeader() {
      return (this.$route.meta && this.$route.meta.moduleName) ||
        this.$route.query.moduleName
        ? {
            sys_log_module: this.$route.query.moduleName
              ? encodeURIComponent(
                  decodeURIComponent(this.$route.query.moduleName)
                )
              : this.$route.meta.moduleName,
          }
        : {};
    },
    // 批量探测
    batchDetection() {
      let params = {
        mkeys: [this.data.mkey],
        detectionFlag: 1,
        isImportant: this.$route.query.sourceName === "涉济推荐" ? 1 : null,
      };
      this.$http
        .post("/message/detection/list", params)
        .then((res) => {
          if (res.body.status === 0) {
            this.$Message.success("探测已启动，稍后刷新页面查看结果");
          } else {
            this.$Message.error(res.body.message);
          }
        })
        .catch((err) => {
          this.$Message.error(err.body.message);
        });
    },
    setProvincOfficeTips(type) {
      this.$emit("setProvincOfficeTips", type);
    },

    // 联动处置
    createJointDisposal() {
      // 记录日志
      if (
        (this.$route.meta && this.$route.meta.moduleName) ||
        this.$route.query.moduleName
      ) {
        const sys_log_module = this.$route.query.moduleName
          ? decodeURIComponent(this.$route.query.moduleName)
          : this.$route.meta.moduleName;
        this.getLog(
          sys_log_module,
          "联动处置/" + this.datas.mtitle,
          this.$route.query.msgKey ? this.$route.query.msgKey : ""
        );
      }

      // 跳转到新建联动处置任务页面，并传递文章信息
      const query = {
        msgKey: this.datas.mkey,
        situation: this.datas.situation,
        mtitle: this.datas.mtitle, // 标题
        mwebsiteName: this.datas.mwebsiteName, // 平台
        murl: this.datas.murl, // 链接
      };

      const { href } = this.$router.resolve({
        path: "/main/jointDisposal/createTask",
        query,
      });

      window.open(href, "_blank");
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    ...mapState(useDomainStore, ["domainList"]), //监控 data 中的数据变化
  },
  // 监控 data 中的数据变化
  watch: {
    data: {
      handler(val) {
        this.datas = val;
      },
      deep: true,
      immediate: true,
    },
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>

<style lang="less" scoped>
.ListControls {
  display: flex;
  flex-wrap: wrap;
  color: #000;
  &.margin {
    .item {
      margin: 0 var(--margin);
    }
  }
  .item {
    background: #e6f7ff;
    border: 1px solid #5585ec;
    border-radius: 4px;
    font-size: 14px;
    line-height: 24px;
    cursor: pointer;
    text-align: center;
    margin: 0 2px;
    height: 24px;
  }

  .item-point {
    position: relative;
    font-size: 14px;
    .point-content {
      display: none;
      position: absolute;
      width: 100px;
      color: #000;
      background: #fff;
      left: -80px;
      top: 20px;
      // border: 1px solid #999999;
      border-radius: 4px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.16);
      z-index: 100;

      .points {
        height: 30px;
        line-height: 30px;
        cursor: pointer;
      }
    }
  }

  .item-point:hover {
    .point-content {
      display: block;
    }
  }

  .points:hover {
    background: #e1f8ff;
  }

  .disabled {
    cursor: not-allowed; //不能点击的效果
    color: #cfd0d3; //置灰的颜色
  }
}

.no-item {
  color: #666666 !important;
  border-color: #a2a2a2 !important;
  background: #ededed !important;
  cursor: default !important;
}

.no-item:hover {
  background: #ededed !important;
}

.content {
  display: flex;
  margin: 10px 0px 20px 0px;

  .content-title {
    width: 80px;
    color: #5585ec;
    font-size: 16px;
    font-family: PingFang SC;
    margin-top: 7px;
    text-align: right;
  }

  .old-text {
    /deep/ .ivu-input {
      height: 200px;
      border: 1px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }
  }

  .old-textNew {
    width: 609px;
    height: 200px;
    border: 1px solid #3b5576;

    /deep/ .ivu-input {
      height: 40px;
      border: 0px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }
  }

  .abstract-text {
    /deep/ .ivu-input {
      height: 100px;
      border: 1px solid #3b5576;
    }
  }

  .content-number {
    /deep/ .ivu-input {
      height: 38px;
      border: 1px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }

    /deep/ .ivu-select-single .ivu-select-selection {
      height: 38px;
      border: 1px solid #3b5576;
      font-family: PingFang SC;
      color: #999999;
      font-size: 14px;
    }
  }
}

.foot {
  text-align: center;
  margin-bottom: 18px;

  .foots {
    width: 80px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #5585ec;
    border-radius: 4px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
  }
}

.inBlock {
  display: inline-block;
}
</style>
