/**
 * 舆论场工具函数
 * 用于处理舆论场代码与名称的转换
 */

import yulunchanMap from '@/assets/json/yulunchang.json'

/**
 * 根据舆论场代码获取舆论场名称
 * @param {string|number} code - 舆论场代码
 * @returns {string} 舆论场名称，如果找不到对应关系则返回原代码
 */
export function getYulunchangName(code) {
  if (!code && code !== 0) {
    return '-'
  }
  
  const codeStr = String(code)
  return yulunchanMap[codeStr] || codeStr
}

/**
 * 根据舆论场名称获取舆论场代码
 * @param {string} name - 舆论场名称
 * @returns {string} 舆论场代码，如果找不到对应关系则返回原名称
 */
export function getYulunchanCode(name) {
  if (!name) {
    return ''
  }
  
  const entry = Object.entries(yulunchanMap).find(([code, mappedName]) => mappedName === name)
  return entry ? entry[0] : name
}

/**
 * 获取所有舆论场映射关系
 * @returns {Object} 舆论场映射对象
 */
export function getAllYulunchanMap() {
  return { ...yulunchanMap }
}

/**
 * 获取所有舆论场名称列表
 * @returns {Array} 舆论场名称数组
 */
export function getYulunchangNameList() {
  return Object.values(yulunchanMap)
}

/**
 * 获取所有舆论场代码列表
 * @returns {Array} 舆论场代码数组
 */
export function getYulunchanCodeList() {
  return Object.keys(yulunchanMap)
}

/**
 * 批量转换舆论场代码为名称
 * @param {Array} codeList - 舆论场代码数组
 * @returns {Array} 舆论场名称数组
 */
export function batchgetYulunchangNames(codeList) {
  if (!Array.isArray(codeList)) {
    return []
  }
  
  return codeList.map(code => getYulunchangName(code))
}

/**
 * 验证舆论场代码是否有效
 * @param {string|number} code - 舆论场代码
 * @returns {boolean} 是否为有效的舆论场代码
 */
export function isValidYulunchanCode(code) {
  if (!code && code !== 0) {
    return false
  }
  
  const codeStr = String(code)
  return yulunchanMap.hasOwnProperty(codeStr)
}

/**
 * 验证舆论场名称是否有效
 * @param {string} name - 舆论场名称
 * @returns {boolean} 是否为有效的舆论场名称
 */
export function isValidYulunchanName(name) {
  if (!name) {
    return false
  }
  
  return Object.values(yulunchanMap).includes(name)
}
