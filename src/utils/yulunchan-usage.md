# 舆论场工具使用说明

## 概述

舆论场工具提供了统一的舆论场代码与名称转换功能，全站可用。

## 文件结构

```
src/
├── assets/json/yulunchang.json    # 舆论场映射关系配置文件
└── utils/yulunchang.js           # 舆论场工具函数
```

## 舆论场映射关系

| 代码 | 名称 |
|------|------|
| 10   | 新浪微博 |
| 20   | 微信公众号 |
| 30   | 媒体网站 |
| 31   | 客户端 |
| 60   | 论坛贴吧 |
| 80   | 小红书 |
| 199  | 短视频 |
| 600  | 今日头条 |

## 使用方法

### 1. 基本导入

```javascript
import { getYulunchangName } from '@/utils/yulunchan'
```

### 2. 在Vue组件中使用

#### 方法一：在methods中使用
```javascript
import { getYulunchangName } from '@/utils/yulunchan'

export default {
  methods: {
    getYulunchangName,
    
    // 其他方法...
  }
}
```

#### 方法二：在模板中直接使用
```vue
<template>
  <div>
    <!-- 显示舆论场名称 -->
    <span>{{ getYulunchangName(item.situation) }}</span>
  </div>
</template>
```

#### 方法三：在计算属性中使用
```javascript
import { getYulunchangName } from '@/utils/yulunchan'

export default {
  computed: {
    situationName() {
      return getYulunchangName(this.situationCode)
    }
  }
}
```

### 3. 在普通JavaScript文件中使用

```javascript
import { getYulunchangName, getYulunchanCode } from '@/utils/yulunchan'

// 代码转名称
const name = getYulunchangName('20') // 返回: "微信公众号"

// 名称转代码
const code = getYulunchanCode('微信公众号') // 返回: "20"
```

## API 参考

### getYulunchangName(code)
根据舆论场代码获取舆论场名称

**参数:**
- `code` (string|number): 舆论场代码

**返回值:**
- `string`: 舆论场名称，如果找不到对应关系则返回原代码，空值返回 "-"

**示例:**
```javascript
getYulunchangName('20')  // "微信公众号"
getYulunchangName(20)    // "微信公众号"
getYulunchangName('999') // "999" (未知代码返回原值)
getYulunchangName('')    // "-" (空值返回 "-")
```

### getYulunchanCode(name)
根据舆论场名称获取舆论场代码

**参数:**
- `name` (string): 舆论场名称

**返回值:**
- `string`: 舆论场代码，如果找不到对应关系则返回原名称

**示例:**
```javascript
getYulunchanCode('微信公众号') // "20"
getYulunchanCode('未知平台')   // "未知平台" (未知名称返回原值)
```

### getAllYulunchanMap()
获取所有舆论场映射关系

**返回值:**
- `Object`: 舆论场映射对象

### getYulunchangNameList()
获取所有舆论场名称列表

**返回值:**
- `Array`: 舆论场名称数组

### getYulunchanCodeList()
获取所有舆论场代码列表

**返回值:**
- `Array`: 舆论场代码数组

### batchgetYulunchangNames(codeList)
批量转换舆论场代码为名称

**参数:**
- `codeList` (Array): 舆论场代码数组

**返回值:**
- `Array`: 舆论场名称数组

### isValidYulunchanCode(code)
验证舆论场代码是否有效

**参数:**
- `code` (string|number): 舆论场代码

**返回值:**
- `boolean`: 是否为有效的舆论场代码

### isValidYulunchanName(name)
验证舆论场名称是否有效

**参数:**
- `name` (string): 舆论场名称

**返回值:**
- `boolean`: 是否为有效的舆论场名称

## 实际应用场景

### 1. 表格显示
```vue
<template>
  <table>
    <tr v-for="item in dataList" :key="item.id">
      <td>{{ getYulunchangName(item.situation) }}</td>
    </tr>
  </table>
</template>
```

### 2. 下拉选择器
```vue
<template>
  <Select v-model="selectedSituation">
    <Option 
      v-for="(name, code) in yulunchanMap" 
      :key="code" 
      :value="code"
    >
      {{ name }}
    </Option>
  </Select>
</template>

<script>
import { getAllYulunchanMap } from '@/utils/yulunchan'

export default {
  data() {
    return {
      selectedSituation: '',
      yulunchanMap: getAllYulunchanMap()
    }
  }
}
</script>
```

### 3. 筛选器
```vue
<template>
  <div>
    <Button 
      v-for="name in yulunchanNames" 
      :key="name"
      @click="filterBySituation(name)"
    >
      {{ name }}
    </Button>
  </div>
</template>

<script>
import { getYulunchangNameList } from '@/utils/yulunchan'

export default {
  data() {
    return {
      yulunchanNames: getYulunchangNameList()
    }
  }
}
</script>
```

## 维护说明

### 添加新的舆论场类型
1. 编辑 `src/assets/json/yulunchang.json` 文件
2. 添加新的代码-名称映射关系
3. 无需修改工具函数，自动支持新增的映射关系

### 修改现有映射关系
1. 直接编辑 `src/assets/json/yulunchang.json` 文件
2. 修改对应的代码-名称映射关系
3. 全站自动生效

## 注意事项

1. **数据类型**: 代码支持字符串和数字类型，内部会自动转换为字符串进行匹配
2. **空值处理**: 空值或undefined会返回 "-"
3. **未知代码**: 未知的代码会返回原代码值
4. **性能**: 映射关系存储在内存中，查询性能优秀
5. **全局可用**: 工具函数可在项目任何地方导入使用
