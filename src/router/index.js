/*
 * @Author: yang<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-23 19:12:27
 * @LastEditors: yangfushan <EMAIL>
 * @LastEditTime: 2025-05-09 11:23:44
 * @FilePath: \jn_jccz_web\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/* jshint esversion: 11 */
import path from "path-browserify";
import Vue from "vue";
import Router from "vue-router";
// import NotFoundComponent from '@/components/common/404';
import VueRouter from "vue-router";

Vue.use(Router);
const originPush = VueRouter.prototype.push;
VueRouter.prototype.push = function (location, resolve, reject) {
  if (resolve && reject) {
    originPush.call(this, location, resolve, reject);
  } else {
    originPush.call(
      this,
      location,
      () => {},
      () => {}
    );
  }
};
/**
 * 判断非登陆页面，后端接口需要去掉登陆验证
 * 前端路由需要加上noRequiresAuth字段
 */

let r = {
  mode: "history",
  routes: [
    // 舆情监测系统主页入口，默认子页可调整redirect
    { path: "/", redirect: "/main" },
    {
      path: "/main",
      component: () => import("@/views/main"),
      redirect: "/main/monitor",
      children: [
        
        // 首页
        // {
        //   path: "/main/home",
        //   name: "首页",
        //   svgIcon: "首页",
        //   component: () => import("@/views/main/home"),
        // },
        {
          path: "/main/AiReport",
          name: "Ai报告",
          // svgIcon: "Ai报告",
          component: () => import("@/views/main/AiReport"),
          redirect: "/main/AiReport/manufacture",
          children: [
            {
              path: "/main/AiReport/manufacture",
              name: "报告制作",
              component: () => import("@/views/main/AiReport/manufacture"),
            },
            {
              path: "/main/AiReport/template",
              name: "报告模板",
              component: () => import("@/views/main/AiReport/template"),
            },
            {
              path: "/main/AiReport/list",
              name: "报告列表",
              component: () => import("@/views/main/AiReport/list"),
            },
          ],
        },

        // 境内监测
        {
          path: "/main/monitor",
          name: "境内监测",
          svgIcon: "涉济监测",
          component: () => import("@/views/main/monitor"),
          redirect: "/main/monitor/recommended",
          children: [
            {
              path: "/main/monitor/SDplatform",
              name: "属地平台",
              svgIcon: "属地平台",
              meta: {
                moduleName: "境内监测/属地平台",
              },
              component: () =>
                import("@/views/main/monitor/SDplatform/index.vue"),
            },
            {
              path: "/main/monitor/recommended",
              name: "涉济推荐",
              svgIcon: "涉济推荐",
              meta: {
                moduleName: "境内监测/涉济推荐-推荐关注",
              },
              component: () =>
                import("@/views/main/monitor/recommended/index.vue"),
            },
            {
              path: "/main/monitor/message",
              name: "专题监测",
              svgIcon: "专题监测",
              meta: {
                moduleName: "境内监测/专题监测",
              },
              component: () => import("@/views/main/monitor/message"),
            },
            {
              path: "/main/monitor/shortVideo",
              name: "短视频监测",
              svgIcon: "短视频监测",
              meta: {
                moduleName: "境内监测/短视频监测",
              },
              //老版本组件路径@/views/main/monitor/scrap-shortVideo
              component: () => import("@/views/main/monitor/shortVideo"),
            },
            {
              path: "/main/monitor/hotList",
              name: "热榜监测",
              svgIcon: "热榜监测",
              meta: {
                moduleName: "境内监测/涉济热榜监测",
              },
              component: () => import("@/views/main/monitor/hotList"),
            },
            // {
            //   path: "/main/monitor/jiNanHotList",
            //   name: "涉济热榜",
            //   svgIcon: "涉济热榜",
            //   meta: {
            //     moduleName: '境内监测/涉济热榜监测'
            //   },
            //   component: () => import("@/views/main/monitor/jiNanHotList"),
            // },
            {
              path: "/main/monitor/comprehensiveSearch/entry",
              name: "精确搜索",
              svgIcon: "精确搜索",
              component: () => import("@/views/main/comprehensiveSearch/entry"),
            },
            {
              path: "/main/monitor/chinaImportanceNews",
              name: "国内热榜",
              svgIcon: "国内热榜",
              meta: {
                moduleName: "境内监测/国内要闻监测",
              },
              component: () =>
                import("@/views/main/monitor/chinaImportanceNews"),
            },
            // {
            //   path: "/main/monitor/chinaHotList",
            //   name: "国内要闻",
            //   svgIcon: "国内要闻",
            //   meta: {
            //     moduleName: '境内监测/国内热榜监测'
            //   },
            //   component: () => import("@/views/main/monitor/chinaHotList"),
            // },
            // 境外监测
            {
              path: "/main/monitor/overseasMonitor",
              name: "境外监测",
              svgIcon: "境外监测",
              meta: {
                moduleName: "涉济监测/境外监测",
              },
              component: () => import("@/views/main/monitor/overseasMonitor"),
            },
            {
              path: "/main/monitor/jiNansubmission",
              name: "涉济报送",
              svgIcon: "涉济报送",
              component: () => import("@/views/main/monitor/jiNansubmission"),
            },
            {
              path: "/main/monitor/riskSubmission",
              name: "风险点报送",
              svgIcon: "风险点报送",
              meta: {
                moduleName: "境内监测/风险点报送",
              },
              component: () => import("@/views/main/monitor/riskSubmission"),
            },
          ],
        },
        // 境外监测
        {
          path: "/main/OffshoreMonitoring",
          name: "境外监测",
          svgIcon: "境外监测",
          component: () => import("@/views/main/OffshoreMonitoring"),
          redirect: "/main/OffshoreMonitoring/recommended",
          children: [
            {
              path: "/main/OffshoreMonitoring/recommended",
              name: "涉济推荐",
              svgIcon: "涉济推荐",
              meta: {
                moduleName: "境外监测/涉济监测",
              },
              component: () =>
                import("@/views/main/OffshoreMonitoring/recommended/index.vue"),
            },
            {
              path: "/main/OffshoreMonitoring/message",
              name: "专题监测",
              svgIcon: "专题监测",
              meta: {
                moduleName: "境外监测/专题监测",
              },
              component: () =>
                import("@/views/main/OffshoreMonitoring/message"),
            },
            {
              path: "/main/OffshoreMonitoring/AccountMessage",
              name: "账号监测",
              svgIcon: "账号监测",
              meta: {
                moduleName: "境外监测/账号监测",
              },
              component: () =>
                import("@/views/main/OffshoreMonitoring/message"),
            },
            {
              path: "/main/OffshoreMonitoring/websiteMessage",
              name: "网站监测",
              svgIcon: "网站监测",
              meta: {
                moduleName: "境外监测/网站监测",
              },
              component: () =>
                import("@/views/main/OffshoreMonitoring/message"),
            },
            // 境外搜索
            {
              path: "/main/OffshoreMonitoring/overseasMonitor",
              name: "境外搜索",
              svgIcon: "境外搜索",
              meta: {
                moduleName: "境外监测/境外搜索",
              },
              component: () => import("@/views/main/monitor/overseasMonitor"),
            },
          ],
        },
        // 涉鲁涉政监测
        // {
        //   path: "/main/shandongMonitor",
        //   name: "涉鲁涉政监测",
        //   svgIcon: "涉鲁涉政监测",
        //   component: () => import("@/views/main/shandongMonitor"),
        //   redirect: "/main/shandongMonitor/zhengInformation",
        //   children: [
        //     {
        //       path: "/main/shandongMonitor/recommended",
        //       name: "涉鲁推荐",
        //       component: () =>
        //         import("@/views/main/monitor/recommended/index1.vue"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/politicalRecommended",
        //       name: "涉政推荐",
        //       component: () =>
        //         import("@/views/main/monitor/recommended/index1.vue"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/message",
        //       name: "信息监测",
        //       svgIcon: "信息监测",
        //       component: () => import("@/views/main/monitor/message"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/shortVideo",
        //       name: "短视频监测",
        //       svgIcon: "短视频监测",
        //       component: () => import("@/views/main/monitor/scrap-shortVideo"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/shanDongHotList",
        //       name: "涉鲁热榜 ",
        //       svgIcon: "涉鲁热榜 ",
        //       component: () => import("@/views/main/monitor/jiNanHotList"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/chinaHotList",
        //       name: "国内热榜",
        //       svgIcon: "国内热榜",
        //       component: () => import("@/views/main/monitor/chinaHotList"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/luInformation",
        //       name: "涉鲁信息库",
        //       svgIcon: "涉鲁信息库",
        //       component: () => import("@/views/main/monitor/luInformation"),
        //     },
        //     {
        //       path: "/main/shandongMonitor/zhengInformation",
        //       name: "涉政信息库",
        //       svgIcon: "涉政信息库",
        //       component: () => import("@/views/main/monitor/zhengInformation"),
        //     },
        //   ],
        // },

        //舆情提示
        {
          path: "/main/publicOpinionTips",
          name: "舆情提示",
          svgIcon: "舆情提示",
          meta: {
            moduleName: "舆情提示/已下发提示单",
          },
          component: () => import("@/views/main/publicOpinionTips"),
        },
        //舆情提示、新建事件
        {
          path: "/main/incident",
          name: "新建事件",
          component: () => import("@/views/main/incident"),
        },
        //舆情提示、提示详情
        {
          path: "/main/tipsDeatil",
          name: "提示详情",
          component: () => import("@/views/main/tipsDeatil"),
        },
        // 风险点报送-详情
        {
          path: "/main/monitor/riskSubmission/detail",
          name: "风险点报送-详情",
          svgIcon: "",
          component: () =>
            import(
              "@/views/main/monitor/riskSubmission/components/details.vue"
            ),
        },
        // 通知管理/通知详情
        {
          path: "/main/notification/detail",
          name: " 通知管理/通知详情",
          svgIcon: "",
          component: () =>
            import("@/views/main/notification/components/details.vue"),
        },
        // 事件分析
        {
          path: "/main/eventAnalysis",
          name: "事件分析",
          svgIcon: "事件分析",
          component: () => import("@/views/main/eventAnalysis"),
          redirect: "/main/eventAnalysis/eventList",
          children: [
            {
              path: "/main/eventAnalysis/eventList",
              name: "事件分析",
              meta: {
                moduleName: "事件分析/事件列表",
              },
              component: () => import("@/views/main/eventAnalysis/eventList"),
            },
            {
              path: "/main/eventAnalysis/eventInfo",
              name: "事件分析",
              component: () => import("@/views/main/eventAnalysis/eventInfo"),
            },
            {
              path: "/main/eventAnalysis/eventDetails",
              name: "事件信息",
              component: () =>
                import("@/views/main/eventAnalysis/eventDetails"),
            },
            {
              path: "/main/eventAnalysis/eventCompare",
              name: "事件对比",
              component: () =>
                import("@/views/main/eventAnalysis/eventCompare"),
            },
          ],
        },
        // 网情要报
        {
          path: "/main/internetReport",
          name: "网情要报",
          svgIcon: "网情要报",
          component: () => import("@/views/main/internetReport"),
          children: [
            {
              path: "/main/internetReport/reportMaterial",
              name: "网情要报素材",
              svgIcon: "网站库",
              meta: {
                moduleName: "网情要报/网情要报素材",
              },
              component: () =>
                import("@/views/main/internetReport/reportMaterial"),
            },
            {
              path: "/main/internetReport/myMaterial",
              name: "个人素材",
              svgIcon: "账号库",
              component: () => import("@/views/main/internetReport/myMaterial"),
            },
          ],
        },

        // 联动处置
        {
          path: "/main/jointDisposal",
          name: "联动处置",
          svgIcon: "联动处置",
          meta: {
            moduleName: "联动处置",
          },
          component: () => import("@/views/main/jointDisposal"),
          redirect: "/main/jointDisposal/taskList",
          children: [
            {
              path: "/main/jointDisposal/taskList",
              name: "联动处置",
              meta: {
                moduleName: "联动处置/任务管理",
              },
              component: () => import("@/views/main/jointDisposal/taskList"),
            },
            // 联动处置-任务详情
            {
              path: "/main/jointDisposal/taskDetail",
              name: "任务详情",
              meta: {
                moduleName: "联动处置/任务详情",
              },
              component: () =>
                import("@/views/main/jointDisposal/taskDetail/index.vue"),
            },
            // 联动处置-新建任务
            {
              path: "/main/jointDisposal/createTask",
              name: "新建联动处置任务",
              meta: {
                moduleName: "联动处置/新建任务",
              },
              component: () =>
                import("@/views/main/jointDisposal/components/createTask"),
            },
            // 联动处置-任务审核
            {
              path: "/main/jointDisposal/taskAudit",
              name: "任务审核",
              meta: {
                moduleName: "联动处置/任务审核",
              },
              component: () =>
                import("@/views/main/jointDisposal/components/taskAudit"),
            },
          ],
        },
        // 档案库  Archive
        {
          path: "/main/Archive",
          name: "全息档案",
          svgIcon: "联系人",
          component: () => import("@/views/main/Archive"),
          redirect: "/main/Archive/overview",
          children: [
            {
              path: "/main/Archive/overview",
              name: "档案概览",
              svgIcon: "",
              meta: {
                moduleName: "全息档案/档案概览",
              },
              component: () => import("@/views/main/Archive/overview"),
            },
            {
              path: "/main/Archive/selfMediaLibrary",
              name: "自媒体库",
              svgIcon: "",
              meta: {
                moduleName: "全息档案/自媒体库",
              },
              component: () => import("@/views/main/Archive/selfMediaLibrary"),
            },
            {
              path: "/main/Archive/mediaLibrary",
              name: "媒体库",
              svgIcon: "",
              meta: {
                moduleName: "全息档案/媒体库",
              },
              component: () => import("@/views/main/Archive/mediaLibrary"),
              redirect: "/main/Archive/mediaLibrary/dataOverview",
              children: [
                {
                  path: "/main/Archive/mediaLibrary/dataOverview",
                  name: "数据概览",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/媒体库/数据概览",
                  },
                  component: () =>
                    import("@/views/main/Archive/mediaLibrary/dataOverview"),
                },
                {
                  path: "/main/Archive/mediaLibrary/topicDetails",
                  name: "主体详情",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/媒体库/主体详情",
                  },
                  component: () =>
                    import("@/views/main/Archive/mediaLibrary/topicDetails"),
                },
                {
                  path: "/main/Archive/mediaLibrary/mediaListAll",
                  name: "传统媒体清单",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/媒体库/传统媒体",
                  },
                  component: () =>
                    import("@/views/main/Archive/mediaLibrary/mediaListAll"),
                },
                {
                  path: "/main/Archive/mediaLibrary/mediaList",
                  name: "传统媒体清单",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/媒体库/传统媒体清单",
                  },
                  component: () =>
                    import("@/views/main/Archive/mediaLibrary/mediaList"),
                },
                {
                  path: "/main/Archive/mediaLibrary/newMediaList",
                  name: "新媒体清单",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/媒体库/新媒体清单",
                  },
                  component: () =>
                    import("@/views/main/Archive/mediaLibrary/newMediaList"),
                },
                {
                  path: "/main/Archive/mediaLibrary/businessPlatform",
                  name: "商业平台",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/媒体库/商业平台",
                  },
                  component: () =>
                    import(
                      "@/views/main/Archive/mediaLibrary/businessPlatform"
                    ),
                },
              ],
            },

            {
              path: "/main/Archive/mediaLibrary/topicDetails/detail",
              name: "主体详情查看",
              svgIcon: "",
              show: false,
              meta: {
                moduleName: "全息档案/媒体库/主体详情/详情查看",
              },
              component: () =>
                import("@/views/main/Archive/mediaLibrary/topicDetails/detail"),
            },
            // 商业平台库模块
            {
              path: "/main/Archive/businessPlatformLibrary",
              name: "商业平台库",
              svgIcon: "",
              meta: {
                moduleName: "全息档案/商业平台库",
              },
              component: () => import("@/views/main/Archive/businessPlatformLibrary"),
              redirect: "/main/Archive/businessPlatformLibrary/topicDetails",
              children: [
                {
                  path: "/main/Archive/businessPlatformLibrary/topicDetails",
                  name: "主体详情",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/商业平台库/主体详情",
                  },
                  component: () =>
                    import("@/views/main/Archive/businessPlatformLibrary/topicDetails"),
                },
                {
                  path: "/main/Archive/businessPlatformLibrary/platformList",
                  name: "商业平台清单",
                  svgIcon: "",
                  meta: {
                    moduleName: "全息档案/商业平台库/商业平台清单",
                  },
                  component: () =>
                    import("@/views/main/Archive/businessPlatformLibrary/platformList"),
                },
              ],
            },
            // 商业平台库详情页
            {
              path: "/main/Archive/businessPlatformLibrary/topicDetails/detail",
              name: "商业平台库主体详情查看",
              svgIcon: "",
              show: false,
              meta: {
                moduleName: "全息档案/商业平台库/主体详情/详情查看",
              },
              component: () =>
                import("@/views/main/Archive/businessPlatformLibrary/topicDetails/detail"),
            },
            {
              path: "/main/Archive/TerritorialWebsite",
              name: "属地网站库",
              svgIcon: "",
              meta: {
                moduleName: "全息档案/属地网站库",
              },
              component: () =>
                import("@/views/main/Archive/TerritorialWebsite"),
            },
          ],
        },
        // 专题数据
        {
          path: "/main/subjectDataBase",
          name: "专题数据",
          svgIcon: "数据库",
          component: () => import("@/views/main/subjectDataBase"),
          children: [
            // 原综合搜索
              {
                path: "/main/comprehensiveSearch",
                name: "文本信息库",
                svgIcon: "综合搜索",
                component: () => import("@/views/main/comprehensiveSearch"),
                redirect: "/main/comprehensiveSearch/searchEntry",
                children: [
                  {
                    path: "/main/comprehensiveSearch/searchEntry",
                    name: "综合检索",
                    svgIcon: "综合检索",
                    meta: {
                      moduleName: "精确搜索/搜索首页",
                    },
                    component: () => import("@/views/main/comprehensiveSearch/entry"),
                  },
                  {
                    path: "/main/comprehensiveSearch/searchResults",
                    name: "搜索结果",
                    svgIcon: "搜索结果",
                    meta: {
                      moduleName: "精确搜索/搜索结果页",
                    },
                    component: () =>
                      import("@/views/main/comprehensiveSearch/searchResults"),
                  },
                ],
              },
            {
              path: "/main/subjectDataBase/pictureDataBase",
              name: "图片信息库",
              svgIcon: "",
              meta: {
                moduleName: "专题数据/图片信息库",
              },
              component: () =>
                import("@/views/main/subjectDataBase/pictureDataBase"),
            },
            {
              path: "/main/subjectDataBase/videoDataBase",
              name: "视频信息库",
              svgIcon: "",
              meta: {
                moduleName: "专题数据/视频信息库",
              },
              component: () => import("@/views/main/monitor/shortVideo"),
            },
            {
              path: "/main/subjectDataBase/evidenceDataBase",
              name: "取证信息库",
              svgIcon: "",
              meta: {
                moduleName: "专题数据/取证信息库",
              },
              component: () =>
                import("@/views/main/subjectDataBase/evidenceDataBase"),
            },
            // {
            //   path: "/main/subjectDataBase/eventDataBase",
            //   name: "事件信息库",
            //   svgIcon: "",
            //   meta: {
            //     moduleName: "专题数据/事件信息库",
            //   },
            //   component: () =>
            //     import("@/views/main/subjectDataBase/eventDataBase"),
            // },
            {
              path: "/main/subjectDataBase/deleteMsgDataBase",
              name: "处置信息库",
              svgIcon: "",
              meta: {
                moduleName: "专题数据/处置信息库",
              },
              component: () =>
                import("@/views/main/subjectDataBase/deleteMsgDataBase"),
            },
            // {
            //   path: "/main/subjectDataBase/promptDataBase",
            //   name: "提示单库",
            //   svgIcon: "",
            //   meta: {
            //     moduleName: "专题数据/提示单库",
            //   },
            //   component: () =>
            //     import("@/views/main/subjectDataBase/promptDataBase"),
            // },
            // {
            //   path: "/main/subjectDataBase/riskDataBase",
            //   name: "风险点库",
            //   svgIcon: "",
            //   meta: {
            //     moduleName: "专题数据/风险点库",
            //   },
            //   component: () =>
            //     import("@/views/main/subjectDataBase/riskDataBase"),
            // },
          ],
        },
        //案例库
        {
          path: "/main/caseLibrary",
          name: "案例库",
          svgIcon: "标签",
          component: () => import("@/views/main/caseLibrary/index.vue"),
          meta: { title: "案例库" }
        },
        // 学习园地
        {
          path: "/main/study",
          name: "学习园地",
          svgIcon: "学习园地",
          component: () => import("@/views/main/study"),
        },
        // 通知管理
        {
          path: "/main/notification",
          name: "通知管理",
          svgIcon: "通知管理",
          component: () => import("@/views/main/notification"),
        },
        // 详情页
        {
          path: "/main/details",
          name: "详情页",
          component: () => import("@/views/main/details"),
        },
        {
          path: "/main/study/detail",
          name: "学习园地详情",
          component: () => import("@/views/main/study/detail"),
        },
        {
          path: "/main/study/messageList",
          name: "学习园地详情",
          component: () => import("@/views/main/study/messageList"),
        },
        // 账号详情页
        {
          path: "/main/accountDetails",
          name: "账号详情页",
          component: () => import("@/views/main/accountDetails"),
        },
        
        // 评价考核
        {
          path: "/main/evaluationExamination",
          name: "评价考核",
          svgIcon: "评价考核",
          component: () => import("@/views/main/evaluationExamination"),
          children: [
            {
              path: "/main/evaluationExamination/qxAssessEvaluate",
              name: "区县考核评价",
              svgIcon: "",
              meta: {
                moduleName: "评价考核/区县评价考核",
              },
              component: () =>
                import("@/views/main/evaluationExamination/qxAssess"),
            },
            {
              path: "/main/evaluationExamination/szUnitEvaluate",
              name: "市直单位评价",
              svgIcon: "",
              meta: {
                moduleName: "评价考核/市直单位评价考核",
              },
              component: () =>
                import("@/views/main/evaluationExamination/szUnit"),
            },
            {
              path: "/main/evaluationExamination/fwUnitEvaluate",
              name: "服务单位考核评价",
              svgIcon: "",
              meta: {
                moduleName: "评价考核/服务单位评价考核",
              },
              component: () =>
                import("@/views/main/evaluationExamination/fwUnit"),
            },
          ],
        },
        //配置管理
        {
          path: "/main/configMgmt",
          name: "配置管理",
          svgIcon: "配置管理",
          component: () => import("@/views/main/configMgmt"),
          redirect: "/main/configMgmt/aiConfig",
          children: [
            {
              path: "/main/configMgmt/aiConfig",
              name: "大模型配置",
              svgIcon: "",
              meta: {
                moduleName: "配置管理/大模型配置",
              },
              component: () => import("@/views/main/configMgmt/aiConfig"),
            },
            {
              path: "/main/configMgmt/domainConfig",
              name: "领域配置",
              svgIcon: "",
              meta: {
                moduleName: "配置管理/领域配置",
              },
              component: () => import("@/views/main/configMgmt/domainConfig"),
            },
            {
              path: "/main/configMgmt/entityConfig",
              name: "实体配置",
              svgIcon: "",
              component: () => import("@/views/main/configMgmt/entityConfig"),
            },
            {
              path: "/main/configMgmt/informConfig",
              name: "通知类型配置",
              svgIcon: "",
              meta: {
                moduleName: "配置管理/通知类型配置",
              },
              component: () => import("@/views/main/configMgmt/informConfig"),
            },
            {
              path: "/main/configMgmt/eventQuota",
              name: "区县配置",
              svgIcon: "",
              meta: {
                moduleName: "配置管理/区县配置",
              },
              component: () => import("@/views/main/configMgmt/eventQuota"),
            },
            {
              path: "/main/configMgmt/domainClass",
              name: "市直单位领域分类",
              svgIcon: "",
              component: () => import("@/views/main/configMgmt/domainClass"),
            },
            {
              path: "/main/configMgmt/jinanRecommended",
              name: "境外涉济推荐配置",
              svgIcon: "",
              component: () =>
                import("@/views/main/configMgmt/jinanRecommended"),
            },
            // {
            //   path: "/main/configMgmt/eventAnalysis",
            //   name: "事件分析配置",
            //   svgIcon: "",
            //   component: () =>
            //     import("@/views/main/configMgmt/eventAnalysis"),
            // },
            {
              path: "/main/configMgmt/mediaConfig",
              name: "事件分析配置",
              svgIcon: "",
              component: () => import("@/views/main/configMgmt/mediaConfig"),
              redirect: "/main/configMgmt/mediaConfig/mediaConfig",
              children: [
                {
                  path: "/main/configMgmt/mediaConfig/mediaConfig",
                  name: "媒体平台配置",
                  svgIcon: "",
                  meta: {
                    moduleName: "配置管理/事件分析配置/媒体平台配置",
                  },
                  component: () => import("@/views/main/configMgmt/mediaConfig/mediaConfig"),
                },
                {
                  path: "/main/configMgmt/mediaConfig/businessPlatformConfig",
                  name: "商业平台配置",
                  svgIcon: "",
                  meta: {
                    moduleName: "配置管理/事件分析配置/商业平台配置",
                  },
                  component: () => import("@/views/main/configMgmt/mediaConfig/businessPlatformConfig/topicDetails"),
                },
              ],
            },
          ],
        },
        //个人中心
        {
          path: "/main/PersonalCenter",
          name: "个人中心",
          svgIcon: "联系人",
          component: () => import("@/views/main/PersonalCenter"),
          meta:{title:"个人中心"}
        },
        // 知识管理
        // {
        //   path: "/main/knowledgeManagement",
        //   name: "知识管理",
        //   svgIcon: "知识管理",
        //   component: () => import("@/views/main/knowledgeManagement"),
        //   children: [
        //     // {
        //     //   path: "/main/knowledgeManagement/website",
        //     //   name: "网站库",
        //     //   svgIcon: "网站库",
        //     //   meta: {
        //     //     moduleName: "知识管理/网站库",
        //     //   },
        //     //   component: () =>
        //     //     import("@/views/main/knowledgeManagement/website"),
        //     // },
        //     // {
        //     //   path: "/main/knowledgeManagement/account",
        //     //   name: "账号库",
        //     //   svgIcon: "账号库",
        //     //   meta: {
        //     //     moduleName: "知识管理/账号库",
        //     //   },
        //     //   component: () =>
        //     //     import("@/views/main/knowledgeManagement/account"),
        //     // },
        //     {
        //       path: "/main/knowledgeManagement/profile",
        //       name: "资料库",
        //       svgIcon: "资料库",
        //       component: () =>
        //         import("@/views/main/knowledgeManagement/profile"),
        //     },
        //     {
        //       path: "/main/knowledgeManagement/searchEntry",
        //       name: "知识检索",
        //       svgIcon: "知识检索",
        //       meta: {
        //         moduleName: "知识管理/知识检索",
        //       },
        //       component: () =>
        //         import("@/views/main/knowledgeManagement/searchEntry"),
        //     },
        //     {
        //       path: "/main/knowledgeManagement/searchResults",
        //       name: "知识检索",
        //       svgIcon: "知识检索",
        //       show: false,
        //       component: () =>
        //         import("@/views/main/knowledgeManagement/searchResults"),
        //     },
        //     {
        //       path: "/main/knowledgeManagement/doubleCase",
        //       name: "双榜案例",
        //       svgIcon: "双榜案例",
        //       meta: {
        //         moduleName: "知识管理/双榜案例",
        //       },
        //       component: () =>
        //         import("@/views/main/knowledgeManagement/doubleCase"),
        //     },
        //     {
        //       path: "/main/knowledgeManagement/adviceFromOthers",
        //       name: "他山之石",
        //       svgIcon: "他山之石",
        //       meta: {
        //         moduleName: "知识管理/他山之石",
        //       },
        //       component: () =>
        //         import("@/views/main/knowledgeManagement/adviceFromOthers"),
        //     },
        //     {
        //       path: "/main/knowledgeManagement/collectionRange",
        //       name: "采集监测范围",
        //       svgIcon: "采集监测范围",
        //       meta: {
        //         moduleName: "知识管理/采集监测范围",
        //       },
        //       component: () =>
        //         import("@/views/main/knowledgeManagement/collectionRange"),
        //     },
        //   ],
        // },
        
        {
          path: "/main/archiveDetail",
          name: "全息档案详情页",
          component: () => import("@/views/main/Archive/archiveDetail"),
        },
        {
          path: "/main/mediaLibraryDetail",
          name: "全息档案详情页",
          component: () => import("@/views/main/Archive/mediaLibraryDetail"),
        },
        {
          path: "/main/accountInfoAuditView",
          name: "账号审核",
          component: () => import("@/views/main/accountInfoAuditView"),
        },
        
        
        
        {
          path: '/main/caseLibraryDeatil',
          name: 'caseLibraryDeatil',
          component: () => import('@/views/main/caseLibrary/caseLibraryDeatil.vue'),
          meta: { title: '新建档案', icon: 'icon-dangan' }
        },
        {
          path: '/main/caseLibraryDetailView',
          name: 'CaseLibraryDetailView',
          component: () => import('@/views/main/caseLibrary/caseLibraryDetailView.vue')
        },
        
        // 学习园地信息详情

        
        {
          path: "/main/configMgmt/mediaConfig/detail",
          name: "详情",
          svgIcon: "",
          component: () => import("@/views/main/configMgmt/mediaConfig/detail"),
        },
        {
              path: "/main/configMgmt/mediaConfig/businessPlatformConfig/detail",
              name: "配置商业平台清单",
              svgIcon: "",
              meta: {
                moduleName: "配置管理/商业平台配置/配置商业平台清单",
              },
              component: () =>
                import("@/views/main/configMgmt/mediaConfig/businessPlatformConfig/topicDetails/detail"),
            },
        // 新建案例及案例详情
        {
          path: "/main/caseDeatil",
          name: "新建案例",
          svgIcon: "",
          component: () =>
            import(
              "@/views/main/knowledgeManagement/doubleCase/components/deatil.vue"
            ),
        },
        {
          path: "/main/monitorInfo",
          name: "新建监测",
          svgIcon: "",
          component: () => import("@/views/main/monitorInfo"),
        },
        {
          path: "/main/OffshoreMonitoring/monitorInfo",
          name: "监测信息",
          svgIcon: "",
          component: () =>
            import("@/views/main/OffshoreMonitoring/monitorInfo"),
        },
        // 舆情日历
        {
          path: "/main/yqCalendar",
          name: "舆情日历",
          svgIcon: "",
          meta: {
            moduleName: "工具栏/舆情日历",
          },
          component: () => import("@/views/main/yqCalendar"),
        },
        // 值班日志
        {
          path: "/main/dutylog",
          name: "值班日志",
          svgIcon: "",
          meta: {
            moduleName: "工具栏/值班日志",
          },
          component: () => import("@/views/main/dutyLog"),
        }, // 值班日志详情
        {
          path: "/main/dutyDeatil",
          name: "日志详情",
          svgIcon: "",
          component: () => import("@/views/main/dutyLog/components/deatil.vue"),
        },
        //  值班表
        {
          path: "/main/dutyTable",
          name: "值班表",
          svgIcon: "",
          meta: {
            moduleName: "工具栏/值班表",
          },
          component: () => import("@/views/main/dutyTable"),
        },
        {
          path: '/caseLibrary',
          name: 'caseLibrary',
          component: () => import('@/views/main/caseLibrary/index.vue'),
          meta: { title: '案例库', icon: 'icon-anlikuku' }
        },
      ],
    },
    {
      path: "/login",
      name: "登录页",
      svgIcon: "登录页",
      component: () => import("@/views/login"),
    },
    {
      path: "/importantevent",
      name: "重大事件分析页大屏",
      svgIcon: "大屏",
      component: () => import("@/views/analysisJudgment"),
    },
    {
      path: "/publicOpinionOverview",
      name: "publicOpinionOverview",
      component: () =>
        import("@/views/publicOpinionOverview/publicOpinionOverview.vue"),
      meta: { title: "涉济舆情总览" },
    },
    {
      path: "/publicOpinionOverviewNew",
      name: "publicOpinionOverviewNew",
      component: () =>
        import("@/views/publicOpinionOverviewNew/publicOpinionOverviewNew.vue"),
      meta: {
        title: "涉济舆情总览",
        noLoadingBar: true  // 禁用加载进度条
      },
    },
  ],
};
export default new Router(r);
