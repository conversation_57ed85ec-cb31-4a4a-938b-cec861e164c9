{"name": "jn_jccz_web", "group": "trs-jn", "version": "1.2.269", "description": "", "author": "slzs", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "dev403": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js --403", "build": "node build/build.js", "test": "node build/build.js --extract=false", "push": "node build/push.js", "push403": "npm run push 403", "dll": "webpack --config build/webpack.dll.config.js"}, "dependencies": {"@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "angular-expressions": "^1.2.1", "axios": "^1.6.7", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "docxtemplater": "^3.48.0", "docxtemplater-image-module-free": "^1.1.1", "echarts": "4.9", "echarts-wordcloud": "^1.1.3", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "html2canvas": "^1.4.1", "iview": "^3.5.0", "jquery": "^3.7.1", "js-file-download": "^0.4.12", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "markdown-it": "^14.1.0", "moment": "^2.22.2", "muuri": "^0.4.1", "path-browserify": "^1.0.1", "pdfjs-dist": "^4.10.38", "pinia": "^2.1.7", "pizzip": "^3.1.7", "qrcode": "^1.5.4", "quill": "^2.0.3", "resize-detector": "^0.3.0", "sortablejs": "^1.15.1", "svg-sprite-loader": "^6.0.11", "throttle-debounce": "^5.0.0", "trs-clamp": "^1.0.0", "trs-tool-highlight": "^1.0.6", "trs-tool-matcher": "^1.0.10", "uglifyjs-webpack-plugin": "^2.2.0", "video.js": "^8.6.1", "vue": "2", "vue-clipboard2": "^0.3.0", "vue-cookies": "^1.7.4", "vue-count-to": "^1.0.13", "vue-infinite-scroll": "^2.0.2", "vue-quill-editor": "^3.0.6", "vue-resource": "^1.5.3", "vue-router": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/preset-env": "^7.24.4", "autoprefixer": "^7.1.2", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "8.2", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.5.4", "css-loader": "^1.0.1", "file-loader": "^2.0.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^3.2.0", "less": "^2.7.2", "less-loader": "^4.0.4", "mini-css-extract-plugin": "^0.4.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^5.0.1", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.1.6", "postcss-px-to-viewport": "^1.1.1", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "scp2": "^0.5.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "ssh2": "^1.15.0", "url-loader": "^1.1.2", "vue-demi": "^0.14.10", "vue-loader": "^15.10.0", "vue-style-loader": "^4.1.2", "webpack": "^4.23.1", "webpack-bundle-analyzer": "^3.0.3", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.10", "webpack-merge": "^4.1.4"}, "engines": {"node": ">=10.13.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"]}